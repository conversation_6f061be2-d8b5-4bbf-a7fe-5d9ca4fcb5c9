@charset "UFT-8";
html,
body {
    margin: 0;
    padding: 0;
    width: 100%;
    overflow-x: hidden;
    /* Prevents horizontal scroll */
}
#container {
    width: 100%;
    max-width: 100%;
    /* Ensures no extra spacing */
    margin: 0;
    /* Remove default margins */
    padding: 0;
    /* Remove default padding */
    grid-gap: 0;
    /* Remove grid gaps if causing issues */
}
#container {
    display: grid;
    grid-template-rows: 80px 60vh 1fr auto;
    /*    grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr;*/
    /*    grid-template-columns: repeat(12, 1fr);*/
    grid-template-columns: repeat(12, minmax(0, 1fr));
    /*    grid-template-rows: 1fr 1fr 1fr 1fr 1fr;*/
    grid-template-rows: auto;
    grid-gap: 15px;
    background-color: #363636;
    box-sizing: border-box;
}
body {
    background-color: #363636;
    background: linear-gradient(0deg, #1D1D1D, #363636);
}

/* Navigation */

.logo img {
    width: 200px;
    margin-left: 30px;
    z-index: 12;
}
.logo {
    grid-column: 1 / 3;
    grid-row: 1;
    z-index: 10;
    margin-top: 25px;
    z-index: 12;
}
.logowhite img {
    width: 280px;
    z-index: 12;
}
.logowhite {
    grid-column: 1 / 3;
    grid-row: 1;
    margin-top: 5px;
    margin-left: -10px;
    z-index: 12;
}
.profile img {
    width: 60px;
}
.profile {
    grid-column: 11 / 13;
    grid-row: 1;
    z-index: 10;
    justify-self: end;
    margin-right: 40px;
    margin-top: 20px;
}
header {
    grid-row: 1;
    grid-column: 1/13;
    margin-top: 70px;
    z-index: 10;
    margin-right: 25px;
}
nav ul {
    font-family: "Allerta Stencil", sans-serif;
    display: flex;
    justify-content: flex-end;
    grid-gap: 20px;
}
nav ul li {
    margin: 0 30px 0 0;
    display: inline;
}
nav ul li a {
    text-decoration: none;
    color: #FCB11A;
    font-weight: bold;
    font-size: 16px;
}
nav a:hover {
    background-color: #b17600;
    border: 2px solid #b17600;
    color: white;
}

/*
nav a.current {
    background-color: #99AACC;
    border: 2px solid #CCDDEE;
    color: white;
    cursor: default;
}
*/

#nav-trigger {
    display: none;
}
label[for="nav-trigger"] {
    display: none;
}
.down {
    width: 15px;
    margin-bottom: -2px;
}

/*ForgotPassword Page*/

.loginbacka {
    width: 900px;
    grid-row: 2;
    z-index: 1;
    grid-column: 8;
    margin-top: -210px;
    margin-left: 40px;
}
.loginbackb {
    grid-row: 3;
    grid-column: 1;
    width: 1000px;
    margin-left: -200px;
    margin-top: -500px;
}
.forgot-back {
    z-index: 3;
    width: 30px;
    grid-column: 2/4;
    grid-row: 2;
    margin-top: 22px;
    margin-left: 35px;
    cursor: pointer;
}
.forgot-p-a {
    color: white;
    font-family: "Archivo Black", sans-serif;
    font-size: 4rem;
    z-index: 2;
    grid-column: 3/11;
    grid-row: 2;
    line-height: 1.5;
    margin-top: -45px;
}
.forgot-p-b {
    grid-column: 3/7;
    grid-row: 3;
    margin-top: -410px;
    z-index: 2;
    font-size: 1.4rem;
    line-height: 0.7;
}
.forgotp2-2 {
    display: flex;
}
.forgotp2-1 {
    color: white;
}
.forgotp2-2-1 {
    color: white;
}
.forgotp2-2-2 {
    color: #FCB11A;
    margin-left: 10px;
    cursor: pointer;
}
.logininput-box {
    position: relative;
    margin-bottom: 55px;
    width: 85%;
}
.logininput-box input {
    width: 131%;
    padding: 12px 0 12px 0px;
    background: transparent;
    border: none;
    border-bottom: 1px solid white;
    color: white;
    font-size: 16px;
    outline: none;
    transition: all 0.3s ease;
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
}
.logininput-box input::placeholder {
    color: rgba(255, 255, 255, 0.7);
    font-weight: 300;
}
.logininput-box input:focus {
    border-bottom-width: 2px;
    border-bottom-color: #ffffff;
}
.codepart {
    grid-column: 3/6;
    z-index: 2;
    grid-row: 4;
    margin-top: -960px;
}
.show-password {
    margin-top: 15px;
    display: flex;
    align-items: center;
}
.show-password input[type="checkbox"] {
    width: 40px;
    height: 30px;
    margin-right: 10px;
    margin-left: -5px;
}
.show-password label {
    color: white;
    font-family: "Shippori Antique", sans-serif;
    font-size: 1.1rem;
    cursor: pointer;
}
.forgotpasswordbtna {
    grid-row: 5;
    margin-top: -600px;
    grid-column: 5/7;
    z-index: 2;
    width: 65%;
    height: 80px;
    border-radius: 25px;
    border: none;
    background-color: #FCB11A;
    color: white;
    font-size: 1.6rem;
    font-family: "Shippori Antique", sans-serif;
    cursor: pointer;
}
.forgotpasswordbtnb {
    cursor: pointer;
    grid-row: 5;
    margin-top: -600px;
    grid-column: 3/5;
    z-index: 2;
    width: 65%;
    height: 80px;
    border-radius: 25px;
    border: none;
    background-color: #A9A9A9;
    color: white;
    font-size: 1.6rem;
    font-family: "Shippori Antique", sans-serif;
}
.forgotpasswordbtna a,
.forgotpasswordbtnb a {
    text-decoration: none;
    color: white;
}

/* Footer */

footer {
    font-family: "Shippori Antique", sans-serif;
    background-color: #363636;
    color: #fff;
    text-align: center;
    padding: 20px 0;
    grid-column: 1/13;
    width: 100%;
    display: flex;
    flex-direction: column;
    background: linear-gradient(0deg, #1D1D1D, #363636);
}
.contact h3 {
    margin-bottom: 25px;
    font-size: 1.4rem;
    font-family: "Archivo Black", sans-serif;
    color: #FCB11A;
}
.contact address {
    font-style: normal;
    line-height: 2.5;
}
.footersocial {
    display: flex;
    justify-content: center;
    grid-gap: 125px;
    margin-top: 55px;
    margin-bottom: 40px;
}
.footerins,
.footerfacebook,
.footerred {
    width: 55px;
    height: auto;
}
footer a {
    text-decoration: none;
    color: #fff;
    cursor: pointer;
}

/* below 600px typically tablet*/

@media only screen and (max-width: 600px) {
    .sm1 {
        grid-column-end: span 12;
    }
    .sm2 {
        grid-column-end: span 6;
    }
    .sm3 {
        grid-column-end: span 4;
    }
    .sm4 {
        grid-column-end: span 3;
    }
    .sm6 {
        grid-column-end: span 2;
    }
    .sm12 {
        grid-column-end: span 1;
    }
}

/* above 601px typically tablet*/

@media only screen and (min-width: 601px) {
    .md1 {
        grid-column-end: span 12;
    }
    .md2 {
        grid-column-end: span 6;
    }
    .md3 {
        grid-column-end: span 4;
    }
    .md4 {
        grid-column-end: span 3;
    }
    .md6 {
        grid-column-end: span 2;
    }
    .md12 {
        grid-column-end: span 1;
    }
    .twothird {
        grid-column-end: span 8;
    }
}

/* above 1025px typically desktop*/

@media only screen and (min-width: 1025px) {
    .col1 {
        grid-column-end: span 12;
    }
    .col2 {
        grid-column-end: span 6;
    }
    .col3 {
        grid-column-end: span 4;
    }
    .col4 {
        grid-column-end: span 3;
    }
    .col6 {
        grid-column-end: span 2;
    }
    .col12 {
        grid-column-end: span 1;
    }
    .twothird {
        grid-column-end: span 8;
    }
}
