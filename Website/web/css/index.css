@charset "UFT-8";
html,
body {
    margin: 0;
    padding: 0;
    width: 100%;
    overflow-x: hidden;
    /* Prevents horizontal scroll */
}
#container {
    width: 100%;
    max-width: 100%;
    /* Ensures no extra spacing */
    margin: 0;
    /* Remove default margins */
    padding: 0;
    /* Remove default padding */
    grid-gap: 0;
    /* Remove grid gaps if causing issues */
}
#container {
    display: grid;
    grid-template-rows: 80px 60vh 1fr auto;
    /*    grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr;*/
    /*    grid-template-columns: repeat(12, 1fr);*/
    grid-template-columns: repeat(12, minmax(0, 1fr));
    /*    grid-template-rows: 1fr 1fr 1fr 1fr 1fr;*/
    grid-template-rows: auto;
    grid-gap: 15px;
    background-color: #363636;
    box-sizing: border-box;
}
body {
    background-color: #363636;
    background: linear-gradient(0deg, #1D1D1D, #363636);
}

/* below 600px typically tablet*/

@media only screen and (max-width: 600px) {
    .sm1 {
        grid-column-end: span 12;
    }
    .sm2 {
        grid-column-end: span 6;
    }
    .sm3 {
        grid-column-end: span 4;
    }
    .sm4 {
        grid-column-end: span 3;
    }
    .sm6 {
        grid-column-end: span 2;
    }
    .sm12 {
        grid-column-end: span 1;
    }
    .logo img {
        width: 100px;
        margin-left: 15px;
        z-index: 12;
    }
    .logo {
        grid-column: 1 / 3;
        grid-row: 1;
        z-index: 10;
        margin-top: 25px;
        z-index: 12;
    }
    .logowhite img {
        width: 280px;
        z-index: 12;
    }
    .logowhite {
        grid-column: 1 / 3;
        grid-row: 1;
        margin-top: 5px;
        margin-left: -10px;
        z-index: 12;
    }
    .profile img {
        width: 35px;
    }
    .profile {
        grid-column: 9/ 13;
        grid-row: 1;
        z-index: 10;
        margin-right: 40px;
        margin-top: 20px;
    }
    header {
        grid-row: 1;
        grid-column: 1/13;
        margin-top: 70px;
        z-index: 10;
        margin-right: 25px;
    }
    /* Burger Navigation */
    #nav-trigger {
        display: none;
    }
    label[for="nav-trigger"] {
        display: block;
        position: fixed;
        right: 15px;
        top: 20px;
        z-index: 200;
        cursor: pointer;
        font-family: "Allerta Stencil", sans-serif;
        color: #FCB11A;
        font-size: 16px;
    }
    .navigation {
        display: flex;
        flex-direction: column;
        position: fixed;
        background: #656464;
        transition: transform 0.4s ease-in-out;
        z-index: 150;
        box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
    }
    .nav-trigger:checked ~ .navigation {
        transform: translateX(-200px);
    }
    nav ul {
        list-style-type: none;
        padding: 60px 0 0;
        margin: 0;
        width: 150px;
        height: 100vh;
        position: fixed;
        top: 0;
        right: -200px;
        background: #fff;
        z-index: 150;
        box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
    }
    .navigation li {
        width: 100%;
        z-index: 150;
    }
    .navigation a {
        display: block;
        width: 100%;
        padding: 12px 20px;
        color: white;
        text-decoration: none;
        font-family: "Allerta Stencil", sans-serif;
    }
    .navigation a:hover {
        background: #FCB11A;
        color: #fff;
    }
    .down {
        display: none;
    }
    .nav-trigger:checked ~ .overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 150;
    }
    /*Home Page*/
    .home-background-container {
        grid-column: 1 / 13;
        grid-row: 1;
        height: 40vh;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        margin-bottom: 30px;
    }
    .home-background {
        position: absolute;
        top: 0;
        width: 100%;
        height: 300px;
        background-image: url('../img/vexiq_basebot.jpg');
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center center;
        z-index: 1;
        filter: brightness(50%);
        /*    filter:blur(12px);*/
    }
    .title-image-container {
        grid-column: 6;
        grid-row: 1;
        align-items: center;
        position: relative;
        z-index: 10;
    }
    .title-logo {
        width: 80%;
    }
    .team-three {
        width: 200px;
        grid-row: 6;
        grid-column: 2;
        margin-left: 45px;
        z-index: 10;
        margin-top: -20px;
    }
    .team-three-back {
        width: 15rem;
        height: 15rem;
        grid-row: 6;
        margin-left: -8px;
        margin-top: -20px;
        grid-column: 3/8;
        z-index: 1;
    }
    .home-t-a {
        grid-row: 5;
        grid-column: 3/4;
        margin-top: -35px;
        font-size: 3rem;
        font-family: "Archivo Black", sans-serif;
        color: #FCB11A;
        z-index: 10;
    }
    .home-p-a {
        grid-row: 6;
        grid-column: 3/11;
        margin-top: 110px;
        font-size: 0.8rem;
        line-height: 2.5;
        z-index: 10;
        color: white;
        font-family: "Shippori Antique", sans-serif;
    }
    .home-t-b {
        grid-row: 7;
        grid-column: 4/10;
        /*        margin-left: 90px;*/
        margin-bottom: -35px;
        z-index: 10;
        font-size: 3rem;
        font-family: "Archivo Black", sans-serif;
        color: #FCB11A;
    }
    .home-p-b {
        grid-row: 8;
        grid-column: 3/11;
        margin-top: 155px;
        z-index: 10;
        color: white;
        font-family: "Shippori Antique", sans-serif;
        font-size: 0.8rem;
        line-height: 2.5;
        margin-bottom: 50px;
    }
    .team-six {
        width: 200px;
        grid-row: 8;
        grid-column: 3;
        /*    margin-top: -40px;*/
        z-index: 10;
    }
    .team-six-back {
        width: 15rem;
        height: 15rem;
        grid-row: 8;
        margin-top: 12px;
        grid-column: 4/8;
        z-index: 1;
    }
    .home-p-c {
        grid-row: 4;
        grid-column: 3/11;
        text-align: center;
        /*        margin-left: 60px;*/
        /*        margin-right: 40px;*/
        margin-top: -60px;
        z-index: 10;
        color: white;
        font-family: "Shippori Antique", sans-serif;
        font-size: 0.8rem;
        line-height: 2.5;
    }
    /* Footer */
    footer {
        font-family: "Shippori Antique", sans-serif;
        background-color: #363636;
        color: #fff;
        text-align: center;
        padding: 20px 0;
        grid-column: 1/13;
        width: 100%;
        display: flex;
        flex-direction: column;
        background: linear-gradient(0deg, #1D1D1D, #363636);
    }
    .contact h3 {
        margin-bottom: 25px;
        font-size: 1.1rem;
        font-family: "Archivo Black", sans-serif;
        color: #FCB11A;
    }
    .contact address {
        font-style: normal;
        line-height: 2.5;
        font-size: 0.8rem;
    }
    .footersocial {
        display: flex;
        justify-content: center;
        grid-gap: 115px;
        margin-top: 55px;
        margin-bottom: 40px;
    }
    .footerins,
    .footerfacebook,
    .footerred {
        width: 35px;
        height: auto;
    }
    footer a {
        text-decoration: none;
        color: #fff;
        cursor: pointer;
    }
}

/* above 601px typically tablet*/

@media only screen and (min-width: 601px) {
    .md1 {
        grid-column-end: span 12;
    }
    .md2 {
        grid-column-end: span 6;
    }
    .md3 {
        grid-column-end: span 4;
    }
    .md4 {
        grid-column-end: span 3;
    }
    .md6 {
        grid-column-end: span 2;
    }
    .md12 {
        grid-column-end: span 1;
    }
    .twothird {
        grid-column-end: span 8;
    }
    /* Navigation */
    .logo img {
        width: 200px;
        margin-left: 30px;
        z-index: 12;
    }
    .logo {
        grid-column: 1 / 3;
        grid-row: 1;
        z-index: 10;
        margin-top: 25px;
        z-index: 12;
    }
    .logowhite img {
        width: 280px;
        z-index: 12;
    }
    .logowhite {
        grid-column: 1 / 3;
        grid-row: 1;
        margin-top: 5px;
        margin-left: -10px;
        z-index: 12;
    }
    .profile img {
        width: 60px;
    }
    .profile {
        grid-column: 11 / 13;
        grid-row: 1;
        z-index: 10;
        justify-self: end;
        margin-right: 40px;
        margin-top: 20px;
    }
    header {
        grid-row: 1;
        grid-column: 1/13;
        margin-top: 70px;
        z-index: 10;
        margin-right: 25px;
    }
    nav ul {
        font-family: "Allerta Stencil", sans-serif;
        display: flex;
        justify-content: flex-end;
        grid-gap: 20px;
    }
    nav ul li {
        margin: 0 30px 0 0;
        display: inline;
    }
    nav ul li a {
        text-decoration: none;
        color: #FCB11A;
        font-weight: bold;
        font-size: 16px;
    }
    nav a:hover {
        background-color: #b17600;
        border: 2px solid #b17600;
        color: white;
    }
    /*
nav a.current {
    background-color: #99AACC;
    border: 2px solid #CCDDEE;
    color: white;
    cursor: default;
}
*/
    #nav-trigger {
        display: none;
    }
    label[for="nav-trigger"] {
        display: none;
    }
    .down {
        width: 15px;
        margin-bottom: -2px;
    }
    /*Home Page*/
    .home-background-container {
        grid-column: 1 / 13;
        grid-row: 1;
        height: 60vh;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
    }
    .home-background {
        position: absolute;
        top: 0;
        /*    left: 0;*/
        width: 100%;
        height: 700px;
        background-image: url('../img/vexiq_basebot.jpg');
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center center;
        z-index: 1;
        filter: brightness(50%);
        /*    filter:blur(12px);*/
    }
    .title-image-container {
        grid-column: 8;
        /*        margin-left: -30px;*/
        grid-row: 1;
        position: relative;
        z-index: 10;
    }
    .title-logo {
        width: 80%;
    }
    .team-three {
        width: 600px;
        grid-row: 6;
        grid-column: 2;
        margin-left: 80px;
        z-index: 10;
        margin-top: -30px;
    }
    .team-three-back {
        width: 45rem;
        height: 45rem;
        grid-row: 6;
        /*        margin-top: -0px;*/
        grid-column: 3/8;
        margin-left: -30px;
        z-index: 1;
    }
    .home-t-a {
        grid-row: 5;
        grid-column: 3/13;
        margin-top: -15px;
        font-size: 5.2rem;
        font-family: "Archivo Black", sans-serif;
        color: #FCB11A;
        z-index: 10;
    }
    .home-p-a {
        grid-row: 7;
        grid-column: 3/11;
        /*        margin-right: 275px;*/
        /*        margin-left: 80px;*/
        margin-top: -298px;
        font-size: 1.1rem;
        line-height: 2.5;
        z-index: 10;
        color: white;
        font-family: "Shippori Antique", sans-serif;
    }
    .home-t-b {
        grid-row: 8;
        grid-column: 5/11;
        margin-left: 90px;
        margin-bottom: -35px;
        z-index: 10;
        font-size: 5.2rem;
        font-family: "Archivo Black", sans-serif;
        color: #FCB11A;
    }
    .home-p-b {
        grid-row: 10;
        grid-column: 3/11;
        margin-top: -250px;
        margin-bottom: 50px;
        z-index: 10;
        color: white;
        font-family: "Shippori Antique", sans-serif;
        font-size: 1.1rem;
        line-height: 2.5;
    }
    .team-six {
        width: 500px;
        grid-row: 9;
        grid-column: 3;
        /*    margin-top: -40px;*/
        z-index: 10;
    }
    .team-six-back {
        width: 36rem;
        height: 41rem;
        grid-row: 9;
        margin-top: -10px;
        grid-column: 4/8;
        margin-left: 30px;
        z-index: 1;
    }
    .home-p-c {
        grid-row: 4;
        grid-column: 3/11;
        text-align: center;
        margin-left: 60px;
        margin-right: 40px;
        margin-top: -100px;
        z-index: 10;
        color: white;
        font-family: "Shippori Antique", sans-serif;
        font-size: 1.6rem;
        line-height: 2.5;
    }
    /* Footer */
    footer {
        font-family: "Shippori Antique", sans-serif;
        background-color: #363636;
        color: #fff;
        text-align: center;
        padding: 20px 0;
        grid-column: 1/13;
        width: 100%;
        display: flex;
        flex-direction: column;
        background: linear-gradient(0deg, #1D1D1D, #363636);
    }
    .contact h3 {
        margin-bottom: 25px;
        font-size: 1.4rem;
        font-family: "Archivo Black", sans-serif;
        color: #FCB11A;
    }
    .contact address {
        font-style: normal;
        line-height: 2.5;
    }
    .footersocial {
        display: flex;
        justify-content: center;
        grid-gap: 125px;
        margin-top: 55px;
        margin-bottom: 40px;
    }
    .footerins,
    .footerfacebook,
    .footerred {
        width: 55px;
        height: auto;
    }
    footer a {
        text-decoration: none;
        color: #fff;
        cursor: pointer;
    }
}

/* above 1025px typically desktop*/

@media only screen and (min-width: 1025px) {
    .col1 {
        grid-column-end: span 12;
    }
    .col2 {
        grid-column-end: span 6;
    }
    .col3 {
        grid-column-end: span 4;
    }
    .col4 {
        grid-column-end: span 3;
    }
    .col6 {
        grid-column-end: span 2;
    }
    .col12 {
        grid-column-end: span 1;
    }
    .twothird {
        grid-column-end: span 8;
    }
    /* Navigation */
    .logo img {
        width: 200px;
        margin-left: 30px;
        z-index: 12;
    }
    .logo {
        grid-column: 1 / 3;
        grid-row: 1;
        z-index: 10;
        margin-top: 25px;
        z-index: 12;
    }
    .logowhite img {
        width: 280px;
        z-index: 12;
    }
    .logowhite {
        grid-column: 1 / 3;
        grid-row: 1;
        margin-top: 5px;
        margin-left: -10px;
        z-index: 12;
    }
    .profile img {
        width: 60px;
    }
    .profile {
        grid-column: 11 / 13;
        grid-row: 1;
        z-index: 10;
        justify-self: end;
        margin-right: 40px;
        margin-top: 20px;
    }
    header {
        grid-row: 1;
        grid-column: 1/13;
        margin-top: 70px;
        z-index: 10;
        margin-right: 25px;
    }
    nav ul {
        font-family: "Allerta Stencil", sans-serif;
        display: flex;
        justify-content: flex-end;
        grid-gap: 20px;
    }
    nav ul li {
        margin: 0 30px 0 0;
        display: inline;
    }
    nav ul li a {
        text-decoration: none;
        color: #FCB11A;
        font-weight: bold;
        font-size: 16px;
    }
    nav a:hover {
        background-color: #b17600;
        border: 2px solid #b17600;
        color: white;
    }
    /*
nav a.current {
    background-color: #99AACC;
    border: 2px solid #CCDDEE;
    color: white;
    cursor: default;
}
*/
    #nav-trigger {
        display: none;
    }
    label[for="nav-trigger"] {
        display: none;
    }
    .down {
        width: 15px;
        margin-bottom: -2px;
    }
    /*Home Page*/
    .home-background-container {
        grid-column: 1 / 13;
        grid-row: 1;
        height: 60vh;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
    }
    .home-background {
        position: absolute;
        top: 0;
        /*    left: 0;*/
        width: 100%;
        height: 900px;
        background: url('../img/vexiq_basebot.jpg') center center fixed;
        background-size: cover;
        filter: blur(4px);
        z-index: 1;
        filter: brightness(50%);
        /*    filter:blur(12px);*/
    }
    .title-image-container {
        grid-column: 8;
        margin-left: -130px;
        margin-top: 300px;
        grid-row: 1;
        position: relative;
        z-index: 10;
    }
    .title-logo {
        width: 80%;
    }
    .team-three {
        width: 600px;
        grid-row: 45;
        grid-column: 2;
        margin-left: 50px;
        z-index: 10;
        margin-top: 30px;
    }
    .team-three-back {
        width: 45rem;
        height: 45rem;
        grid-row: 45;
        margin-top: -70px;
        grid-column: 3/8;
        margin-left: 40px;
        z-index: 1;
    }
    .home-t-a {
        grid-row: 45;
        grid-column: 8/13;
        margin-left: 90px;
        margin-top: -95px;
        font-size: 5.2rem;
        font-family: "Archivo Black", sans-serif;
        color: #FCB11A;
        z-index: 10;
    }
    .home-p-a {
        grid-row: 45;
        grid-column: 7/13;
        margin-right: 275px;
        margin-left: 80px;
        margin-top: 198px;
        font-size: 1.1rem;
        line-height: 2.5;
        z-index: 10;
        color: white;
        font-family: "Shippori Antique", sans-serif;
    }
    .home-t-b {
        grid-row: 46;
        grid-column: 2/7;
        margin-left: 90px;
        margin-bottom: -35px;
        z-index: 10;
        font-size: 5.2rem;
        font-family: "Archivo Black", sans-serif;
        color: #FCB11A;
    }
    .home-p-b {
        grid-row: 47;
        grid-column: 2/7;
        margin-left: 90px;
        margin-right: 40px;
        margin-top: 10px;
        z-index: 10;
        color: white;
        font-family: "Shippori Antique", sans-serif;
        font-size: 1.1rem;
        line-height: 2.5;
    }
    .team-six {
        width: 500px;
        grid-row: 47;
        grid-column: 7;
        margin-left: 30px;
        /*    margin-top: -40px;*/
        z-index: 10;
    }
    .team-six-back {
        width: 33rem;
        height: 41rem;
        grid-row: 47;
        margin-top: -10px;
        grid-column: 4/8;
        margin-left: 130px;
        z-index: 1;
    }
    .home-p-c {
        grid-row: 42;
        grid-column: 3/11;
        text-align: center;
        margin-left: 60px;
        margin-right: 40px;
        margin-top: -200px;
        z-index: 10;
        color: white;
        font-family: "Shippori Antique", sans-serif;
        font-size: 1.6rem;
        line-height: 2.5;
    }
    /* Footer */
    footer {
        font-family: "Shippori Antique", sans-serif;
        background-color: #363636;
        color: #fff;
        text-align: center;
        padding: 20px 0;
        grid-column: 1/13;
        width: 100%;
        display: flex;
        flex-direction: column;
        background: linear-gradient(0deg, #1D1D1D, #363636);
    }
    .contact h3 {
        margin-bottom: 25px;
        font-size: 1.4rem;
        font-family: "Archivo Black", sans-serif;
        color: #FCB11A;
    }
    .contact address {
        font-style: normal;
        line-height: 2.5;
    }
    .footersocial {
        display: flex;
        justify-content: center;
        grid-gap: 125px;
        margin-top: 55px;
        margin-bottom: 40px;
    }
    .footerins,
    .footerfacebook,
    .footerred {
        width: 55px;
        height: auto;
    }
    footer a {
        text-decoration: none;
        color: #fff;
        cursor: pointer;
    }
}
