@charset "UFT-8";
html,
body {
    margin: 0;
    padding: 0;
    width: 100%;
    overflow-x: hidden;
    /* Prevents horizontal scroll */
}
#container {
    width: 100%;
    max-width: 100%;
    /* Ensures no extra spacing */
    margin: 0;
    /* Remove default margins */
    padding: 0;
    /* Remove default padding */
    grid-gap: 0;
    /* Remove grid gaps if causing issues */
}
#container {
    display: grid;
    grid-template-rows: 80px 60vh 1fr auto;
    /*    grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr;*/
    /*    grid-template-columns: repeat(12, 1fr);*/
    grid-template-columns: repeat(12, minmax(0, 1fr));
    /*    grid-template-rows: 1fr 1fr 1fr 1fr 1fr;*/
    grid-template-rows: auto;
    grid-gap: 15px;
    background-color: #363636;
    box-sizing: border-box;
}
body {
    background-color: #363636;
    background: linear-gradient(0deg, #1D1D1D, #363636);
}

/* Navigation */

.logo img {
    width: 200px;
    margin-left: 30px;
    z-index: 12;
}
.logo {
    grid-column: 1 / 3;
    grid-row: 1;
    z-index: 10;
    margin-top: 25px;
    z-index: 12;
}
.logowhite img {
    width: 280px;
    z-index: 12;
}
.logowhite {
    grid-column: 1 / 3;
    grid-row: 1;
    margin-top: 5px;
    margin-left: -10px;
    z-index: 12;
}
.profile img {
    width: 60px;
}
.profile {
    grid-column: 11 / 13;
    grid-row: 1;
    z-index: 10;
    justify-self: end;
    margin-right: 40px;
    margin-top: 20px;
}
header {
    grid-row: 1;
    grid-column: 1/13;
    margin-top: 70px;
    z-index: 10;
    margin-right: 25px;
}
nav ul {
    font-family: "Allerta Stencil", sans-serif;
    display: flex;
    justify-content: flex-end;
    grid-gap: 20px;
}
nav ul li {
    margin: 0 30px 0 0;
    display: inline;
}
nav ul li a {
    text-decoration: none;
    color: #FCB11A;
    font-weight: bold;
    font-size: 16px;
}
nav a:hover {
    background-color: #b17600;
    border: 2px solid #b17600;
    color: white;
}

/*
nav a.current {
    background-color: #99AACC;
    border: 2px solid #CCDDEE;
    color: white;
    cursor: default;
}
*/

#nav-trigger {
    display: none;
}
label[for="nav-trigger"] {
    display: none;
}
.down {
    width: 15px;
    margin-bottom: -2px;
}

/*Login Page*/

.loginbacka {
    width: 900px;
    grid-row: 2;
    z-index: 1;
    grid-column: 8;
    margin-top: -210px;
    margin-left: 40px;
}
h1 {
    font-size: 3.3rem;
    font-family: "Archivo Black", sans-serif;
    color: white;
    text-align: center;
    margin-bottom: -2px;
    margin-top: 80px;
}
.loginsignup {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}
.loginp1 {
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    color: #A9A9A9;
}
.loginsignup a {
    text-decoration: none;
    color: white;
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    /*    margin-top: 17px;*/
    margin-left: 13px;
}
.logininput-box {
    position: relative;
    margin-bottom: 35px;
    width: 85%;
}
.input-icon {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
    pointer-events: none;
}
.loginemailicon {
    width: 28px;
    height: 20px;
}
.loginpasswordicon {
    width: 26px;
    height: 33px;
    margin-bottom: 10px;
}
.usericon {
    width: 24px;
    height: 30px;
}
.logininput-box input {
    width: 100%;
    padding: 12px 0 12px 53px;
    background: transparent;
    border: none;
    border-bottom: 1px solid white;
    color: white;
    font-size: 16px;
    outline: none;
    transition: all 0.3s ease;
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
}
.logininput-box input::placeholder {
    color: rgba(255, 255, 255, 0.7);
    font-weight: 300;
}
.logininput-box input:focus {
    border-bottom-width: 2px;
    border-bottom-color: #ffffff;
}
.loginpart {
    display: flex;
    justify-content: center;
    align-items: 100vh;
    background: #454545;
    /*
    background: grey;
    opacity: 0.4;
*/
    grid-column: 6/12;
    position: relative;
    width: 730px;
    height: 720px;
    margin-top: 60px;
    border-radius: 120px;
    /*    box-shadow: 0 0 30px black;*/
    grid-row: 2;
    z-index: 2;
}

/*
.forgot-link {
    margin-bottom: 20px;
}
.forgot-link a {
    text-decoration: none;
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    color: white;
}
*/

.loginsubmitbtn {
    width: 103%;
    height: 80px;
    border-radius: 25px;
    border: none;
    background-color: #FCB11A;
    color: white;
    font-size: 1.6rem;
    font-family: "Shippori Antique", sans-serif;
}
.welcomeback {
    grid-column: 3/6;
    grid-row: 2;
    margin-top: 70px;
    text-align: center;
    align-content: center;
}
.helloa {
    width: 330px;
    perspective: 400px;
    transform-style: preserve-3d;
}
span {
    position: relative;
    color: white;
    width: 10px;
    height: 120px;
    float: left;
    margin: 0 8px;
    transform: rotateX(-360deg);
    animation: run 2.5s linear infinite;
    animation-delay: calc(0.15s * var(--i));
}

/*
span {
    position: relative;
    color: white;
    width: 10px;
    height: 120px;
    float: left;
    margin: 0 8px;
    transform: rotateX(-360deg);
    animation: run 2.5s linear infinite;
    animation-delay: calc(0.15s * var(--i));
}
*/

span:before,
spam:after {
    position: absolute;
    content: attr(data-txt);
    font-family: "Shippori Antique", sans-serif;
    font-size: 24px;
    left: 50%;
    transform: translateX(-50%);
}
span:before {
    top: -2px;
}
span:after {
    bottom: -2px;
}
@keyframes run {
    to {
        transform: none;
    }
}
.loginbackb {
    grid-row: 3;
    grid-column: 1;
    width: 1000px;
    margin-left: -200px;
    margin-top: -500px;
}

/*verification code*/


/*
.verification-code-part {
        background-color: white;
    background: rgba(255, 255, 255, 0.925);
    grid-column: 6/12;
    position: relative;
    width: 460px;
    height: 620px;
    margin-top: 110px;
    margin-left: 140px;
    border-radius: 120px;
    box-shadow: 0 0 30px black;
    grid-row: 2;
    z-index: 3;
}
.signupp2 {
    font-family: "Archivo Black", sans-serif;
    color: #363636;
    text-align: center;
    margin-top: 100px;
    font-size: 2rem;
}
.email-verification {
    text-align: center;
}
.email-verification-icon {
    width: 50px;
}
.signupp3 {
    font-family: "Shippori Antique", sans-serif;
    color: #363636;
    margin-top: 0px;
    font-size: 1.2rem;
}
.verification-resenda {
    font-family: "Shippori Antique", sans-serif;
    color: #363636;
    display: flex;
    text-align: center;
    margin-top: -20px;
    text-align: center;
    font-size: 0.8rem;
    margin-left:
}
.verification-resendb {
    font-family: "Shippori Antique", sans-serif;
    color: #363636;
    display: flex;
    margin-bottom: 15px;
    align-items: center;
    font-size: 0.8rem;
}
.verification-resendb a {
    text-decoration: none;
    color: #FCB11A;
    font-size: 0.8rem;
    font-family: "Shippori Antique", sans-serif;
    margin-left: 13px;
}
.verification-resend {
    margin-left: 65px;
}
.verification-inputbox {
    position: relative;
    margin-bottom: 35px;
    margin-left: 65px;
    width: 73%;
}
.verification-inputbox input {
    width: 100%;
    padding: 12px 0 12px 0px;
    background: transparent;
    border: none;
    border-bottom: 2px solid #363636;
    color: #969696;
    font-size: 0.8rem;
    outline: none;
    transition: all 0.3s ease;
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
}
.verification-inputbox input::placeholder {
    color: #969696;
    font-size: 0.8rem;
}
.verification-inputbox input:focus {
    border-bottom-width: 2px;
    border-bottom-color: #363636;
}
.verification-code-btn {
   margin-left:53px;
    width: 70%;
    height: 80px;
    border-radius: 25px;
    border: none;
    background-color: #FCB11A;
    color: white;
    font-size: 1.6rem;
    font-family: "Shippori Antique", sans-serif;
}
*/


/*verification code*/

.verification-code-part {
    background: rgba(255, 255, 255, 0.925);
    grid-column: 6/12;
    position: relative;
    width: 460px;
    height: 620px;
    margin-top: 110px;
    margin-left: 140px;
    border-radius: 120px;
    box-shadow: 0 0 30px black;
    grid-row: 2;
    z-index: 3;
    /* Add these flex properties to center content */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.signup-p-b {
    font-family: "Archivo Black", sans-serif;
    color: #363636;
    text-align: center;
    margin-top: -35px;
    font-size: 2rem;
}
.email-verification {
    margin-top: -10px;
    text-align: center;
}
.email-verification-icon {
    width: 50px;
}
.signup-p-c {
    font-family: "Shippori Antique", sans-serif;
    color: #363636;
    margin-top: 5px;
    font-size: 1.2rem;
}
.verification-resend {
    margin-top: 40px;
    text-align: center;
    width: 100%;
    margin-bottom: 90px;
}
.verification-resenda {
    font-family: "Shippori Antique", sans-serif;
    color: #7AC921;
    ;
    /* Remove display flex and margin adjustments */
    text-align: center;
    font-size: 0.8rem;
    margin-bottom: 10px;
}
.verification-resendb {
    font-family: "Shippori Antique", sans-serif;
    color: #7AC921;
    ;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 0.8rem;
    margin-top: -10px;
}
.verification-resendb a {
    text-decoration: none;
    color: #FCB11A;
    font-size: 0.8rem;
    font-family: "Shippori Antique", sans-serif;
    margin-left: 13px;
}
.verification-inputbox {
    position: relative;
    margin-top: 50px;
    margin-bottom: 30px;
    width: 73%;
}
.verification-inputbox input {
    width: 100%;
    padding: 12px 0;
    background: transparent;
    border: none;
    border-bottom: 2px solid #363636;
    color: #969696;
    font-size: 1.1rem;
    outline: none;
    transition: all 0.3s ease;
    font-family: "Shippori Antique", sans-serif;
}
.verification-inputbox input::placeholder {
    color: #969696;
    font-size: 0.8rem;
}
.verification-inputbox input:focus {
    border-bottom-width: 2px;
    border-bottom-color: #363636;
}
.verification-code-btn {
    width: 55%;
    height: 80px;
    border-radius: 25px;
    border: none;
    background-color: #FCB11A;
    color: white;
    font-size: 1.6rem;
    font-family: "Shippori Antique", sans-serif;
    margin-bottom: -70px;
    /* Remove margin-left as flex handles centering */
}
.verification-code-btn a {
    text-decoration: none;
    color: white;
}

/* Footer */

footer {
    font-family: "Shippori Antique", sans-serif;
    background-color: #363636;
    color: #fff;
    text-align: center;
    padding: 20px 0;
    grid-column: 1/13;
    width: 100%;
    display: flex;
    flex-direction: column;
    background: linear-gradient(0deg, #1D1D1D, #363636);
}
.contact h3 {
    margin-bottom: 25px;
    font-size: 1.4rem;
    font-family: "Archivo Black", sans-serif;
    color: #FCB11A;
}
.contact address {
    font-style: normal;
    line-height: 2.5;
}
.footersocial {
    display: flex;
    justify-content: center;
    grid-gap: 125px;
    margin-top: 55px;
    margin-bottom: 40px;
}
.footerins,
.footerfacebook,
.footerred {
    width: 55px;
    height: auto;
}
footer a {
    text-decoration: none;
    color: #fff;
    cursor: pointer;
}

/* below 600px typically tablet*/

@media only screen and (max-width: 600px) {
    .sm1 {
        grid-column-end: span 12;
    }
    .sm2 {
        grid-column-end: span 6;
    }
    .sm3 {
        grid-column-end: span 4;
    }
    .sm4 {
        grid-column-end: span 3;
    }
    .sm6 {
        grid-column-end: span 2;
    }
    .sm12 {
        grid-column-end: span 1;
    }
}

/* above 601px typically tablet*/

@media only screen and (min-width: 601px) {
    .md1 {
        grid-column-end: span 12;
    }
    .md2 {
        grid-column-end: span 6;
    }
    .md3 {
        grid-column-end: span 4;
    }
    .md4 {
        grid-column-end: span 3;
    }
    .md6 {
        grid-column-end: span 2;
    }
    .md12 {
        grid-column-end: span 1;
    }
    .twothird {
        grid-column-end: span 8;
    }
}

/* above 1025px typically desktop*/

@media only screen and (min-width: 1025px) {
    .col1 {
        grid-column-end: span 12;
    }
    .col2 {
        grid-column-end: span 6;
    }
    .col3 {
        grid-column-end: span 4;
    }
    .col4 {
        grid-column-end: span 3;
    }
    .col6 {
        grid-column-end: span 2;
    }
    .col12 {
        grid-column-end: span 1;
    }
    .twothird {
        grid-column-end: span 8;
    }
}
