@charset "UFT-8";
html,
body {
    margin: 0;
    padding: 0;
    width: 100%;
    overflow-x: hidden;
    /* Prevents horizontal scroll */
}
#container {
    width: 100%;
    max-width: 100%;
    /* Ensures no extra spacing */
    margin: 0;
    /* Remove default margins */
    padding: 0;
    /* Remove default padding */
    grid-gap: 0;
    /* Remove grid gaps if causing issues */
}
#container {
    display: grid;
    grid-template-rows: 80px 60vh 1fr auto;
    /*    grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr;*/
    /*    grid-template-columns: repeat(12, 1fr);*/
    grid-template-columns: repeat(12, minmax(0, 1fr));
    /*    grid-template-rows: 1fr 1fr 1fr 1fr 1fr;*/
    grid-template-rows: auto;
    grid-gap: 15px;
    background-color: #363636;
    box-sizing: border-box;
}
body {
    background-color: #363636;
    background: linear-gradient(0deg, #1D1D1D, #363636);
}

/* Navigation */

.logo img {
    width: 200px;
    margin-left: 30px;
    z-index: 12;
}
.logo {
    grid-column: 1 / 3;
    grid-row: 1;
    z-index: 10;
    margin-top: 25px;
    z-index: 12;
}
.logowhite img {
    width: 280px;
    z-index: 12;
}
.logowhite {
    grid-column: 1 / 3;
    grid-row: 1;
    margin-top: 5px;
    margin-left: -10px;
    z-index: 12;
}
.profile img {
    width: 60px;
}
.profile {
    grid-column: 11 / 13;
    grid-row: 1;
    z-index: 10;
    justify-self: end;
    margin-right: 40px;
    margin-top: 20px;
}
header {
    grid-row: 1;
    grid-column: 1/13;
    margin-top: 70px;
    z-index: 10;
    margin-right: 25px;
}
nav ul {
    font-family: "Allerta Stencil", sans-serif;
    display: flex;
    justify-content: flex-end;
    grid-gap: 20px;
}
nav ul li {
    margin: 0 30px 0 0;
    display: inline;
}
nav ul li a {
    text-decoration: none;
    color: #FCB11A;
    font-weight: bold;
    font-size: 16px;
}
nav a:hover {
    background-color: #b17600;
    border: 2px solid #b17600;
    color: white;
}

/*
nav a.current {
    background-color: #99AACC;
    border: 2px solid #CCDDEE;
    color: white;
    cursor: default;
}
*/

#nav-trigger {
    display: none;
}
label[for="nav-trigger"] {
    display: none;
}
.down {
    width: 15px;
    margin-bottom: -2px;
}

/*Home Page*/

.home-background-container {
    grid-column: 1 / 13;
    grid-row: 1;
    height: 60vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}
.home-background {
    position: absolute;
    top: 0;
    /*    left: 0;*/
    width: 100%;
    height: 900px;
    background: url('../img/vexiq_basebot.jpg') center center fixed;
    background-size: cover;
    filter: blur(4px);
    z-index: 1;
    filter: brightness(50%);
    /*    filter:blur(12px);*/
}
.title-image-container {
    grid-column: 8;
    margin-left: -130px;
    margin-top: 300px;
    grid-row: 1;
    position: relative;
    z-index: 10;
}
.title-logo {
    width: 80%;
}
.team-three {
    width: 600px;
    grid-row: 45;
    grid-column: 2;
    margin-left: 50px;
    z-index: 10;
    margin-top: 30px;
}
.team-three-back {
    width: 45rem;
    height: 45rem;
    grid-row: 45;
    margin-top: -70px;
    grid-column: 3/8;
    margin-left: 40px;
    z-index: 1;
}
.home-t-a {
    grid-row: 45;
    grid-column: 8/13;
    margin-left: 90px;
    margin-top: -95px;
    font-size: 5.2rem;
    font-family: "Archivo Black", sans-serif;
    color: #FCB11A;
    z-index: 10;
}
.home-p-a {
    grid-row: 45;
    grid-column: 7/13;
    margin-right: 275px;
    margin-left: 80px;
    margin-top: 198px;
    font-size: 1.1rem;
    line-height: 2.5;
    z-index: 10;
    color: white;
    font-family: "Shippori Antique", sans-serif;
}
.home-t-b {
    grid-row: 46;
    grid-column: 2/7;
    margin-left: 90px;
    margin-bottom: -35px;
    z-index: 10;
    font-size: 5.2rem;
    font-family: "Archivo Black", sans-serif;
    color: #FCB11A;
}
.home-p-b {
    grid-row: 47;
    grid-column: 2/7;
    margin-left: 90px;
    margin-right: 40px;
    margin-top: 10px;
    z-index: 10;
    color: white;
    font-family: "Shippori Antique", sans-serif;
    font-size: 1.1rem;
    line-height: 2.5;
}
.team-six {
    width: 500px;
    grid-row: 47;
    grid-column: 7;
    margin-left: 30px;
    /*    margin-top: -40px;*/
    z-index: 10;
}
.team-six-back {
    width: 33rem;
    height: 41rem;
    grid-row: 47;
    margin-top: -10px;
    grid-column: 4/8;
    margin-left: 130px;
    z-index: 1;
}
.home-p-c {
    grid-row: 42;
    grid-column: 3/11;
    text-align: center;
    margin-left: 60px;
    margin-right: 40px;
    margin-top: -200px;
    z-index: 10;
    color: white;
    font-family: "Shippori Antique", sans-serif;
    font-size: 1.6rem;
    line-height: 2.5;
}

/*About Us*/

.aboutyellowbacka {
    width: 85rem;
    grid-row: 1;
    grid-column: 1;
    margin-top: -20rem;
    margin-left: -20rem;
    z-index: 1;
    /* Lower z-index so nav stays above */
    position: relative;
}
.about-t-a {
    font-size: clamp(2.5rem, 7vw, 7.2rem);
    font-family: "Archivo Black", sans-serif;
    color: white;
    grid-row: 1;
    grid-column: 1/13;
    margin-top: 18vw;
    margin-left: 100px;
    z-index: 20;
    /* Ensure header/nav is above backgrounds */
    position: relative;
    /* Remove background and padding for transparent look */
    background: none;
    padding: 0;
    border-radius: 0;
    box-sizing: border-box;
    max-width: 100vw;
    word-break: break-word;
    text-align: center;
    line-height: 1.1;
    position: relative;
}
.about-p-a {
    grid-row: 2;
    margin-top: -700px;
    margin-right: 30px;
    grid-column: 5/11;
    font-size: 1.1rem;
    color: white;
    font-family: "Shippori Antique", sans-serif;
    line-height: 2.5;
    z-index: 10;
}
.highlight {
    font-size: 1.3rem;
}
.abouttablea {
    grid-row: 2;
    color: white;
    margin-top: -570px;
    margin-left: -25px;
    font-size: 0.7rem;
    font-family: "Allerta Stencil", sans-serif;
    grid-column: 3/5;
    line-height: 2.5;
    z-index: 10;
}
.tablenow {
    font-size: 1.1rem;
}
.aboutyellowbackb {
    grid-row: 3;
    grid-column: 7/13;
    margin-top: -400px;
    width: 85rem;
}
.about-t-b {
    font-size: 6.3rem;
    font-family: "Archivo Black", sans-serif;
    color: #FCB11A;
    grid-row: 3;
    grid-column: 3/10;
    margin-top: 30px;
    z-index: 10;
}
.about-p-b {
    grid-row: 3;
    margin-left: 5px;
    margin-right: -20px;
    margin-top: 160px;
    grid-column: 3/8;
    font-size: 1.1rem;
    color: white;
    font-family: "Shippori Antique", sans-serif;
    line-height: 2.5;
    z-index: 10;
}
.abouttableb {
    grid-row: 3;
    color: white;
    margin-top: 420px;
    margin-left: -25px;
    font-size: 0.7rem;
    font-family: "Allerta Stencil", sans-serif;
    grid-column: 10/13;
    line-height: 2.5;
    z-index: 10;
}
.teama {
    grid-row: 4;
    grid-column: 5/9;
    margin-left: -30px;
    margin-right: -30px;
    margin-top: 50px;
    justify-items: center;
}
.teamb {
    grid-row: 5;
    grid-column: 5/9;
    margin-left: -30px;
    margin-right: -30px;
    margin-top: 150px;
    justify-items: center;
}
.teamc {
    grid-row: 6;
    grid-column: 5/9;
    margin-left: -30px;
    margin-right: -30px;
    margin-top: 150px;
    justify-items: center;
    z-index: 2;
}
.teamd {
    grid-row: 7;
    grid-column: 5/9;
    margin-left: -30px;
    margin-right: -30px;
    margin-top: 150px;
    justify-items: center;
    z-index: 2;
}
.aboutphoto {
    display: block;
    width: 200px;
    border-radius: 100px;
}
.teamname {
    color: white;
    font-family: "Allerta Stencil", sans-serif;
    font-size: 2rem;
}
.teamtitle {
    color: #969696;
    font-family: "Allerta Stencil", sans-serif;
    font-size: 1.5rem;
}
.teamintro {
    color: white;
    font-family: "Shippori Antique", sans-serif;
    line-height: 2.5;
    font-size: 1.1rem;
    text-align: center;
}
.teamins {
    width: 60px;
    object-fit: contain;
}
.teamface {
    width: 65px;
    object-fit: contain;
}
.teamx {
    width: 50px;
    object-fit: contain;
}
.teamemail {
    width: 50px;
    object-fit: contain;
}
.teamcontact {
    margin-top: 40px;
    display: flex;
    justify-content: center;
    grid-gap: 125px;
}
.aboutyellowbackc {
    grid-row: 8;
    margin-left: -700px;
    margin-top: -500px;
    z-index: 1;
    width: 90rem;
}
.about-t-c {
    font-size: 6.3rem;
    font-family: "Archivo Black", sans-serif;
    color: #FCB11A;
    grid-row: 8;
    grid-column: 6/11;
    margin-top: 260px;
    z-index: 10;
}
.about-p-c {
    grid-row: 8;
    margin-left: 5px;
    margin-right: -60px;
    margin-top: 400px;
    margin-bottom: 80px;
    grid-column: 6/11;
    font-size: 1.1rem;
    color: white;
    font-family: "Shippori Antique", sans-serif;
    line-height: 2.5;
    z-index: 10;
}
.coacha {
    grid-row: 9;
    grid-column: 4/6;
    justify-items: center;
}
.coachb {
    grid-row: 9;
    grid-column: 8/10;
    justify-items: center;
}
.coachc {
    grid-row: 10;
    grid-column: 4/6;
    justify-items: center;
    margin-top: 150px;
    margin-left: -30px;
    margin-right: -30px;
}
.coachd {
    grid-row: 10;
    grid-column: 8/10;
    justify-items: center;
    margin-top: 150px;
}
.coache {
    grid-row: 11;
    grid-column: 4/6;
    justify-items: center;
    margin-top: 150px;
    margin-left: -30px;
    margin-right: -30px;
}
.coachname {
    color: white;
    font-family: "Allerta Stencil", sans-serif;
    font-size: 2rem;
}
.coachtitle {
    color: #969696;
    font-family: "Allerta Stencil", sans-serif;
    font-size: 1.5rem;
}
.coachins {
    width: 40px;
    object-fit: contain;
}
.coachface {
    width: 45px;
    object-fit: contain;
}
.coachx {
    width: 35px;
    object-fit: contain;
}
.coachemail {
    width: 35px;
    object-fit: contain;
}
.coachcontact {
    margin-top: 40px;
    display: flex;
    justify-content: center;
    grid-gap: 25px;
}
.about-t-d {
    grid-row: 12;
    grid-column: 3/8;
    font-size: 6.3rem;
    font-family: "Archivo Black", sans-serif;
    color: white;
    margin-top: 150px;
}

/* Partner Table*/

.abouttablec {
    grid-row: 12;
    color: rgb(255, 255, 255);
    margin-top: 270px;
    margin-left: -25px;
    font-size: 0.7rem;
    font-family: "Allerta Stencil", sans-serif;
    grid-column: 10/12;
    line-height: 2.5;
    z-index: 10;
}
.abouttablec ul li::marker {
    content: none;
}
.abouttablec ul li {
    position: relative;
    text-align: right;
    padding-right: 1.2em;
}
.abouttablec ul li::after {
    content: "•";
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
}

/* PartnerButton */

.ourPartnerButton {
    width: 200px;
    height: 600px;
    margin-bottom: 100px;
    border-radius: 140px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    /*    border: 2px solid #000000;*/
    border: none;
    padding-bottom: 700px;
    cursor: pointer;
    /*    background-color: #969696;*/
    background: url('../img/aboutyellowbacke.jpg') center center no-repeat;
    /*
    background-size: 260px 1013px;
    object-fit: contain;
*/
}
.ourPartnerButton .buttonIcon {
    width: 130px;
    height: 130px;
    margin-bottom: 16px;
    margin-top: 350px;
}
.fca {
    width: 190px;
    height: 190px;
    margin-bottom: 16px;
    margin-top: 320px;
}
.fcaname {
    color: white;
    font-family: "Allerta Stencil", sans-serif;
    font-size: 1.1rem;
    margin-top: -25px;
}
.partnername {
    color: white;
    font-family: "Allerta Stencil", sans-serif;
    font-size: 1.1rem;
}
.buttona {
    grid-row: 13;
    grid-column: 4/6;
    margin-left: 100px;
    z-index: 1;
}
.buttonb {
    grid-row: 13;
    grid-column: 5/7;
    margin-top: 250px;
    margin-left: 100px;
    z-index: 3;
}
.buttonc {
    grid-row: 13;
    grid-column: 6/9;
    margin-left: 100px;
    z-index: 1;
}
.buttond {
    grid-row: 13;
    grid-column: 7/9;
    margin-left: 100px;
    margin-top: 250px;
    z-index: 1;
}

/*Contact Us*/

.contactyellowback {
    grid-row: 1;
    grid-column: 1;
    width: 60rem;
    margin-left: -170px;
    margin-top: -160px;
}
.contact-t-a {
    font-size: 8.1rem;
    font-family: "Archivo Black", sans-serif;
    color: white;
    grid-row: 2;
    grid-column: 2/13;
    margin-top: -740px;
    margin-left: 40px;
}
.contact-p-a {
    font-size: 1.4rem;
    color: #969696;
    font-family: "Shippori Antique", sans-serif;
    line-height: 2.5;
    grid-row: 2;
    grid-column: 2/5;
    margin-top: -480px;
    margin-left: 50px;
}
.contact-p-b {
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    line-height: 2;
    grid-row: 3;
    grid-column: 2/6;
    margin-left: 50px;
    margin-top: -320px;
}
.contact-p-ba {
    color: #969696;
}
.contact-p-bb {
    color: white;
}
.contact-p-c {
    grid-row: 3;
    grid-column: 6/8;
    margin-top: -320px;
}
.contact-p-ca {
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    color: #969696;
}
.contactins {
    width: 50px;
    object-fit: contain;
}
.contactface {
    width: 55px;
    object-fit: contain;
}
.contactred {
    width: 55px;
    object-fit: contain;
}
.contactcontact {
    display: flex;
    justify-content: center;
    grid-gap: 55px;
    margin-left: -10px;
}
.contact-p-d {
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    line-height: 2;
    grid-row: 4;
    grid-column: 2/6;
    margin-left: 50px;
    margin-top: -190px;
}
.contact-p-da {
    color: #969696;
}
.contact-p-db {
    color: white;
}
.contact-p-e {
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    line-height: 2;
    grid-row: 5;
    grid-column: 2/6;
    margin-left: 50px;
    margin-top: -45px;
}
.contact-p-ea {
    color: #969696;
}
.contact-p-eb {
    color: white;
}
.contactqr {
    grid-row: 5;
    grid-column: 6/8;
    width: 270px;
    margin-top: -170px;
    margin-bottom: 200px;
}
.contactsideimg {
    width: 585px;
    grid-row: 6;
    grid-column: 10/13;
    margin-top: -935px;
    margin-left: -100px;
}

/*Course A Page*/

.courseaback {
    grid-column: 1 / 13;
    grid-row: 2;
    height: 60vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    width: 100;
}
.coursea {
    position: absolute;
    top: 0;
    width: 100%;
    height: 500px;
    background: url('../img/team2a.jpg') center center fixed;
    background-size: cover;
    filter: blur(4px);
    z-index: 1;
    filter: brightness(50%);
}
.courseatitlea {
    z-index: 2;
    font-size: 7.3rem;
    font-family: "Archivo Black", sans-serif;
    color: #FCB11A;
    text-align: left;
    grid-column: 2/9;
    grid-row: 2;
    margin-top: 255px;
}
.courseapa {
    z-index: 2;
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    color: #363636;
    background-color: #FCB11A;
    grid-row: 2;
    margin-top: 438px;
    grid-column: 11/13;
    margin-left: 35px;
    height: 50px;
    width: 150px;
    border-radius: 50px;
    border: none;
    cursor: pointer;
}
.coursebback {
    grid-column: 1 / 13;
    grid-row: 3;
    height: 60vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    margin-top: -70px;
}
.courseb {
    position: absolute;
    top: 0;
    width: 100%;
    height: 500px;
    background: url('../img/team1.jpg') center center fixed;
    background-size: cover;
    filter: blur(4px);
    z-index: 1;
    filter: brightness(50%);
}
.courseatitleb {
    z-index: 2;
    font-size: 7.3rem;
    font-family: "Archivo Black", sans-serif;
    color: #FCB11A;
    text-align: left;
    grid-column: 2/9;
    grid-row: 3;
    margin-top: 200px;
}
.courseapb {
    z-index: 2;
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    color: #363636;
    background-color: #FCB11A;
    grid-row: 3;
    margin-top: 368px;
    grid-column: 11/13;
    margin-left: 35px;
    height: 50px;
    width: 150px;
    border-radius: 50px;
    border: none;
    cursor: pointer;
}
.coursecback {
    grid-column: 1 / 13;
    grid-row: 4;
    height: 60vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    margin-top: -70px;
}
.coursec {
    position: absolute;
    top: 0;
    width: 100%;
    height: 500px;
    background: url('../img/zzb.jpg') center center fixed;
    background-size: cover;
    filter: blur(4px);
    z-index: 1;
    filter: brightness(50%);
}
.courseatitlec {
    z-index: 2;
    font-size: 7.3rem;
    font-family: "Archivo Black", sans-serif;
    color: #FCB11A;
    text-align: left;
    grid-column: 2/10;
    grid-row: 4;
    margin-top: 175px;
}
.courseapc {
    z-index: 2;
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    color: #363636;
    background-color: #FCB11A;
    grid-row: 4;
    margin-top: 368px;
    grid-column: 11/13;
    margin-left: 35px;
    height: 50px;
    width: 150px;
    border-radius: 50px;
    border: none;
    cursor: pointer;
}

/*Course B Page*/

.courseb-a-back {
    grid-row: 3;
    width: 114rem;
    grid-column: 1/13;
    margin-top: -95px;
    margin-left: -30px;
}
.courseb-p-a {
    color: white;
    font-family: "Shippori Antique", sans-serif;
    opacity: 0.5;
    font-size: 4rem;
    grid-row: 4;
    margin-top: -1235px;
    /*    grid-column: 1/7;*/
    width: 500px;
    padding: 20px;
    rotate: 90deg;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: -255px;
    line-height: 0.9;
}
.courseb-p-b {
    color: white;
    font-size: 6rem;
    font-family: "Archivo Black", sans-serif;
    grid-column: 3/9;
    margin-top: -976px;
    margin-left: 30px;
    grid-row: 4;
}
.coursebqr {
    width: 130px;
    grid-row: 4;
    margin-top: -862px;
    grid-column: 11/12;
    z-index: 2;
}
.courseb-qr-p {
    width: 130px;
    grid-row: 4;
    margin-top: -732px;
    grid-column: 11/12;
    z-index: 2;
    font-size: 0.9rem;
    color: white;
    font-family: "Shippori Antique", sans-serif;
    text-align: center;
}
.courseb-p-c {
    grid-row: 5;
    grid-column: 3/5;
    margin-top: -650px;
    font-size: 1.1rem;
    margin-left: 35px;
    line-height: 2.5;
    color: white;
    font-family: "Shippori Antique", sans-serif;
}
.courseb-p-d {
    grid-row: 5;
    grid-column: 5/7;
    margin-top: -650px;
    font-size: 1.1rem;
    margin-left: 30px;
    line-height: 2.5;
    color: white;
    font-family: "Shippori Antique", sans-serif;
}
.courseb-p-e {
    grid-row: 5;
    grid-column: 8/11;
    margin-top: -650px;
    margin-right: 70px;
    font-size: 1.1rem;
    margin-left: 30px;
    line-height: 2.5;
    color: white;
    line-height: 2.5;
    font-family: "Shippori Antique", sans-serif;
}
.courseb-p-f {
    font-size: 2rem;
    font-family: "Archivo Black", sans-serif;
    grid-row: 6;
    margin-top: -430px;
    grid-column: 3/6;
    margin-left: 35px;
    color: white;
}
.courseb-p-g {
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    grid-row: 7;
    margin-top: -340px;
    grid-column: 3/6;
    margin-left: 35px;
    margin-right: -25px;
    margin-bottom: 40px;
    line-height: 2.5;
    color: white;
}
.courseb-p-h {
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    grid-row: 7;
    margin-top: -340px;
    grid-column: 8/11;
    margin-left: 20px;
    margin-right: -20px;
    line-height: 2.5;
    color: white;
}
.courseb-p-i {
    grid-row: 8;
    grid-column: 3/5;
    margin-left: 35px;
    color: white;
    font-family: "Archivo Black", sans-serif;
    font-size: 3rem;
    line-height: 1.6;
    margin-top: -470px;
}
.courseb-p-j {
    color: white;
    font-family: "Shippori Antique", sans-serif;
    opacity: 0.5;
    font-size: 4rem;
    grid-row: 7;
    grid-column: 11;
    width: 500px;
    rotate: -90deg;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: -110px;
    margin-top: 50px;
    line-height: 0.9;
}
.courseb-p-k {
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    grid-row: 8;
    margin-top: -100px;
    grid-column: 5/12;
    margin-left: 20px;
    margin-left: 70px;
    line-height: 2.5;
    color: white;
}
.courseb-yellow-line {
    grid-row: 9;
    width: 670px;
    grid-column: 4/12;
    margin-left: 120px;
    margin-top: -275px;
}
.courseb-p-l {
    grid-row: 10;
    font-family: "Archivo Black", sans-serif;
    font-size: 3rem;
    line-height: 1.6;
    color: white;
    grid-column: 8/11;
    margin-left: -30px;
    margin-top: -280px;
}
.team-three-b {
    grid-row: 8;
    width: 250px;
    border-radius: 50px;
    grid-column: 3/5;
    margin-top: -35px;
    margin-left: 17px;
}
.courseb-p-m {
    grid-row: 11;
    grid-column: 3/10;
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    line-height: 2.5;
    color: white;
    margin-top: -30px;
    margin-left: 17px;
}
.courseb-p-n {
    grid-row: 12;
    font-family: "Archivo Black", sans-serif;
    font-size: 3rem;
    line-height: 1.6;
    color: white;
    grid-column: 3/5;
    margin-left: 17px;
    z-index: 2;
}
.coursebackc {
    grid-row: 12;
    width: 400px;
    z-index: 1;
    grid-column: 3/5;
    margin-left: -20px;
    margin-top: 10px;
}
.courseb-p-o {
    grid-row: 13;
    grid-column: 3/6;
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    line-height: 2.5;
    color: white;
    margin-left: 17px;
}
.team-two {
    grid-row: 13;
    width: 600px;
    border-radius: 50px;
    grid-column: 7/11;
    margin-top: 20px;
    margin-bottom: 100px;
}
.coursebbackb {
    grid-column: 1 / 13;
    grid-row: 15;
    height: 60vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    margin-top: -70px;
}
.courseb {
    position: absolute;
    top: 0;
    width: 100%;
    height: 500px;
    background: url('../img/team1.jpg') center center fixed;
    background-size: cover;
    filter: blur(4px);
    z-index: 1;
    filter: brightness(50%);
}
.coursebtitleb {
    z-index: 2;
    font-size: 7.3rem;
    font-family: "Archivo Black", sans-serif;
    color: #FCB11A;
    text-align: left;
    grid-column: 2/9;
    grid-row: 15;
    margin-top: 200px;
}
.coursebbp2 {
    z-index: 2;
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    color: #363636;
    background-color: #FCB11A;
    grid-row: 15;
    margin-top: 368px;
    grid-column: 11/13;
    margin-left: 35px;
    height: 50px;
    width: 150px;
    border-radius: 50px;
    border: none;
}
.coursebreadless {
    z-index: 2;
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    color: #363636;
    background-color: #FCB11A;
    grid-row: 14;
    margin-top: -130px;
    margin-bottom: 130px;
    grid-column: 11/13;
    margin-left: 35px;
    height: 50px;
    width: 150px;
    border-radius: 50px;
    border: none;
    cursor: pointer;
}

/*Course C Page*/

.courseback {
    grid-row: 4;
    width: 130rem;
    margin-top: -110px;
    margin-left: -30px;
}
.coursectitle {
    z-index: 2;
    font-size: 7.3rem;
    font-family: "Archivo Black", sans-serif;
    color: #FCB11A;
    text-align: left;
    grid-column: 2/9;
    grid-row: 2;
    margin-top: 271px;
}
.coursec-p-a {
    color: white;
    font-family: "Shippori Antique", sans-serif;
    opacity: 0.5;
    font-size: 4rem;
    grid-row: 5;
    margin-top: -1515px;
    grid-column: 1/7;
    width: 500px;
    padding: 20px;
    rotate: 90deg;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: -195px;
    line-height: 0.9;
}
.coursec-p-b {
    color: white;
    font-size: 6rem;
    font-family: "Archivo Black", sans-serif;
    grid-column: 3/9;
    margin-top: -1116px;
    margin-left: 30px;
    grid-row: 5;
}
.coursecqr {
    width: 130px;
    grid-row: 5;
    margin-top: -1005px;
    grid-column: 11/12;
    z-index: 2;
}
.coursec-qr-p {
    width: 130px;
    grid-row: 5;
    margin-top: -872px;
    grid-column: 11/12;
    z-index: 2;
    font-size: 0.9rem;
    color: white;
    font-family: "Shippori Antique", sans-serif;
    text-align: center;
}
.coursec-p-c {
    grid-row: 5;
    grid-column: 3/5;
    margin-top: -750px;
    font-size: 1.1rem;
    margin-left: 35px;
    line-height: 2.5;
    color: white;
    font-family: "Shippori Antique", sans-serif;
}
.coursec-p-d {
    grid-row: 5;
    grid-column: 5/8;
    margin-top: -750px;
    font-size: 1.1rem;
    margin-left: 70px;
    line-height: 2.5;
    color: white;
    font-family: "Shippori Antique", sans-serif;
}
.coursec-p-e {
    grid-row: 5;
    grid-column: 8/11;
    margin-top: -750px;
    margin-right: 70px;
    font-size: 1.1rem;
    margin-left: 30px;
    line-height: 2.5;
    color: white;
    line-height: 2.5;
    font-family: "Shippori Antique", sans-serif;
}
.coursec-p-f {
    font-size: 2rem;
    font-family: "Archivo Black", sans-serif;
    grid-row: 6;
    margin-top: -550px;
    grid-column: 3/6;
    margin-left: 35px;
    color: white;
}
.coursec-p-g {
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    grid-row: 7;
    margin-top: -450px;
    grid-column: 3/6;
    margin-left: 35px;
    margin-right: -25px;
    line-height: 2.5;
    color: white;
}
.coursec-p-h {
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    grid-row: 7;
    margin-top: -470px;
    grid-column: 8/11;
    margin-left: 20px;
    margin-right: -20px;
    line-height: 2.5;
    color: white;
}
.coursec-p-i {
    grid-row: 8;
    grid-column: 3/5;
    margin-left: 35px;
    color: white;
    font-family: "Archivo Black", sans-serif;
    font-size: 3rem;
    line-height: 1.6;
    margin-top: -480px;
}
.courseb-p-j {
    color: white;
    font-family: "Shippori Antique", sans-serif;
    opacity: 0.5;
    font-size: 4rem;
    grid-row: 7;
    grid-column: 11;
    width: 500px;
    rotate: -90deg;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: -50px;
    margin-top: 40px;
    line-height: 0.9;
}
.coursec-yellow-line {
    grid-row: 9;
    width: 670px;
    grid-column: 4/12;
    margin-left: 120px;
    margin-top: -275px;
}
.coursebackc {
    grid-row: 12;
    width: 400px;
    z-index: 1;
    grid-column: 3/5;
    margin-left: -20px;
    margin-top: 10px;
}
.team-two {
    grid-row: 13;
    width: 600px;
    border-radius: 50px;
    grid-column: 7/11;
    margin-top: 20px;
    margin-bottom: 100px;
}

/*Course D Page*/

.coursed-a-back {
    grid-row: 3;
    width: 114rem;
    grid-column: 1/13;
    margin-top: -95px;
    margin-left: -30px;
}
.coursedtitle {
    z-index: 2;
    font-size: 7.3rem;
    font-family: "Archivo Black", sans-serif;
    color: #FCB11A;
    text-align: left;
    grid-column: 2/10;
    grid-row: 2;
    margin-top: 246px;
}
.coursed-p-a {
    color: white;
    font-family: "Shippori Antique", sans-serif;
    opacity: 0.5;
    font-size: 4rem;
    grid-row: 4;
    margin-top: -385px;
    /*    grid-column: 1/7;*/
    width: 590px;
    padding: 20px;
    rotate: 90deg;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: -355px;
    line-height: 0.9;
}
.coursed-p-b {
    color: white;
    font-size: 6rem;
    font-family: "Archivo Black", sans-serif;
    grid-column: 3/10;
    margin-top: -116px;
    margin-left: 30px;
    grid-row: 4;
}
.coursebqr {
    width: 130px;
    grid-row: 4;
    margin-top: -862px;
    grid-column: 11/12;
    z-index: 2;
}
.courseb-qr-p {
    width: 130px;
    grid-row: 4;
    margin-top: -732px;
    grid-column: 11/12;
    z-index: 2;
    font-size: 0.9rem;
    color: white;
    font-family: "Shippori Antique", sans-serif;
    text-align: center;
}
.coursed-p-c {
    grid-row: 5;
    grid-column: 3/5;
    margin-top: -650px;
    font-size: 1.1rem;
    margin-left: 35px;
    line-height: 2.5;
    color: white;
    font-family: "Shippori Antique", sans-serif;
}
.coursed-p-d {
    grid-row: 5;
    grid-column: 5/7;
    margin-top: -650px;
    font-size: 1.1rem;
    margin-left: 30px;
    line-height: 2.5;
    color: white;
    font-family: "Shippori Antique", sans-serif;
}
.coursed-p-e {
    grid-row: 5;
    grid-column: 8/11;
    margin-top: -650px;
    margin-right: 70px;
    font-size: 1.1rem;
    margin-left: 30px;
    line-height: 2.5;
    color: white;
    line-height: 2.5;
    font-family: "Shippori Antique", sans-serif;
}
.courseb-p-f {
    font-size: 2rem;
    font-family: "Archivo Black", sans-serif;
    grid-row: 6;
    margin-top: -430px;
    grid-column: 3/6;
    margin-left: 35px;
    color: white;
}
.courseb-p-g {
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    grid-row: 7;
    margin-top: -340px;
    grid-column: 3/6;
    margin-left: 35px;
    margin-right: -25px;
    margin-bottom: 40px;
    line-height: 2.5;
    color: white;
}
.courseb-p-h {
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    grid-row: 7;
    margin-top: -340px;
    grid-column: 8/11;
    margin-left: 20px;
    margin-right: -20px;
    line-height: 2.5;
    color: white;
}
.courseb-p-i {
    grid-row: 8;
    grid-column: 3/5;
    margin-left: 35px;
    color: white;
    font-family: "Archivo Black", sans-serif;
    font-size: 3rem;
    line-height: 1.6;
    margin-top: -470px;
}
.courseb-p-j {
    color: white;
    font-family: "Shippori Antique", sans-serif;
    opacity: 0.5;
    font-size: 4rem;
    grid-row: 7;
    grid-column: 11;
    width: 500px;
    rotate: -90deg;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: -110px;
    margin-top: 50px;
    line-height: 0.9;
}
.courseb-p-k {
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    grid-row: 8;
    margin-top: -100px;
    grid-column: 5/12;
    margin-left: 20px;
    margin-left: 70px;
    line-height: 2.5;
    color: white;
}
.courseb-yellow-line {
    grid-row: 9;
    width: 670px;
    grid-column: 4/12;
    margin-left: 120px;
    margin-top: -275px;
}
.courseb-p-l {
    grid-row: 10;
    font-family: "Archivo Black", sans-serif;
    font-size: 3rem;
    line-height: 1.6;
    color: white;
    grid-column: 8/11;
    margin-left: -30px;
    margin-top: -280px;
}
.team-three-b {
    grid-row: 8;
    width: 250px;
    border-radius: 50px;
    grid-column: 3/5;
    margin-top: -35px;
    margin-left: 17px;
}
.courseb-p-m {
    grid-row: 11;
    grid-column: 3/10;
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    line-height: 2.5;
    color: white;
    margin-top: -30px;
    margin-left: 17px;
}
.courseb-p-n {
    grid-row: 12;
    font-family: "Archivo Black", sans-serif;
    font-size: 3rem;
    line-height: 1.6;
    color: white;
    grid-column: 3/5;
    margin-left: 17px;
    z-index: 2;
}
.coursebackc {
    grid-row: 12;
    width: 400px;
    z-index: 1;
    grid-column: 3/5;
    margin-left: -20px;
    margin-top: 10px;
}
.courseb-p-o {
    grid-row: 13;
    grid-column: 3/6;
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    line-height: 2.5;
    color: white;
    margin-left: 17px;
}
.team-two {
    grid-row: 13;
    width: 600px;
    border-radius: 50px;
    grid-column: 7/11;
    margin-top: 20px;
    margin-bottom: 100px;
}
.coursebbackb {
    grid-column: 1 / 13;
    grid-row: 15;
    height: 60vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    margin-top: -70px;
}
.courseb {
    position: absolute;
    top: 0;
    width: 100%;
    height: 500px;
    background: url('../img/team1.jpg') center center fixed;
    background-size: cover;
    filter: blur(4px);
    z-index: 1;
    filter: brightness(50%);
}
.coursebtitleb {
    z-index: 2;
    font-size: 7.3rem;
    font-family: "Archivo Black", sans-serif;
    color: #FCB11A;
    text-align: left;
    grid-column: 2/9;
    grid-row: 15;
    margin-top: 200px;
}
.coursebbp2 {
    z-index: 2;
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    color: #363636;
    background-color: #FCB11A;
    grid-row: 15;
    margin-top: 368px;
    grid-column: 11/13;
    margin-left: 35px;
    height: 50px;
    width: 150px;
    border-radius: 50px;
    border: none;
}
.coursebreadless {
    z-index: 2;
    font-size: 1.1rem;
    font-family: "Shippori Antique", sans-serif;
    color: #363636;
    background-color: #FCB11A;
    grid-row: 14;
    margin-top: -130px;
    margin-bottom: 130px;
    grid-column: 11/13;
    margin-left: 35px;
    height: 50px;
    width: 150px;
    border-radius: 50px;
    border: none;
    cursor: pointer;
}

/* Footer */

footer {
    font-family: "Shippori Antique", sans-serif;
    background-color: #363636;
    color: #fff;
    text-align: center;
    padding: 20px 0;
    grid-column: 1/13;
    width: 100%;
    display: flex;
    flex-direction: column;
    background: linear-gradient(0deg, #1D1D1D, #363636);
}
.contact h3 {
    margin-bottom: 25px;
    font-size: 1.4rem;
    font-family: "Archivo Black", sans-serif;
    color: #FCB11A;
}
.contact address {
    font-style: normal;
    line-height: 2.5;
}
.footersocial {
    display: flex;
    justify-content: center;
    grid-gap: 125px;
    margin-top: 55px;
    margin-bottom: 40px;
}
.footerins,
.footerfacebook,
.footerred {
    width: 55px;
    height: auto;
}
footer a {
    text-decoration: none;
    color: #fff;
    cursor: pointer;
}

/* below 600px typically tablet*/

@media only screen and (max-width: 600px) {
    .sm1 {
        grid-column-end: span 12;
    }
    .sm2 {
        grid-column-end: span 6;
    }
    .sm3 {
        grid-column-end: span 4;
    }
    .sm4 {
        grid-column-end: span 3;
    }
    .sm6 {
        grid-column-end: span 2;
    }
    .sm12 {
        grid-column-end: span 1;
    }
    /*
    header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        padding: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        z-index: 100;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
*/
    .logo img {
        width: 100px;
        height: auto;
        margin-left: 10;
    }
    .profile img {
        display: grid;
        grid-column: 5/6;
        width: 40px;
        height: 40px;
    }
    /* Burger Navigation */
    #nav-trigger {
        display: none;
    }
    label[for="nav-trigger"] {
        display: block;
        position: fixed;
        right: 15px;
        top: 20px;
        z-index: 200;
        cursor: pointer;
        font-family: "Allerta Stencil", sans-serif;
        color: #FCB11A;
        font-size: 16px;
    }
    .navigation {
        display: flex;
        flex-direction: column;
        position: fixed;
        /*
        top: 0;
        right: -200px;
        width: 200px;
        height: 100vh;
*/
        background: #fff;
        transition: transform 0.4s ease-in-out;
        z-index: 150;
        box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
    }
    .nav-trigger:checked ~ .navigation {
        transform: translateX(-200px);
    }
    nav ul {
        list-style-type: none;
        padding: 60px 0 0;
        margin: 0;
        width: 150px;
        height: 100vh;
        position: fixed;
        top: 0;
        right: -200px;
        background: #fff;
        z-index: 150;
        box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
    }
    .navigation li {
        width: 100%;
    }
    .navigation a {
        display: block;
        width: 100%;
        padding: 12px 20px;
        color: #333;
        text-decoration: none;
        font-family: "Allerta Stencil", sans-serif;
    }
    .navigation a:hover {
        background: #FCB11A;
        color: #fff;
    }
    .down {
        display: none;
        /* Hide dropdown arrows on mobile */
    }
    /* Overlay when menu is open */
    .nav-trigger:checked ~ .overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 120;
    }
}

/* above 601px typically tablet*/

@media only screen and (min-width: 601px) {
    .md1 {
        grid-column-end: span 12;
    }
    .md2 {
        grid-column-end: span 6;
    }
    .md3 {
        grid-column-end: span 4;
    }
    .md4 {
        grid-column-end: span 3;
    }
    .md6 {
        grid-column-end: span 2;
    }
    .md12 {
        grid-column-end: span 1;
    }
    .twothird {
        grid-column-end: span 8;
    }
}

/* above 1025px typically desktop*/

@media only screen and (min-width: 1025px) {
    .col1 {
        grid-column-end: span 12;
    }
    .col2 {
        grid-column-end: span 6;
    }
    .col3 {
        grid-column-end: span 4;
    }
    .col4 {
        grid-column-end: span 3;
    }
    .col6 {
        grid-column-end: span 2;
    }
    .col12 {
        grid-column-end: span 1;
    }
    .twothird {
        grid-column-end: span 8;
    }
}
