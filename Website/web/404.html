<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Page Not Found</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .container {
            max-width: 600px;
            padding: 2rem;
        }

        .error-code {
            font-size: 8rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin-bottom: 1rem;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-20px);
            }
            60% {
                transform: translateY(-10px);
            }
        }

        .error-message {
            font-size: 2rem;
            margin-bottom: 1rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .error-description {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .home-button {
            display: inline-block;
            padding: 1rem 2rem;
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: bold;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.3);
        }

        .home-button:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .search-container {
            margin: 2rem 0;
        }

        .search-box {
            padding: 1rem;
            border: none;
            border-radius: 25px;
            width: 100%;
            max-width: 400px;
            font-size: 1rem;
            text-align: center;
            background: rgba(255,255,255,0.9);
            color: #333;
        }

        .search-box::placeholder {
            color: #666;
        }

        .helpful-links {
            margin-top: 2rem;
        }

        .helpful-links a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            margin: 0 1rem;
            font-size: 1.1rem;
            transition: color 0.3s ease;
        }

        .helpful-links a:hover {
            color: white;
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .error-code {
                font-size: 5rem;
            }
            
            .error-message {
                font-size: 1.5rem;
            }
            
            .container {
                padding: 1rem;
            }
            
            .helpful-links a {
                display: block;
                margin: 0.5rem 0;
            }
        }
    </style>
</head>
<body style="min-height:100vh;">
    <div class="container" style="min-height:100vh;">
        <div class="error-code">404</div>
        <h1 class="error-message">Oops! Page Not Found</h1>
        <p class="error-description">
            The page you're looking for seems to have wandered off into the digital void. 
            Don't worry, it happens to the best of us!
        </p>
        
        <div class="search-container">
            <input type="text" class="search-box" placeholder="Search our site..." id="searchBox">
        </div>
        
        <a href="/" class="home-button">🏠 Take Me Home</a>
        
        <div class="helpful-links">
            <p>Or try these popular pages:</p>
            <a href="/about">About</a>
            <a href="/contact">Contact</a>
            <a href="/blog">Blog</a>
            <a href="/services">Services</a>
        </div>
    </div>

    <script>
        // Simple search functionality
        document.getElementById('searchBox').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const query = this.value.trim();
                if (query) {
                    // Redirect to your search page or Google site search
                    window.location.href = `/?search=${encodeURIComponent(query)}`;
                    // Alternative: Google site search
                    // window.open(`https://www.google.com/search?q=site:${window.location.hostname} ${encodeURIComponent(query)}`, '_blank');
                }
            }
        });

        // Optional: Log 404 errors for analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', 'page_not_found', {
                'page_location': window.location.href,
                'page_referrer': document.referrer
            });
        }
    </script>
</body>
</html>