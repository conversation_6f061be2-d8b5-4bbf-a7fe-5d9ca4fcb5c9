<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>STEMBLOCK - Dashboard</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Allerta+Stencil&family=Archivo+Black&family=Shippori+Antique&display=swap" rel="stylesheet">
    <style>
        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .welcome-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
        }
        .course-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .progress-bar {
            width: 100%;
            height: 10px;
            background: #e9ecef;
            border-radius: 5px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: #28a745;
            transition: width 0.3s ease;
        }
        .continue-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        .activity-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-bottom: 1px solid #e9ecef;
        }
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }
        .activity-icon.course { background: #007bff; color: white; }
        .activity-icon.achievement { background: #ffc107; color: white; }
        .activity-icon.project { background: #28a745; color: white; }
    </style>
</head>

<body>
    <div id="container" style="min-height:100vh;">
        <div class="logo">
            <img src="img/stem_block_logo.png" alt="logo">
        </div>
        <div class="profile">
            <!-- Will be updated by auth-manager.js -->
            <img src="img/profile.png" alt="profile">
        </div>

        <header>
            <nav>
                <input type="checkbox" id="nav-trigger" class="nav-trigger" />
                <label class="menu" for="nav-trigger">
                    &#9776; Menu
                </label>

                <ul class="navigation">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="coursea.html">Course</a>
                        <img class="down" src="img/down.png" alt="down"></li>
                    <li><a href="aboutus.html">About Us</a>
                        <img class="down" src="img/down.png" alt="down"></li>
                    <li><a href="contact.html">Contact Us</a>
                        <img class="down" src="img/down.png" alt="down"></li>
                    <li><a href="dashboard.html" class="current">Dashboard</a></li>
                </ul>
            </nav>
        </header>

        <div class="dashboard-container">
            <!-- Welcome Section -->
            <div class="welcome-section">
                <h1 id="welcomeMessage">Welcome to Your Dashboard!</h1>
                <p>Track your learning progress and continue your STEM journey</p>
            </div>

            <!-- User Statistics -->
            <div id="userStats" class="stats-grid">
                <!-- Will be populated by dashboard.js -->
                <div class="stat-card">
                    <h3>Loading...</h3>
                    <div class="stat-number">⏳</div>
                    <p>Please wait</p>
                </div>
            </div>

            <!-- Course Progress -->
            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 30px;">
                <div>
                    <h2>Your Courses</h2>
                    <div id="courseProgress">
                        <!-- Will be populated by dashboard.js -->
                        <div class="course-card">
                            <h3>Loading courses...</h3>
                            <p>⏳ Please wait while we load your course data.</p>
                        </div>
                    </div>
                </div>

                <!-- Recent Activities -->
                <div>
                    <h2>Recent Activity</h2>
                    <div id="recentActivities" style="background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); padding: 20px;">
                        <!-- Will be populated by dashboard.js -->
                        <div class="activity-item">
                            <div class="activity-icon course">⏳</div>
                            <div>
                                <p>Loading activities...</p>
                                <small>Please wait</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Profile Section -->
            <div style="margin-top: 30px; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h2>Profile Information</h2>
                <div id="profileInfo">
                    <p><strong>Username:</strong> <span id="userName">Loading...</span></p>
                    <p><strong>Email:</strong> <span id="userEmail">Loading...</span></p>
                    <p><strong>Role:</strong> <span id="userRole">Loading...</span></p>
                    <button id="editProfileBtn" class="continue-btn">Edit Profile</button>
                </div>
                
                <div id="profileEdit" style="display: none;">
                    <form id="profileEditForm">
                        <div style="margin-bottom: 15px;">
                            <label>Username:</label>
                            <input type="text" name="username" style="width: 100%; padding: 8px; margin-top: 5px;">
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label>Email:</label>
                            <input type="email" name="email" style="width: 100%; padding: 8px; margin-top: 5px;">
                        </div>
                        <button type="button" id="saveProfileBtn" class="continue-btn">Save Changes</button>
                        <button type="button" onclick="toggleProfileEdit()" style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-left: 10px;">Cancel</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <footer style="width:100%; margin-top: 50px;">
        <div class="contact">
            <h3>Contact Us</h3>
            <address>
               Phone: (506)9985996<br>
               Address: 50 Crowther Ln suite 140, Fredericton, NB E3C 0J1<br>
               <a href="mailto:<EMAIL>">Email: <EMAIL></a>
            </address>
        </div>
        <div class="footersocial">
            <img class="footerins" src="img/inswhite.png" alt="inswhite">
            <img class="footerfacebook" src="img/facebook.png" alt="facebook">
        </div>
    </footer>

    <!-- JavaScript files -->
    <script src="js/env.js"></script>
    <script src="js/config.js"></script>
    <script src="js/auth-manager.js"></script>
    <script src="js/dashboard.js"></script>
</body>

</html>
