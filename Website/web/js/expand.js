document.addEventListener('DOMContentLoaded', function () {
    // For coursea.html navigation
    var courseap1 = document.querySelector('.courseap1');
    var courseap2 = document.querySelector('.courseap2');
    if (courseap1) {
        courseap1.addEventListener('click', function () {
            window.location.href = 'courseb.html';
        });
    }
    if (courseap2) {
        courseap2.addEventListener('click', function () {
            window.location.href = 'coursec.html';
        });
    }

    // For courseb.html expand/collapse
    var readMoreBtn = document.querySelector('.coursebbp2');
    var readLessBtn = document.querySelector('.coursebreadless');
    var expandedSection = document.querySelector('.coursebbackb');
    var expandedTitle = document.querySelector('.coursebtitleb');
    if (expandedSection && expandedTitle && readMoreBtn && readLessBtn) {
        expandedSection.style.display = 'none';
        expandedTitle.style.display = 'none';
        readLessBtn.style.display = 'none';
        readMoreBtn.addEventListener('click', function () {
            expandedSection.style.display = 'block';
            expandedTitle.style.display = 'block';
            readLessBtn.style.display = 'inline-block';
            readMoreBtn.style.display = 'none';
        });
        readLessBtn.addEventListener('click', function () {
            // On Read less, redirect to coursea.html
            window.location.href = 'coursea.html';
        });
    } else if (readLessBtn) {
        // For coursec.html or any page with only coursebreadless
        readLessBtn.addEventListener('click', function () {
            window.location.href = 'coursea.html';
        });
    }
});
