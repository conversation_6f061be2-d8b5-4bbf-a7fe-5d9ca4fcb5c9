import React, { useState } from "react";

export default function RegisterForm() {
  const [registerData, setRegisterData] = useState({ username: "", email: "", password: "", role: "student" });
  const [registerResult, setRegisterResult] = useState("");

  const handleRegisterChange = (e) => {
    setRegisterData({ ...registerData, [e.target.name]: e.target.value });
  };

  const handleRegister = async (e) => {
    e.preventDefault();
    setRegisterResult("");
    const res = await fetch("/register", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(registerData),
    });
    const data = await res.json();
    if (res.ok) {
      setRegisterResult("✅ " + (data.message || "Registered!") + (data.verification_link ? "\nVerify: " + data.verification_link : ""));
    } else {
      setRegisterResult("❌ " + (data.error || JSON.stringify(data)));
    }
  };

  return (
    <div>
      <h2>Register</h2>
      <form onSubmit={handleRegister}>
        <input name="username" placeholder="Username" value={registerData.username} onChange={handleRegisterChange} required />
        <input name="email" placeholder="Email" value={registerData.email} onChange={handleRegisterChange} required />
        <input name="password" type="password" placeholder="Password" value={registerData.password} onChange={handleRegisterChange} required />
        <select name="role" value={registerData.role} onChange={handleRegisterChange}>
          <option value="student">Student</option>
          <option value="coach">Coach</option>
        </select>
        <button type="submit">Register</button>
      </form>
      <pre>{registerResult}</pre>
    </div>
  );
}
