// dashboard.js - <PERSON>les dashboard functionality and data loading

document.addEventListener('DOMContentLoaded', function () {
    // Check authentication first
    checkAuthenticationAndLoadDashboard();

    // Setup dashboard functionality
    setupDashboard();
});

// Check authentication and load dashboard
async function checkAuthenticationAndLoadDashboard() {
    try {
        const response = await fetch(Config.getApiUrl('dashboardAuth'), {
            method: "GET",
            ...Config.getRequestConfig()
        });

        if (response.ok) {
            const data = await response.json();
            loadDashboardData(data.user);
        } else {
            // Not authenticated, redirect to login
            window.location.href = 'login.html?redirect=' + encodeURIComponent(window.location.pathname);
        }
    } catch (error) {
        console.error('Authentication check failed:', error);
        showErrorMessage('Unable to verify authentication. Please try logging in again.');
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 3000);
    }
}

// Load dashboard data
function loadDashboardData(user) {
    // Update user info
    updateUserInfo(user);
    
    // Load course progress
    loadCourseProgress();
    
    // Load recent activities
    loadRecentActivities();
    
    // Load statistics
    loadUserStatistics();
    
    // Setup interactive elements
    setupInteractiveElements();
}

// Update user information display
function updateUserInfo(user) {
    const userNameElement = document.getElementById('userName');
    const userEmailElement = document.getElementById('userEmail');
    const userRoleElement = document.getElementById('userRole');
    const welcomeMessage = document.getElementById('welcomeMessage');

    if (userNameElement) userNameElement.innerText = user.username || 'Student';
    if (userEmailElement) userEmailElement.innerText = user.email || '';
    if (userRoleElement) userRoleElement.innerText = user.role || 'Student';
    if (welcomeMessage) welcomeMessage.innerText = `Welcome back, ${user.username || 'Student'}!`;
}

// Load course progress data
async function loadCourseProgress() {
    const progressContainer = document.getElementById('courseProgress');
    if (!progressContainer) return;

    try {
        // Mock data - replace with actual API call
        const mockCourses = [
            { id: 1, title: "VEX IQ Fundamentals", progress: 75, totalLessons: 12, completedLessons: 9 },
            { id: 2, title: "Robot Programming Basics", progress: 45, totalLessons: 8, completedLessons: 4 },
            { id: 3, title: "Advanced Robotics", progress: 20, totalLessons: 15, completedLessons: 3 }
        ];

        progressContainer.innerHTML = mockCourses.map(course => `
            <div class="course-card">
                <h3>${course.title}</h3>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${course.progress}%"></div>
                </div>
                <p>${course.completedLessons}/${course.totalLessons} lessons completed (${course.progress}%)</p>
                <button class="continue-btn" onclick="continueCourse(${course.id})">Continue Learning</button>
            </div>
        `).join('');

    } catch (error) {
        console.error('Failed to load course progress:', error);
        progressContainer.innerHTML = '<p>Unable to load course progress. Please try again later.</p>';
    }
}

// Load recent activities
async function loadRecentActivities() {
    const activitiesContainer = document.getElementById('recentActivities');
    if (!activitiesContainer) return;

    try {
        // Mock data - replace with actual API call
        const mockActivities = [
            { type: 'course', message: 'Completed lesson "Introduction to VEX IQ"', time: '2 hours ago' },
            { type: 'achievement', message: 'Earned "First Robot" badge', time: '1 day ago' },
            { type: 'project', message: 'Submitted project "Line Following Robot"', time: '3 days ago' },
            { type: 'course', message: 'Started "Advanced Programming" course', time: '1 week ago' }
        ];

        activitiesContainer.innerHTML = mockActivities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon ${activity.type}">
                    ${getActivityIcon(activity.type)}
                </div>
                <div class="activity-content">
                    <p>${activity.message}</p>
                    <small>${activity.time}</small>
                </div>
            </div>
        `).join('');

    } catch (error) {
        console.error('Failed to load recent activities:', error);
        activitiesContainer.innerHTML = '<p>Unable to load recent activities. Please try again later.</p>';
    }
}

// Load user statistics
async function loadUserStatistics() {
    const statsContainer = document.getElementById('userStats');
    if (!statsContainer) return;

    try {
        // Mock data - replace with actual API call
        const mockStats = {
            totalCourses: 3,
            completedCourses: 1,
            totalHours: 24,
            badges: 5,
            projects: 8,
            rank: 'Intermediate'
        };

        statsContainer.innerHTML = `
            <div class="stat-card">
                <h3>Courses</h3>
                <div class="stat-number">${mockStats.completedCourses}/${mockStats.totalCourses}</div>
                <p>Completed</p>
            </div>
            <div class="stat-card">
                <h3>Learning Hours</h3>
                <div class="stat-number">${mockStats.totalHours}</div>
                <p>Total Hours</p>
            </div>
            <div class="stat-card">
                <h3>Badges</h3>
                <div class="stat-number">${mockStats.badges}</div>
                <p>Earned</p>
            </div>
            <div class="stat-card">
                <h3>Projects</h3>
                <div class="stat-number">${mockStats.projects}</div>
                <p>Completed</p>
            </div>
        `;

    } catch (error) {
        console.error('Failed to load user statistics:', error);
        statsContainer.innerHTML = '<p>Unable to load statistics. Please try again later.</p>';
    }
}

// Setup interactive elements
function setupInteractiveElements() {
    // Setup course navigation
    setupCourseNavigation();
    
    // Setup profile editing
    setupProfileEditing();
    
    // Setup settings
    setupSettings();
}

// Setup course navigation
function setupCourseNavigation() {
    const courseButtons = document.querySelectorAll('.course-btn');
    courseButtons.forEach(button => {
        button.addEventListener('click', function() {
            const courseId = this.dataset.courseId;
            navigateToCourse(courseId);
        });
    });
}

// Setup profile editing
function setupProfileEditing() {
    const editProfileBtn = document.getElementById('editProfileBtn');
    if (editProfileBtn) {
        editProfileBtn.addEventListener('click', function() {
            toggleProfileEdit();
        });
    }

    const saveProfileBtn = document.getElementById('saveProfileBtn');
    if (saveProfileBtn) {
        saveProfileBtn.addEventListener('click', function() {
            saveProfileChanges();
        });
    }
}

// Setup settings
function setupSettings() {
    const settingsForm = document.getElementById('settingsForm');
    if (settingsForm) {
        settingsForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveSettings();
        });
    }
}

// Helper functions
function getActivityIcon(type) {
    const icons = {
        course: '📚',
        achievement: '🏆',
        project: '🛠️',
        quiz: '📝',
        video: '🎥'
    };
    return icons[type] || '📌';
}

function continueCourse(courseId) {
    // Navigate to course page
    window.location.href = `coursea.html?id=${courseId}`;
}

function navigateToCourse(courseId) {
    window.location.href = `coursea.html?id=${courseId}`;
}

function toggleProfileEdit() {
    const profileInfo = document.getElementById('profileInfo');
    const profileEdit = document.getElementById('profileEdit');
    
    if (profileInfo && profileEdit) {
        profileInfo.style.display = profileInfo.style.display === 'none' ? 'block' : 'none';
        profileEdit.style.display = profileEdit.style.display === 'none' ? 'block' : 'none';
    }
}

async function saveProfileChanges() {
    const form = document.getElementById('profileEditForm');
    if (!form) return;

    const formData = new FormData(form);
    const profileData = Object.fromEntries(formData);

    try {
        const response = await fetch(Config.getApiUrl('updateProfile'), {
            method: "POST",
            ...Config.getRequestConfig(),
            body: JSON.stringify(profileData)
        });

        if (response.ok) {
            showSuccessMessage('Profile updated successfully!');
            toggleProfileEdit();
            // Reload user data
            checkAuthenticationAndLoadDashboard();
        } else {
            const data = await response.json();
            showErrorMessage(data.error || 'Failed to update profile');
        }
    } catch (error) {
        showErrorMessage('Unable to update profile. Please try again.');
    }
}

async function saveSettings() {
    const form = document.getElementById('settingsForm');
    if (!form) return;

    const formData = new FormData(form);
    const settings = Object.fromEntries(formData);

    try {
        const response = await fetch(Config.getApiUrl('updateSettings'), {
            method: "POST",
            ...Config.getRequestConfig(),
            body: JSON.stringify(settings)
        });

        if (response.ok) {
            showSuccessMessage('Settings saved successfully!');
        } else {
            const data = await response.json();
            showErrorMessage(data.error || 'Failed to save settings');
        }
    } catch (error) {
        showErrorMessage('Unable to save settings. Please try again.');
    }
}

// Utility functions
function showSuccessMessage(message) {
    const successDiv = document.createElement('div');
    successDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        z-index: 1000;
        font-weight: bold;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    `;
    successDiv.innerText = "✅ " + message;
    document.body.appendChild(successDiv);

    setTimeout(() => {
        if (document.body.contains(successDiv)) {
            document.body.removeChild(successDiv);
        }
    }, 3000);
}

function showErrorMessage(message) {
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #dc3545;
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        z-index: 1000;
        font-weight: bold;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    `;
    errorDiv.innerText = "❌ " + message;
    document.body.appendChild(errorDiv);

    setTimeout(() => {
        if (document.body.contains(errorDiv)) {
            document.body.removeChild(errorDiv);
        }
    }, 5000);
}
