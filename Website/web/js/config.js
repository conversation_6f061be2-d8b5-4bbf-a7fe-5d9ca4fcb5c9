// config.js - Environment-based configuration for frontend

// Configuration object that adapts based on environment
const Config = {
    // Determine environment
    isDevelopment: window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1',
    
    // Get backend URL based on environment
    getBackendURL: function() {
        // Check if running locally
        if (this.isDevelopment) {
            // For local development, use environment variable or default to localhost:8080
            return window.ENV_BACKEND_URL || 'http://localhost:8080';
        } else {
            // For production, use the cloud backend
            return 'https://stemblock-login-gljgs.ondigitalocean.app';
        }
    },

    // Get frontend URL
    getFrontendURL: function() {
        if (this.isDevelopment) {
            return window.ENV_FRONTEND_URL || `${window.location.protocol}//${window.location.host}`;
        } else {
            return 'https://www.stemblock.ca';
        }
    },

    // API endpoints
    endpoints: {
        login: '/login',
        register: '/register',
        forgotPassword: '/forgot-password',
        resetPassword: '/reset-password',
        verifyEmail: '/verify-email',
        dashboardAuth: '/dashboard-auth',
        resendVerification: '/resend-verification',
        contact: '/contact',
        logout: '/logout',
        updateProfile: '/update-profile',
        updateSettings: '/update-settings',
        validateResetToken: '/validate-reset-token'
    },

    // Build full API URL
    getApiUrl: function(endpoint) {
        return this.getBackendURL() + this.endpoints[endpoint];
    },

    // Request configuration
    getRequestConfig: function(options = {}) {
        return {
            credentials: 'include', // Always include cookies for JWT
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };
    },

    // Debug logging (only in development)
    log: function(message, data = null) {
        if (this.isDevelopment) {
            console.log(`[STEMBlock Config] ${message}`, data || '');
        }
    },

    // Initialize configuration
    init: function() {
        this.log('Initializing configuration', {
            environment: this.isDevelopment ? 'development' : 'production',
            backendURL: this.getBackendURL(),
            frontendURL: this.getFrontendURL()
        });

        // Set global config for other scripts
        window.STEMBlockConfig = this;
    }
};

// Auto-initialize when script loads
Config.init();

// Export for use in other scripts
window.Config = Config;
