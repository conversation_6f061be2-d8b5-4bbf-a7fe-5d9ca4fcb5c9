// auth-manager.js - Global authentication state management

class AuthManager {
    constructor() {
        this.user = null;
        this.isAuthenticated = false;
        this.checkInterval = null;
        this.init();
    }

    // Initialize authentication manager
    async init() {
        await this.checkAuthStatus();
        this.updateUI();
        this.startPeriodicCheck();
        this.setupLogoutHandler();
    }

    // Check current authentication status
    async checkAuthStatus() {
        try {
            const response = await fetch(Config.getApiUrl('dashboardAuth'), {
                method: "GET",
                ...Config.getRequestConfig()
            });

            if (response.ok) {
                const data = await response.json();
                this.user = data.user;
                this.isAuthenticated = true;
                this.onAuthSuccess();
            } else {
                this.user = null;
                this.isAuthenticated = false;
                this.onAuthFailure();
            }
        } catch (error) {
            console.error('Auth check failed:', error);
            this.user = null;
            this.isAuthenticated = false;
        }

        return this.isAuthenticated;
    }

    // Handle successful authentication
    onAuthSuccess() {
        // Update navigation
        this.updateNavigation();
        
        // Update user display
        this.updateUserDisplay();
        
        // Show authenticated content
        this.showAuthenticatedContent();
    }

    // Handle authentication failure
    onAuthFailure() {
        // Update navigation
        this.updateNavigation();
        
        // Hide authenticated content
        this.hideAuthenticatedContent();
    }

    // Update navigation based on auth status
    updateNavigation() {
        const profileElements = document.querySelectorAll('.profile');
        const loginLinks = document.querySelectorAll('a[href="login.html"]');
        const logoutButtons = document.querySelectorAll('.logout-btn, #logout-btn');

        if (this.isAuthenticated) {
            // Show profile, hide login links
            profileElements.forEach(el => el.style.display = 'block');
            loginLinks.forEach(el => el.style.display = 'none');
            logoutButtons.forEach(el => el.style.display = 'block');
        } else {
            // Hide profile, show login links
            profileElements.forEach(el => el.style.display = 'block');
            loginLinks.forEach(el => el.style.display = 'block');
            logoutButtons.forEach(el => el.style.display = 'none');
        }
    }

    // Update user display elements
    updateUserDisplay() {
        const userNameElements = document.querySelectorAll('.user-name, #user-name');
        const userEmailElements = document.querySelectorAll('.user-email, #user-email');

        if (this.isAuthenticated && this.user) {
            userNameElements.forEach(el => {
                el.textContent = this.user.username || this.user.email || 'User';
            });
            userEmailElements.forEach(el => {
                el.textContent = this.user.email || '';
            });
        } else {
            userNameElements.forEach(el => el.textContent = 'Guest');
            userEmailElements.forEach(el => el.textContent = '');
        }
    }

    // Show content that requires authentication
    showAuthenticatedContent() {
        const authContent = document.querySelectorAll('.auth-required');
        authContent.forEach(el => el.style.display = 'block');
    }

    // Hide content that requires authentication
    hideAuthenticatedContent() {
        const authContent = document.querySelectorAll('.auth-required');
        authContent.forEach(el => el.style.display = 'none');
    }

    // Update UI elements
    updateUI() {
        // This method can be overridden by specific pages
        if (typeof updatePageUI === 'function') {
            updatePageUI(this.isAuthenticated, this.user);
        }
    }

    // Start periodic authentication check
    startPeriodicCheck() {
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
        }

        // Check every 5 minutes
        this.checkInterval = setInterval(() => {
            this.checkAuthStatus();
        }, window.ENV_SESSION_CHECK_INTERVAL || 5 * 60 * 1000);
    }

    // Setup logout handler
    setupLogoutHandler() {
        const logoutButtons = document.querySelectorAll('.logout-btn, #logout-btn');
        logoutButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.logout();
            });
        });
    }

    // Logout user
    async logout() {
        try {
            const response = await fetch(Config.getApiUrl('logout'), {
                method: 'POST',
                ...Config.getRequestConfig()
            });

            // Clear local state regardless of response
            this.user = null;
            this.isAuthenticated = false;
            this.onAuthFailure();

            // Redirect to home page
            window.location.href = 'index.html';
        } catch (error) {
            console.error('Logout error:', error);
            // Still clear local state and redirect
            this.user = null;
            this.isAuthenticated = false;
            this.onAuthFailure();
            window.location.href = 'index.html';
        }
    }

    // Get current user
    getUser() {
        return this.user;
    }

    // Check if user is authenticated
    isUserAuthenticated() {
        return this.isAuthenticated;
    }

    // Require authentication (redirect to login if not authenticated)
    requireAuth() {
        if (!this.isAuthenticated) {
            window.location.href = 'login.html';
            return false;
        }
        return true;
    }
}

// Create global auth manager instance
window.authManager = new AuthManager();

// Export for use in other scripts
window.AuthManager = AuthManager;
