// email-verification.js - Handles email verification functionality

document.addEventListener('DOMContentLoaded', function () {
    // Get token from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    const email = urlParams.get('email');

    // Auto-verify if token is present in URL
    if (token) {
        verifyEmailWithToken(token);
    }

    // Handle manual verification form if present
    const verifyForm = document.getElementById('verifyForm');
    if (verifyForm) {
        verifyForm.addEventListener('submit', async function (e) {
            e.preventDefault();
            const form = e.target;
            const verificationToken = form.token.value;
            
            if (verificationToken) {
                await verifyEmailWithToken(verificationToken);
            }
        });
    }

    // Handle resend verification link
    const resendButton = document.getElementById('resendVerification');
    if (resendButton) {
        resendButton.addEventListener('click', async function (e) {
            e.preventDefault();
            await resendVerificationEmail();
        });
    }

    // Auto-verify function
    async function verifyEmailWithToken(verificationToken) {
        const result = document.getElementById('verificationResult');
        
        if (result) {
            result.innerText = "⏳ Verifying your email...";
        }

        try {
            const response = await fetch(`${Config.getApiUrl('verifyEmail')}?token=${encodeURIComponent(verificationToken)}`, {
                method: "GET",
                ...Config.getRequestConfig()
            });

            const data = await response.json();

            if (response.ok) {
                // Verification successful
                if (result) {
                    result.innerText = "✅ " + (data.message || "Email verified successfully! You can now log in.");
                    result.style.color = "green";
                }

                // Show success message and redirect
                showSuccessMessage();
                
                // Redirect to login after delay
                setTimeout(() => {
                    window.location.href = "login.html";
                }, 3000);

            } else {
                // Verification failed
                if (result) {
                    result.innerText = "❌ " + (data.error || "Email verification failed. Please try again.");
                    result.style.color = "red";
                }
                showErrorMessage(data.error);
            }
        } catch (error) {
            // Network error
            if (result) {
                result.innerText = "❌ Unable to connect to server. Please check your internet connection.";
                result.style.color = "red";
            }
        }
    }

    // Resend verification email
    async function resendVerificationEmail() {
        const result = document.getElementById('resendResult');
        
        if (result) {
            result.innerText = "⏳ Sending verification email...";
        }

        try {
            // Get email from URL or prompt user
            let emailAddress = email;
            if (!emailAddress) {
                emailAddress = prompt("Please enter your email address:");
                if (!emailAddress) return;
            }

            const response = await fetch(Config.getApiUrl('resendVerification'), {
                method: "POST",
                ...Config.getRequestConfig(),
                body: JSON.stringify({
                    email: emailAddress
                })
            });

            const data = await response.json();

            if (response.ok) {
                // Resend successful
                if (result) {
                    result.innerText = "✅ " + (data.message || "Verification email sent! Please check your inbox.");
                    result.style.color = "green";
                }
            } else {
                // Resend failed
                if (result) {
                    result.innerText = "❌ " + (data.error || "Failed to send verification email. Please try again.");
                    result.style.color = "red";
                }
            }
        } catch (error) {
            // Network error
            if (result) {
                result.innerText = "❌ Unable to connect to server. Please check your internet connection.";
                result.style.color = "red";
            }
        }
    }

    // Show success message
    function showSuccessMessage() {
        const successDiv = document.createElement('div');
        successDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            z-index: 1000;
            font-weight: bold;
        `;
        successDiv.innerText = "✅ Email verified successfully! Redirecting to login...";
        document.body.appendChild(successDiv);

        // Remove after 5 seconds
        setTimeout(() => {
            document.body.removeChild(successDiv);
        }, 5000);
    }

    // Show error message
    function showErrorMessage(errorText) {
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #dc3545;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            z-index: 1000;
            font-weight: bold;
        `;
        errorDiv.innerText = "❌ " + (errorText || "Verification failed");
        document.body.appendChild(errorDiv);

        // Remove after 5 seconds
        setTimeout(() => {
            document.body.removeChild(errorDiv);
        }, 5000);
    }

    // Update UI based on verification status
    function updateVerificationUI(isVerified) {
        const verifyButton = document.querySelector('.verification-code-btn');
        const resendLink = document.querySelector('.verification-resendb a');
        
        if (isVerified) {
            if (verifyButton) {
                verifyButton.innerText = "✅ Verified - Continue to Login";
                verifyButton.onclick = () => window.location.href = "login.html";
            }
            if (resendLink) {
                resendLink.style.display = "none";
            }
        }
    }
});
