// forgotpassword.js - <PERSON>les password reset functionality

document.addEventListener('DOMContentLoaded', function () {
    // Use Config system for backend URL

    // <PERSON>le forgot password request form
    const forgotPasswordForm = document.getElementById('forgotPasswordForm');
    if (forgotPasswordForm) {
        forgotPasswordForm.addEventListener('submit', async function (e) {
            e.preventDefault();
            const form = e.target;
            const result = document.getElementById('forgotPasswordResult');

            // Clear previous results
            if (result) {
                result.innerText = "⏳ Sending reset link...";
            }

            try {
                const email = form.email.value;

                const response = await fetch(Config.getApiUrl('forgotPassword'), {
                    method: "POST",
                    ...Config.getRequestConfig(),
                    body: JSON.stringify({
                        email: email
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    // Request successful
                    if (result) {
                        result.innerText = "✅ " + (data.message || "Password reset link sent to your email!");
                    }

                    // Clear form
                    form.reset();
                } else {
                    // Handle errors
                    if (result) {
                        if (data.validation_errors) {
                            let errorMessage = "❌ Please fix the following errors:\n";
                            data.validation_errors.forEach(err => {
                                errorMessage += `• ${err.field}: ${err.message}\n`;
                            });
                            result.innerText = errorMessage;
                        } else {
                            result.innerText = "❌ " + (data.error || "Failed to send reset link. Please try again.");
                        }
                    }
                }
            } catch (error) {
                // Handle network errors
                if (result) {
                    result.innerText = "❌ Unable to connect to server. Please check your internet connection.";
                }
            }
        });
    }

    // Handle password reset form (for reset page)
    const resetPasswordForm = document.getElementById('resetPasswordForm');
    if (resetPasswordForm) {
        resetPasswordForm.addEventListener('submit', async function (e) {
            e.preventDefault();
            const form = e.target;
            const result = document.getElementById('resetPasswordResult');

            // Clear previous results
            if (result) {
                result.innerText = "";
            }

            try {
                const token = form.token.value;
                const password = form.password.value;
                const confirmPassword = form.confirm_password.value;

                // Client-side validation
                if (password !== confirmPassword) {
                    if (result) {
                        result.innerText = "❌ Passwords do not match.";
                    }
                    return;
                }

                // Show loading
                if (result) {
                    result.innerText = "⏳ Resetting password...";
                }

                const response = await fetch(Config.getApiUrl('resetPassword'), {
                    method: "POST",
                    ...Config.getRequestConfig(),
                    body: JSON.stringify({
                        token: token,
                        password: password
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    // Reset successful
                    if (result) {
                        result.innerText = "✅ " + (data.message || "Password reset successful! Redirecting to login...");
                    }

                    // Redirect to login after short delay
                    setTimeout(() => {
                        window.location.href = "login.html";
                    }, 2000);
                } else {
                    // Handle errors
                    if (result) {
                        if (data.validation_errors) {
                            let errorMessage = "❌ Please fix the following errors:\n";
                            data.validation_errors.forEach(err => {
                                errorMessage += `• ${err.field}: ${err.message}\n`;
                            });
                            result.innerText = errorMessage;
                        } else {
                            result.innerText = "❌ " + (data.error || "Password reset failed. Please try again.");
                        }
                    }
                }
            } catch (error) {
                // Handle network errors
                if (result) {
                    result.innerText = "❌ Unable to connect to server. Please check your internet connection.";
                }
            }
        });
    }

    // Handle show/hide password functionality
    const showPasswordCheckbox = document.getElementById('showPassword');
    if (showPasswordCheckbox) {
        const newPasswordInput = document.getElementById('newPasswordInput') || document.getElementById('password');
        const confirmPasswordInput = document.getElementById('confirmPasswordInput') || document.getElementById('confirm_password');

        showPasswordCheckbox.addEventListener('change', function () {
            const type = this.checked ? 'text' : 'password';
            if (newPasswordInput) newPasswordInput.type = type;
            if (confirmPasswordInput) confirmPasswordInput.type = type;
        });
    }
});
