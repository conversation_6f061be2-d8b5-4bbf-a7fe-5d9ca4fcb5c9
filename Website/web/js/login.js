// login.js - <PERSON>les login form submission for loginForm

document.addEventListener("DOMContentLoaded", function () {
    const loginForm = document.getElementById("loginForm");
    if (loginForm) {
        loginForm.addEventListener("submit", async function (e) {
            e.preventDefault();
            const form = e.target;
            const result = document.getElementById("loginResult");

            // Clear previous results
            if (result) {
                result.innerText = "⏳ Logging in...";
            }

            try {
                // Get form data - note: HTML uses email field but backend expects username
                const email = form.email.value;
                const password = form.password.value;

                const loginUrl = Config.getApiUrl('login');
                console.log('Login URL:', loginUrl);
                console.log('Request body:', JSON.stringify({
                    username: email,
                    password: password
                }));

                const response = await fetch(loginUrl, {
                    method: "POST",
                    ...Config.getRequestConfig(),
                    body: JSON.stringify({
                        username: email, // Backend expects username but we're using email as username
                        password: password
                    })
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                const responseText = await response.text();
                console.log('Response text:', responseText);

                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (e) {
                    console.error('Failed to parse JSON:', e);
                    console.error('Response was:', responseText);
                    throw new Error('Server returned invalid JSON: ' + responseText.substring(0, 100));
                }

                if (response.ok) {
                    // Login successful
                    if (result) {
                        result.innerText = "✅ " + (data.message || "Login successful! Redirecting...");
                    }

                    // Redirect to home page after short delay
                    setTimeout(() => {
                        window.location.href = "index.html";
                    }, 1500);
                } else {
                    // Handle login errors
                    if (result) {
                        if (data.validation_errors) {
                            let errorMessage = "❌ Please fix the following errors:\n";
                            data.validation_errors.forEach(err => {
                                errorMessage += `• ${err.field}: ${err.message}\n`;
                            });
                            result.innerText = errorMessage;
                        } else {
                            result.innerText = "❌ " + (data.error || "Login failed. Please try again.");
                        }
                    }
                }
            } catch (error) {
                // Handle network errors
                if (result) {
                    result.innerText = "❌ Unable to connect to server. Please check your internet connection.";
                }
            }
        });
    }
});
