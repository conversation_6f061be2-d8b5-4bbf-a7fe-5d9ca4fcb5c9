import React, { useState } from "react";

export default function LoginForm() {
  const [loginData, setLoginData] = useState({ username: "", password: "" });
  const [loginResult, setLoginResult] = useState("");

  const handleLoginChange = (e) => {
    setLoginData({ ...loginData, [e.target.name]: e.target.value });
  };

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoginResult("");
    const res = await fetch("/login", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      credentials: "same-origin",
      body: JSON.stringify(loginData),
    });
    const data = await res.json();
    if (res.ok) {
      window.location = "/dashboard";
    } else {
      setLoginResult("❌ " + (data.error || JSON.stringify(data)));
    }
  };

  return (
    <div>
      <h2>Login</h2>
      <form onSubmit={handleLogin}>
        <input name="username" placeholder="Username" value={loginData.username} onChange={handleLoginChange} required />
        <input name="password" type="password" placeholder="Password" value={loginData.password} onChange={handleLoginChange} required />
        <button type="submit">Login</button>
      </form>
      <pre>{loginResult}</pre>
    </div>
  );
}
