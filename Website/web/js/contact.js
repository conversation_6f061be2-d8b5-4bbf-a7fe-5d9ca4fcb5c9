// contact.js - <PERSON>les contact form submission

document.addEventListener('DOMContentLoaded', function () {
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', async function (e) {
            e.preventDefault();
            const form = e.target;
            const result = document.getElementById('contactResult');

            // Clear previous results
            if (result) {
                result.innerText = "";
            }

            try {
                // Get form data
                const name = form.name.value;
                const email = form.email.value;
                const subject = form.subject.value;
                const message = form.message.value;

                // Client-side validation
                if (!name || !email || !subject || !message) {
                    if (result) {
                        result.innerText = "❌ Please fill in all fields.";
                        result.style.color = "red";
                    }
                    return;
                }

                // Email validation
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    if (result) {
                        result.innerText = "❌ Please enter a valid email address.";
                        result.style.color = "red";
                    }
                    return;
                }

                // Show loading
                if (result) {
                    result.innerText = "⏳ Sending message...";
                    result.style.color = "#007bff";
                }

                const response = await fetch(Config.getApiUrl('contact'), {
                    method: "POST",
                    ...Config.getRequestConfig(),
                    body: JSON.stringify({
                        name: name,
                        email: email,
                        subject: subject,
                        message: message
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    // Message sent successfully
                    if (result) {
                        result.innerText = "✅ " + (data.message || "Message sent successfully! We'll get back to you soon.");
                        result.style.color = "green";
                    }

                    // Clear form
                    form.reset();

                    // Show success notification
                    showSuccessNotification();
                } else {
                    // Handle errors
                    if (result) {
                        if (data.validation_errors) {
                            let errorMessage = "❌ Please fix the following errors:\n";
                            data.validation_errors.forEach(err => {
                                errorMessage += `• ${err.field}: ${err.message}\n`;
                            });
                            result.innerText = errorMessage;
                        } else {
                            result.innerText = "❌ " + (data.error || "Failed to send message. Please try again.");
                        }
                        result.style.color = "red";
                    }
                }
            } catch (error) {
                // Handle network errors
                if (result) {
                    result.innerText = "❌ Unable to connect to server. Please check your internet connection or try again later.";
                    result.style.color = "red";
                }
            }
        });
    }

    // Character counter for message field
    const messageField = document.getElementById('message');
    const charCounter = document.getElementById('charCounter');
    if (messageField && charCounter) {
        messageField.addEventListener('input', function() {
            const remaining = 1000 - this.value.length;
            charCounter.innerText = `${remaining} characters remaining`;
            
            if (remaining < 0) {
                charCounter.style.color = 'red';
            } else if (remaining < 100) {
                charCounter.style.color = 'orange';
            } else {
                charCounter.style.color = 'green';
            }
        });
    }

    // Auto-resize textarea
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(textarea => {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    });
});

// Show success notification
function showSuccessNotification() {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        z-index: 1000;
        font-weight: bold;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        animation: slideIn 0.3s ease-out;
    `;
    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
            <span>✅</span>
            <div>
                <div>Message Sent Successfully!</div>
                <small>We'll get back to you within 24 hours.</small>
            </div>
        </div>
    `;
    
    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    `;
    document.head.appendChild(style);
    
    document.body.appendChild(notification);

    // Remove after 5 seconds
    setTimeout(() => {
        if (document.body.contains(notification)) {
            notification.style.animation = 'slideIn 0.3s ease-out reverse';
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }
    }, 5000);
}
