// register.js - <PERSON><PERSON> registration form submission for registerForm

document.addEventListener("DOMContentLoaded", function() {
    const registerForm = document.getElementById("registerForm");
    if (registerForm) {
        registerForm.addEventListener("submit", async function(e) {
            e.preventDefault();
            const form = e.target;
            const result = document.getElementById("registerResult");

            // Clear previous results
            if (result) {
                result.innerText = "";
            }

            try {
                // Get form data
                const username = form.username.value;
                const email = form.email.value;
                const password = form.password.value;

                // Check for confirm password field if it exists
                const confirmPasswordField = form.confirm_password;
                if (confirmPasswordField && password !== confirmPasswordField.value) {
                    if (result) {
                        result.innerText = "❌ Passwords do not match.";
                    }
                    return;
                }

                // Show loading
                if (result) {
                    result.innerText = "⏳ Creating account...";
                }

                const response = await fetch("https://stemblock-login-gljgs.ondigitalocean.app/register", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    credentials: "include", // CRITICAL: This enables JWT cookies
                    body: JSON.stringify({
                        username: username,
                        email: email,
                        password: password
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    // Registration successful
                    if (result) {
                        result.innerText = "✅ " + (data.message || "Registration successful! Please check your email to verify your account.");
                    }

                    // Clear form after successful registration
                    form.reset();
                } else {
                    // Handle registration errors
                    if (result) {
                        if (data.validation_errors) {
                            let errorMessage = "❌ Please fix the following errors:\n";
                            data.validation_errors.forEach(err => {
                                errorMessage += `• ${err.field}: ${err.message}\n`;
                            });
                            result.innerText = errorMessage;
                        } else {
                            result.innerText = "❌ " + (data.error || "Registration failed. Please try again.");
                        }
                    }
                }
            } catch (error) {
                // Handle network errors
                if (result) {
                    result.innerText = "❌ Unable to connect to server. Please check your internet connection.";
                }
            }
        });
    }
});
