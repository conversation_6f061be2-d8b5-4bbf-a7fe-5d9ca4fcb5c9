// reset-password.js - <PERSON>les password reset form functionality

document.addEventListener('DOMContentLoaded', function () {
    // Get token from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');

    // Pre-fill token if present
    const tokenInput = document.getElementById('token');
    if (tokenInput && token) {
        tokenInput.value = token;
    }

    // Handle password reset form
    const resetPasswordForm = document.getElementById('resetPasswordForm');
    if (resetPasswordForm) {
        resetPasswordForm.addEventListener('submit', async function (e) {
            e.preventDefault();
            const form = e.target;
            const result = document.getElementById('resetPasswordResult');

            // Clear previous results
            if (result) {
                result.innerText = "";
            }

            try {
                const resetToken = form.token.value;
                const password = form.password.value;
                const confirmPassword = form.confirm_password.value;

                // Client-side validation
                if (!resetToken) {
                    if (result) {
                        result.innerText = "❌ Reset token is required.";
                    }
                    return;
                }

                if (password !== confirmPassword) {
                    if (result) {
                        result.innerText = "❌ Passwords do not match.";
                    }
                    return;
                }

                if (password.length < 8) {
                    if (result) {
                        result.innerText = "❌ Password must be at least 8 characters long.";
                    }
                    return;
                }

                // Show loading
                if (result) {
                    result.innerText = "⏳ Resetting password...";
                }

                const response = await fetch(Config.getApiUrl('resetPassword'), {
                    method: "POST",
                    ...Config.getRequestConfig(),
                    body: JSON.stringify({
                        token: resetToken,
                        password: password
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    // Reset successful
                    if (result) {
                        result.innerText = "✅ " + (data.message || "Password reset successful! Redirecting to login...");
                        result.style.color = "green";
                    }

                    // Show success notification
                    showSuccessNotification();

                    // Clear form
                    form.reset();

                    // Redirect to login after short delay
                    setTimeout(() => {
                        window.location.href = "login.html";
                    }, 2000);
                } else {
                    // Handle errors
                    if (result) {
                        if (data.validation_errors) {
                            let errorMessage = "❌ Please fix the following errors:\n";
                            data.validation_errors.forEach(err => {
                                errorMessage += `• ${err.field}: ${err.message}\n`;
                            });
                            result.innerText = errorMessage;
                        } else {
                            result.innerText = "❌ " + (data.error || "Password reset failed. Please try again.");
                        }
                        result.style.color = "red";
                    }
                }
            } catch (error) {
                // Handle network errors
                if (result) {
                    result.innerText = "❌ Unable to connect to server. Please check your internet connection.";
                    result.style.color = "red";
                }
            }
        });
    }

    // Handle show/hide password functionality
    const showPasswordCheckbox = document.getElementById('showPassword');
    if (showPasswordCheckbox) {
        const passwordInput = document.getElementById('password');
        const confirmPasswordInput = document.getElementById('confirm_password');

        showPasswordCheckbox.addEventListener('change', function () {
            const type = this.checked ? 'text' : 'password';
            if (passwordInput) passwordInput.type = type;
            if (confirmPasswordInput) confirmPasswordInput.type = type;
        });
    }

    // Password strength indicator
    const passwordInput = document.getElementById('password');
    if (passwordInput) {
        passwordInput.addEventListener('input', function () {
            updatePasswordStrength(this.value);
        });
    }

    // Show success notification
    function showSuccessNotification() {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            z-index: 1000;
            font-weight: bold;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        `;
        notification.innerText = "✅ Password reset successful! Redirecting...";
        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 3000);
    }

    // Password strength checker
    function updatePasswordStrength(password) {
        const strengthIndicator = document.getElementById('passwordStrength');
        if (!strengthIndicator) return;

        let strength = 0;
        let feedback = [];

        // Length check
        if (password.length >= 8) {
            strength += 1;
        } else {
            feedback.push("At least 8 characters");
        }

        // Uppercase check
        if (/[A-Z]/.test(password)) {
            strength += 1;
        } else {
            feedback.push("One uppercase letter");
        }

        // Lowercase check
        if (/[a-z]/.test(password)) {
            strength += 1;
        } else {
            feedback.push("One lowercase letter");
        }

        // Number check
        if (/\d/.test(password)) {
            strength += 1;
        } else {
            feedback.push("One number");
        }

        // Special character check
        if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
            strength += 1;
        } else {
            feedback.push("One special character");
        }

        // Update indicator
        const strengthLevels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
        const strengthColors = ['#dc3545', '#fd7e14', '#ffc107', '#28a745', '#20c997'];
        
        strengthIndicator.innerText = `Password Strength: ${strengthLevels[strength] || 'Very Weak'}`;
        strengthIndicator.style.color = strengthColors[strength] || '#dc3545';

        if (feedback.length > 0) {
            strengthIndicator.innerText += ` (Need: ${feedback.join(', ')})`;
        }
    }

    // Check if token is valid on page load
    if (token) {
        validateResetToken(token);
    }

    // Validate reset token
    async function validateResetToken(resetToken) {
        try {
            const response = await fetch(`${Config.getApiUrl('validateResetToken')}?token=${encodeURIComponent(resetToken)}`, {
                method: "GET",
                ...Config.getRequestConfig()
            });

            if (!response.ok) {
                // Token is invalid or expired
                const errorDiv = document.createElement('div');
                errorDiv.style.cssText = `
                    background: #f8d7da;
                    color: #721c24;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 20px 0;
                    border: 1px solid #f5c6cb;
                `;
                errorDiv.innerHTML = `
                    <strong>⚠️ Invalid or Expired Token</strong><br>
                    This password reset link is invalid or has expired. 
                    <a href="forgotpassworda.html">Request a new password reset link</a>.
                `;
                
                const form = document.getElementById('resetPasswordForm');
                if (form) {
                    form.parentNode.insertBefore(errorDiv, form);
                    form.style.display = 'none';
                }
            }
        } catch (error) {
            console.error('Error validating reset token:', error);
        }
    }
});
