<!DOCTYPE html>
<html lang="">
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>STEMBLOCK</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Allerta+Stencil&family=Archivo+Black&family=Shippori+Antique&display=swap" rel="stylesheet">
</head>

<body>
    <div id="container" style="min-height:100vh;">


        <div class="logo">
            <img src="img/stem_block_logo.png" alt="logo">
        </div>
        <div class="profile">
            <!--            <a href="login.html">-->
            <img src="img/profile.png" alt="profile">
            <!--            </a>-->
        </div>

        <header>
            <nav>
                <input type="checkbox" id="nav-trigger" class="nav-trigger" />
                <label class="menu" for="nav-trigger">
                    &#9776; Menu
                </label>

                <ul class="navigation">
                    <li><a href="index.html" class="current">Home</a></li>

                    <li><a href="coursea.html">Course</a>
                        <img class="down" src="img/down.png" alt="down"></li>

                    <li><a href="aboutus.html">About Us</a>
                        <img class="down" src="img/down.png" alt="down"></li>

                    <li><a href="contact.html">Contact Us</a>
                        <img class="down" src="img/down.png" alt="down"></li>
                </ul>
            </nav>
        </header>

        <img class="aboutyellowbacka" src="img/aboutyellowbacka.jpg" alt="aboutyellowbacka">

        <div class="about-t-a">ABOUT US</div>
        <div class="about-p-a">
            <p>In an era of digital transformation, STEMBlock was born out of a profound reflection on the future of education. As a robotics education institution, STEMBlock is dedicated to providing the next generation of learners with a hands-on, minds-on approach to learning.
                <br>
                <br>Our name, "<span class="highlight">STEMBlock</span>", symbolizes not only the core values of STEM (Science, Technology, Engineering, Mathematics) education but also the concept of modular learning—just as robots are assembled from various components, knowledge is built from individual concepts.
            </p>
        </div>

        <div class="abouttablea">
            <ul>
                <li>
                    <div class="tablenow">About Us</div>
                </li>
                <li>Our Team</li>
                <li>Our Partners</li>
            </ul>
        </div>

        <img class="aboutyellowbackb" src="img/aboutyellowbackb.jpg" alt="aboutyellowbackb">


        <div class="about-t-b">Our Team</div>
        <div class="about-p-b">
            <p>The founding team of STEMBlock understands that in the future world, children need more than just knowledge from textbooks; they need the ability to turn abstract concepts into practical applications.
                <br>
                <br>Thus, we embrace the philosophy of “building the future from the ground up.” Whether through programming “code blocks” or constructing with physical “building blocks,” we encourage every student to act as a builder, stacking together scattered pieces of knowledge into a comprehensive technological framework
            </p>
        </div>

        <div class="abouttableb">
            <ul>
                <li>About Us</li>
                <li>
                    <div class="tablenow">Our Team</div>
                </li>
                <li>Our Partners</li>
            </ul>
        </div>

        <div class="teama">
            <img class="aboutphoto" src="img/marshall.jpg" alt="aboutphoto">
            <p class="teamname">Marshall Yang</p>
            <p class="teamtitle">Co-founder</p>
            <p class="teamintro">A seasoned robotics enthusiast, Marshall has a proven track record coaching at the VEX World Championship, not once, but twice. His dedication extends beyond the competition field, having mentored numerous students from various private schools and organizations, inspiring a passion for STEM and robotics across different communities.</p>
            <div class="teamcontact">
                <img class="teamins" src="img/inswhite.png" alt="ins">
                <img class="teamface" src="img/facebook.png" alt="facebook">
                <img class="teamx" src="img/x.png" alt="x">
                <img class="teamemail" src="img/email.png" alt="email">
            </div>
        </div>

        <div class="teamb">
            <img class="aboutphoto" src="img/gavin.jpg" alt="aboutphoto">
            <p class="teamname">Gavin Huang</p>
            <p class="teamtitle">Co-founder</p>
            <p class="teamintro">Gavin is a seasoned robotics coach that brings extensive competitive robotics experience with his unique coaching style. One time FIRST Robotics World Championship and two times VEX Robotics World Championship participants globally, Gavin has successfully transitioned from competitor to mentor to record proven entrepreneur.</p>
            <div class="teamcontact">
                <img class="teamins" src="img/inswhite.png" alt="ins">
                <img class="teamface" src="img/facebook.png" alt="facebook">
                <img class="teamx" src="img/x.png" alt="x">
                <img class="teamemail" src="img/email.png" alt="email">
            </div>
        </div>

        <div class="teamc">
            <img class="aboutphoto" src="img/aboutphoto.jpg" alt="aboutphoto">
            <p class="teamname">Jian Huang</p>
            <p class="teamtitle">Co-founder</p>
            <p class="teamintro">A dedicated educator with a proven track record of making math and programming accessible and exciting for students of all abilities. Their passion for fostering a love of learning shines through in their dynamic teaching style and individualized approach.</p>
            <div class="teamcontact">
                <img class="teamins" src="img/inswhite.png" alt="ins">
                <img class="teamface" src="img/facebook.png" alt="facebook">
                <img class="teamx" src="img/x.png" alt="x">
                <img class="teamemail" src="img/email.png" alt="email">
            </div>
        </div>
<!--
        <div class="teamd">
            <img class="aboutphoto" src="img/aboutphoto.jpg" alt="aboutphoto">
            <p class="teamname">ZZ</p>
            <p class="teamtitle">Co-founder</p>
            <p class="teamintro">ZZ graduated with a bachelor’s degree from the Lu Xun Academy of Fine Arts in China and earned a master’s degree in Multimedia Art and Design from the School of Arts at Toulouse II University in France.
                <br>
                <br> Early in his career, he participated in the development of Microsoft’s Xbox game Alone in the Dark IV, and served as Art Director for several large-scale online games, including Heroes of Might and Magic Online by TQ Digital and Sword of Heaven I by Linekong.
                <br>
                <br> Over the past decade, he has been involved in the development of AAA games at Gameloft’s Montreal studio in Canada, contributing to titles such as Dungeon Hunter IV, Modern Combat IV, and Rival Knights.
                <br>
                <br> He has a deep understanding of Web 3.0 and pop culture. Recently, he has collaborated with a well-known designer toy studio in China and has been supervising projects for Mango TV.</p>
            <div class="teamcontact">
                <img class="teamins" src="img/inswhite.png" alt="ins">
                <img class="teamface" src="img/facebook.png" alt="facebook">
                <img class="teamx" src="img/x.png" alt="x">
                <img class="teamemail" src="img/email.png" alt="email">
            </div>
        </div>
-->


        <img class="aboutyellowbackc" src="img/aboutyellowbackc.jpg" alt="aboutyellowbackc">

        <div class="about-t-c">Coach Team</div>
        <div class="about-p-c">
            <p>Get ready to be amazed! Our coaches bring a wealth of experience and passion to STEM and robotics, igniting curiosity and empowering students from beginners to experts.
            </p>
        </div>


        <div class="coacha">
            <!--            <img class="aboutphoto" src="img/aboutphoto.jpg" alt="aboutphoto">-->
            <p class="coachname">Marshall Yang</p>
            <p class="coachtitle">Head Coach</p>
            <div class="coachcontact">
                <img class="coachins" src="img/inswhite.png" alt="ins">
                <img class="coachface" src="img/facebook.png" alt="facebook">
                <img class="coachx" src="img/x.png" alt="x">
                <img class="coachemail" src="img/email.png" alt="email">
            </div>
        </div>
        <div class="coachb">
            <!--            <img class="aboutphoto" src="img/aboutphoto.jpg" alt="aboutphoto">-->
            <p class="coachname">Gavin Huang</p>
            <p class="coachtitle">Coach</p>
            <div class="coachcontact">
                <img class="coachins" src="img/inswhite.png" alt="ins">
                <img class="coachface" src="img/facebook.png" alt="facebook">
                <img class="coachx" src="img/x.png" alt="x">
                <img class="coachemail" src="img/email.png" alt="email">
            </div>
        </div>
        <div class="coachc">
            <!--            <img class="aboutphoto" src="img/aboutphoto.jpg" alt="aboutphoto">-->
            <p class="coachname">Trevor Menchenton</p>
            <p class="coachtitle">Coach</p>
            <div class="coachcontact">
                <img class="coachins" src="img/inswhite.png" alt="ins">
                <img class="coachface" src="img/facebook.png" alt="facebook">
                <img class="coachx" src="img/x.png" alt="x">
                <img class="coachemail" src="img/email.png" alt="email">
            </div>
        </div>
        <div class="coachd">
            <!--           <img class="aboutphoto" src="img/aboutphoto.jpg" alt="aboutphoto">-->
            <p class="coachname">Leo Meng</p>
            <p class="coachtitle">Coach</p>
            <div class="coachcontact">
                <img class="coachins" src="img/inswhite.png" alt="ins">
                <img class="coachface" src="img/facebook.png" alt="facebook">
                <img class="coachx" src="img/x.png" alt="x">
                <img class="coachemail" src="img/email.png" alt="email">
            </div>
        </div>
        <div class="coache">
            <!--           <img class="aboutphoto" src="img/aboutphoto.jpg" alt="aboutphoto">-->
            <p class="coachname">ZZ</p>
            <p class="coachtitle">Coach</p>
            <div class="coachcontact">
                <img class="coachins" src="img/inswhite.png" alt="ins">
                <img class="coachface" src="img/facebook.png" alt="facebook">
                <img class="coachx" src="img/x.png" alt="x">
                <img class="coachemail" src="img/email.png" alt="email">
            </div>
        </div>

        <div class="about-t-d">
            <p>Our Partner</p>
        </div>

        <!-- Partners Table -->
        <div class="abouttablec">
            <ul>
                <li>About Us</li>
                <li>
                    Our Team
                </li>
                <li>
                    <div class="tablenow">Our Partners</div>
                </li>
            </ul>
        </div>

        <!-- Button -->
        <div class="buttona">
            <button class="ourPartnerButton">
                <img class="buttonIcon" src="img/caution.png" alt="cautionTape" />
                <div>
                    <p class="partnername"> Caution Tape
                        <br>Robotics Club </p>
                </div>
            </button>
        </div>
        <div class="buttonb">
            <button class="ourPartnerButton">
                <img class="buttonIcon" src="img/cansteam.png" alt="cansteam" />
                <div>
                    <p class="partnername"> Cansteam Foundation</p>
                </div>
            </button>
        </div>
        <div class="buttonc">
            <button class="ourPartnerButton">
                <img class="fca" src="img/fca.png" alt="fca" />
                <div>
                    <p class="fcaname"> Fredericton
                        <br>Christian Academy</p>
                </div>
            </button>
        </div>
        <div class="buttond">
            <button class="ourPartnerButton">
                <img class="buttonIcon" src="img/fma.png" alt="fma" />
                <div>
                    <p class="partnername"> Fredericton
                        <br>Montessori Academy</p>
                </div>
            </button>
        </div>




    </div>

    <footer style="width:100%;">
        <div class="contact">
            <h3>Contact Us</h3>
            <address>
               Phone: (506)9985996<br>
               Address: 50 Crowther Ln suite 140, Fredericton, NB E3C 0J1<br>
               <a href="mailto:<EMAIL>">Email: <EMAIL></a>
            </address>
        </div>
        <div class="footersocial">
            <img class="footerins" src="img/inswhite.png" alt="inswhite">
            <img class="footerfacebook" src="img/facebook.png" alt="facebook">
        </div>
    </footer>


</body>

</html>
