# 🌍 Environment Variables Setup Guide

## 🎯 **Overview**

The web folder now uses environment variables to automatically configure the backend URL based on your environment. This makes it easy to switch between development and production without changing code.

## 🔧 **How It Works**

### **Automatic Environment Detection**
The system automatically detects your environment:
- **Development**: `localhost` or `127.0.0.1` → Uses local backend
- **Production**: Any other domain → Uses cloud backend

### **Default Configuration**
```javascript
// Development (localhost)
Backend URL: http://localhost:8081
Frontend URL: http://localhost:3000 (or current port)

// Production (any other domain)
Backend URL: https://stemblock-login-gljgs.ondigitalocean.app
Frontend URL: https://www.stemblock.ca
```

## 🚀 **Quick Start**

### **1. Start Your Backend (Development)**
```bash
cd stemblock-login
export PORT=8081
export DEVELOPMENT=true
go run main.go
```

### **2. Start Your Frontend**
```bash
cd Website/web
python3 -m http.server 3000
```

### **3. Visit Your Site**
```
http://localhost:3000
```

The frontend will automatically connect to `http://localhost:8081` for the backend.

## ⚙️ **Custom Environment Variables**

You can override the default configuration by setting environment variables in your HTML:

### **Method 1: Set Variables in HTML**
Add this script tag **before** loading other scripts:
```html
<script>
    // Override backend URL
    window.ENV_BACKEND_URL = 'http://localhost:9000';
    
    // Override frontend URL
    window.ENV_FRONTEND_URL = 'http://localhost:4000';
    
    // Enable debug mode
    window.ENV_DEBUG = true;
    
    // Set API timeout (milliseconds)
    window.ENV_API_TIMEOUT = 15000;
    
    // Set session check interval (milliseconds)
    window.ENV_SESSION_CHECK_INTERVAL = 3 * 60 * 1000; // 3 minutes
</script>
<script src="js/env.js"></script>
<script src="js/config.js"></script>
```

### **Method 2: Create Custom Environment File**
Create `js/env-custom.js`:
```javascript
// Custom environment configuration
window.ENV_BACKEND_URL = 'http://localhost:9000';
window.ENV_FRONTEND_URL = 'http://localhost:4000';
window.ENV_DEBUG = true;
```

Then load it before `env.js`:
```html
<script src="js/env-custom.js"></script>
<script src="js/env.js"></script>
<script src="js/config.js"></script>
```

## 🔍 **Available Environment Variables**

| Variable | Default (Dev) | Default (Prod) | Description |
|----------|---------------|----------------|-------------|
| `ENV_BACKEND_URL` | `http://localhost:8081` | `https://stemblock-login-gljgs.ondigitalocean.app` | Backend API URL |
| `ENV_FRONTEND_URL` | `http://localhost:3000` | `https://www.stemblock.ca` | Frontend URL |
| `ENV_TYPE` | `development` | `production` | Environment type |
| `ENV_DEBUG` | `true` | `false` | Enable debug logging |
| `ENV_API_TIMEOUT` | `10000` | `10000` | API request timeout (ms) |
| `ENV_SESSION_CHECK_INTERVAL` | `300000` | `300000` | Session check interval (ms) |

## 🛠️ **Development Scenarios**

### **Scenario 1: Standard Local Development**
```bash
# Backend on port 8081
cd stemblock-login
export PORT=8081
go run main.go

# Frontend on port 3000
cd Website/web
python3 -m http.server 3000
```
**Result**: Frontend automatically connects to `http://localhost:8081`

### **Scenario 2: Custom Backend Port**
```bash
# Backend on port 9000
cd stemblock-login
export PORT=9000
go run main.go
```

Add to your HTML:
```html
<script>
    window.ENV_BACKEND_URL = 'http://localhost:9000';
</script>
<script src="js/env.js"></script>
```

### **Scenario 3: Testing with Cloud Backend**
```html
<script>
    window.ENV_BACKEND_URL = 'https://stemblock-login-gljgs.ondigitalocean.app';
</script>
<script src="js/env.js"></script>
```

### **Scenario 4: Different Frontend Port**
```bash
# Frontend on port 8080
cd Website/web
python3 -m http.server 8080
```

The system automatically detects the port and adjusts accordingly.

## 🔧 **Backend CORS Configuration**

Make sure your Go backend allows requests from your frontend URL:

### **For Development**
```go
// In your security.go middleware
w.Header().Set("Access-Control-Allow-Origin", "http://localhost:3000")
w.Header().Set("Access-Control-Allow-Credentials", "true")
```

### **For Production**
```go
w.Header().Set("Access-Control-Allow-Origin", "https://www.stemblock.ca")
w.Header().Set("Access-Control-Allow-Credentials", "true")
```

### **For Dynamic CORS (Recommended)**
```go
func EnableCORS(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        origin := r.Header.Get("Origin")
        
        // Allow localhost for development
        if strings.Contains(origin, "localhost") || strings.Contains(origin, "127.0.0.1") {
            w.Header().Set("Access-Control-Allow-Origin", origin)
        } else if origin == "https://www.stemblock.ca" {
            w.Header().Set("Access-Control-Allow-Origin", origin)
        }
        
        w.Header().Set("Access-Control-Allow-Credentials", "true")
        w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
        w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
        
        if r.Method == "OPTIONS" {
            w.WriteHeader(http.StatusOK)
            return
        }
        
        next.ServeHTTP(w, r)
    })
}
```

## 🐛 **Debugging**

### **Check Current Configuration**
Open browser console and run:
```javascript
console.log('Environment:', window.ENV);
console.log('Config:', window.Config);
```

### **Test Backend Connection**
Visit: `http://localhost:3000/test-connection.html`

### **Enable Debug Logging**
```html
<script>
    window.ENV_DEBUG = true;
</script>
```

## 📋 **Troubleshooting**

### **Problem**: "Unable to connect to server"
**Solution**: 
1. Check if backend is running: `curl http://localhost:8081/`
2. Check CORS configuration in backend
3. Verify environment variables: `console.log(window.ENV)`

### **Problem**: CORS errors
**Solution**: 
1. Update backend CORS to allow your frontend URL
2. Ensure `Access-Control-Allow-Credentials: true`
3. Check browser Network tab for exact error

### **Problem**: Wrong backend URL
**Solution**: 
1. Set custom environment variable before loading scripts
2. Check browser console for configuration logs
3. Verify `window.ENV_BACKEND_URL` value

## 🎯 **Production Deployment**

For production, the system automatically uses:
- Backend: `https://stemblock-login-gljgs.ondigitalocean.app`
- Frontend: `https://www.stemblock.ca`

No additional configuration needed! 🚀
