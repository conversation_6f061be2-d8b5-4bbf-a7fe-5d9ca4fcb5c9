<!DOCTYPE html>
<html lang="">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>STEMBLOCK</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Allerta+Stencil&family=Archivo+Black&family=Shippori+Antique&display=swap" rel="stylesheet">
</head>

<body>
    <div id="container" style="min-height:100vh;">


        <div class="logowhite">
            <img src="img/stemblocklogo.png" alt="logo">
        </div>
        <div class="profile">
            <!--           <a href="login.html">-->
            <img src="img/profile.png" alt="profile">
            <!--            </a>-->
        </div>

        <header>
            <nav>
                <input type="checkbox" id="nav-trigger" class="nav-trigger" />
                <label class="menu" for="nav-trigger">
                    &#9776; Menu
                </label>

                <ul class="navigation">
                    <li><a href="index.html" class="current">Home</a></li>

                    <li><a href="coursea.html">Course</a>
                        <img class="down" src="img/down.png" alt="down"></li>

                    <li><a href="aboutus.html">About Us</a>
                        <img class="down" src="img/down.png" alt="down"></li>

                    <li><a href="contact.html">Contact Us</a>
                        <img class="down" src="img/down.png" alt="down"></li>
                </ul>
            </nav>
        </header>


        <div class="courseaback">
            <div class="coursea"></div>
        </div>
        <p class="courseatitlea">VEX
            <br>IQ STEMLab </p>

        <div class="courseb-a-back"><img class="courseb-a-back" src="img/courseback.jpg" alt="courseback"></div>

        <div class="courseb-p-a">
            <p>Explore, Build, Code, and Discover the World of Robotics </p>
        </div>
        <div class="courseb-p-b">
            <p>VEX
                <br> IQ STEMLab </p>
        </div>
        <img class="coursebqr" src="img/qrcodewhite.jpg" alt="qrcodewhite">
        <div class="courseb-qr-p">
            <p>Register to try our
                <br> Free Trial Class</p>
        </div>
        <div class="courseb-p-c">
            <p>Grade Level:
                <br>4th – 8th Grade

            </p>
        </div>
        <div class="courseb-p-d">
            <p>Course Type:
                <br> Hands-On Robotics, Programming &#38; Engineering

            </p>
        </div>
        <div class="courseb-p-e">
            <p>Focus Areas:
                <br>Robotics Theory, Mechanical Design, and Programming Logic

            </p>
        </div>
        <div class="courseb-p-f">
            <p>Course Introduction</p>
        </div>
        <div class="courseb-p-g">
            <p>Get ready to build and program your own robots in this exciting, hands-on VEX IQ STEMLab class! Designed for curious and creative minds in Grades 4–8, this course offers a dynamic introduction to the world of robotics, mechanical structures, and programming. </p>
        </div>


        <div class="courseb-p-h">
            <p>Students will use VEX IQ kits and coding platforms to bring their ideas to life —learning how robots think, move, and interact with their environment. Whether it's building a robot to solve a maze or coding a sequence to complete a task, students will be at the centre of the action with real tools, real code, and real challenges. </p>
        </div>
        <div class="courseb-p-i">
            <p>What Students Will Learn
            </p>
        </div>

        <div class="courseb-p-j">
            <p>Explore, Build, Code, and Discover the World of Robotics </p>
        </div>
        <div class="courseb-p-k">
            <ul>
                <li>Core robotics concepts: motion, structure, and sensor systems</li>
                <li>Mechanical engineering principles: gears, levers, pulleys, axles</li>
                <li>Introduction to programming with VEXcode IQ Blocks (block-based coding)</li>
                <li>Logic, sequences, loops, and conditionals in robotics programming</li>
                <li>How to integrate hardware and software to complete challenges</li>
                <li>Collaboration, troubleshooting, and engineering design thinking</li>
            </ul>
        </div>
        <div class="courseb-yellow-line"><img class="courseb-yellow-line" src="img/coursebackb.jpg" alt="coursebackb"></div>

        <div class="courseb-p-l">
            <p>By the end of this course, students will
            </p>
        </div>

        <div class="team-three-b"><img class="team-three-b" src="img/team3b.jpg" alt="team3b"></div>

        <div class="courseb-p-m">
            <ul>
                <li>Understand how mechanical and software systems work together in robotics </li>
                <li>Build and program functioning robots to complete specific tasks </li>
                <li>Apply computational thinking and logic in real-world scenarios </li>
                <li>Make improvement on problem-solving, teamwork, and creative engineering skills </li>
            </ul>
        </div>

        <div class="courseb-p-n">
            <p>Learning Approach</p>
        </div>

        <div class="coursebackc"><img class="coursebackc" src="img/coursebackc.jpg" alt="team3b"></div>


        <div class="courseb-p-o">
            <p>This course uses a hands-on, project-based approach, where students learn by building, coding, testing, and improving their designs. Every project takes 4-5 classes finishes, with a mini show case at the end.
                <br>
                <br> For each project, it features build challenges, programming tasks, and interactive exploration of how machines and code work together. No prior experience is needed, just curiosity and a desire to learn! </p>
        </div>

        <div class="team-two"><img class="team-two" src="img/team2.jpg" alt="team2"></div>

        <button class="coursebreadless">Read less </button>

        <script src="js/expand.js"></script>
    </div>

    <footer style="width:100%;">
        <div class="contact">
            <h3>Contact Us</h3>
            <address>
               Phone: (506)9985996<br>
               Address: 50 Crowther Ln suite 140, Fredericton, NB E3C 0J1<br>
               <a href="mailto:<EMAIL>">Email: <EMAIL></a>
            </address>
        </div>
        <div class="footersocial">
            <img class="footerins" src="img/inswhite.png" alt="inswhite">
            <img class="footerfacebook" src="img/facebook.png" alt="facebook">
        </div>
    </footer>
</body>

</html>
