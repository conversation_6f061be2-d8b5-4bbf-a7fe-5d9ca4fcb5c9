<!DOCTYPE html>
<html>
<head>
    <title>Backend Connection Test</title>
</head>
<body>
    <h1>Backend Connection Test</h1>
    <button onclick="testConnection()">Test Backend Connection</button>
    <pre id="result"></pre>

    <script src="js/env.js"></script>
    <script>
        async function testConnection() {
            const result = document.getElementById('result');
            result.innerText = 'Testing connection...';

            try {
                // Test 1: Simple fetch to backend
                const backendUrl = window.ENV?.BACKEND_URL || 'http://localhost:8081';
                console.log('Testing connection to:', backendUrl);

                const response = await fetch(backendUrl + '/', {
                    method: 'GET',
                    credentials: 'include'
                });

                result.innerText = `✅ Connection successful!
Status: ${response.status}
Headers: ${JSON.stringify([...response.headers.entries()], null, 2)}`;

            } catch (error) {
                result.innerText = `❌ Connection failed:
Error: ${error.message}
Stack: ${error.stack}`;
                console.error('Connection error:', error);
            }
        }
    </script>
</body>
</html>
