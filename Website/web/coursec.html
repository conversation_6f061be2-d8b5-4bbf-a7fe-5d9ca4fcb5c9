<!DOCTYPE html>
<html lang="">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>STEMBLOCK</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Allerta+Stencil&family=Archivo+Black&family=Shippori+Antique&display=swap" rel="stylesheet">
</head>

<body>
    <div id="container" style="min-height:100vh;">


        <div class="logowhite">
            <img src="img/stemblocklogo.png" alt="logo">
        </div>
        <div class="profile">
            <!--           <a href="login.html">-->
            <img src="img/profile.png" alt="profile">
            <!--            </a>-->
        </div>

        <header>
            <nav>
                <input type="checkbox" id="nav-trigger" class="nav-trigger" />
                <label class="menu" for="nav-trigger">
                    &#9776; Menu
                </label>

                <ul class="navigation">
                    <li><a href="index.html" class="current">Home</a></li>

                    <li><a href="coursea.html">Course</a>
                        <img class="down" src="img/down.png" alt="down"></li>

                    <li><a href="aboutus.html">About Us</a>
                        <img class="down" src="img/down.png" alt="down"></li>

                    <li><a href="contact.html">Contact Us</a>
                        <img class="down" src="img/down.png" alt="down"></li>
                </ul>
            </nav>
        </header>

        <div class="courseaback">
            <div class="courseb"></div>
        </div>
        <p class="coursectitle">VEX
            <br>GO Robotics </p>

        <div class="courseback"><img class="courseback" src="img/courseback.jpg" alt="courseback"></div>

        <div class="coursec-p-a">
            <p>Build, Play, and Learn with Robots </p>
        </div>
        <div class="coursec-p-b">
            <p>VEX
                <br> GO Robotics </p>
        </div>


        <img class="coursecqr" src="img/qrcodewhite.jpg" alt="qrcodewhite">
        <div class="coursec-qr-p">
            <p>Register to try our
                <br> Free Trial Class</p>
        </div>

        <div class="coursec-p-c">
            <p>Grade Level:
                <br>Kindergarten – 3rd Grade

            </p>
        </div>
        <div class="coursec-p-d">
            <p>Course Type:
                <br>Intro to Robotics &#38; Programmin
            </p>
        </div>
        <div class="coursec-p-e">
            <p>Focus Areas:
                <br>Robotics Components, Hands-On Building, Beginner Programming

            </p>
        </div>
        <div class="coursec-p-f">
            <p>Course Introduction</p>
        </div>
        <div class="coursec-p-g">
            <p>Welcome to the exciting world of VEX GO—where young learners become inventors, builders, and explorers! This course is specially designed for early elementary students to discover the basics of robotics through creative, hands-on learning. </p>
        </div>


        <div class="coursec-p-h">
            <p>Using colourful VEX GO kits, students will build simple robots, learn about robotic parts like motors and sensors, and explore how they can use programming to make their robots move, respond, and think. Activities are fun, engaging, and designed to spark curiosity while building foundational STEM skills. </p>
        </div>
        <div class="coursec-p-i">
            <p>What Students Will Learn
            </p>
        </div>

        <div class="courseb-p-j">
            <p>Build, Play, and Learn with Robots</p>
        </div>
        <div class="courseb-p-k">
            <ul>
                <li>Introduction to basic robot parts: motors, gears, beams, and sensors </li>
                <li>How robots move, sense, and respond to their environment </li>
                <li>Beginning programming using VEXCode GO (block-based coding) </li>
                <li>How to give commands and create simple programs </li>
                <li>Teamwork, communication, and problem-solving through guided challenges </li>
            </ul>
        </div>
        <div class="coursec-yellow-line"><img class="coursec-yellow-line" src="img/coursebackb.jpg" alt="coursebackb"></div>

        <div class="courseb-p-l">
            <p>By the end of this course, students will
            </p>
        </div>

        <div class="team-three-b"><img class="team-three-b" src="img/team9b.jpg" alt="team9b"></div>

        <div class="courseb-p-m">
            <ul>
                <li>Recognize and name core robot components </li>
                <li>Understand that robots can follow instructions through code  </li>
                <li>Build basic robot models and make them move or respond </li>
                <li>Gain confidence through creativity, hands-on building, and programming play </li>
            </ul>
        </div>

        <div class="courseb-p-n">
            <p>Learning Approach</p>
        </div>

        <div class="coursebackc"><img class="coursebackc" src="img/coursebackc.jpg" alt="team3b"></div>


        <div class="courseb-p-o">
            <p>VEX GO Robotics is all about learning through doing! Students will work in pairs or small groups, following step-by-step build instructions, creating their own robots, and experimenting with how coding affects robot behaviour.
                <br>
                <br> Lessons are age-appropriate, playful, and packed with discovery. Each series of classes will centre around a theme-based project, with a clear focus that connects robotics to exciting real-world topics such as space exploration, ocean discovery, or smart city automation.  </p>
        </div>

        <div class="team-two"><img class="team-two" src="img/team7.jpg" alt="team7"></div>

        <button class="coursebreadless">Read less </button>
        <script src="js/expand.js"></script>
    </div>



    <footer style="width:100%;">
        <div class="contact">
            <h3>Contact Us</h3>
            <address>
               Phone: (506)9985996<br>
               Address: 50 Crowther Ln suite 140, Fredericton, NB E3C 0J1<br>
               <a href="mailto:<EMAIL>">Email: <EMAIL></a>
            </address>
        </div>
        <div class="footersocial">
            <img class="footerins" src="img/inswhite.png" alt="inswhite">
            <img class="footerfacebook" src="img/facebook.png" alt="facebook">
        </div>
    </footer>
</body>

</html>
