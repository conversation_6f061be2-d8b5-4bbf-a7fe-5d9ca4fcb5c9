<!DOCTYPE html>
<html lang="">

<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>STEMBLOCK</title>
    <link rel="stylesheet" href="css/forgotpassword.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Allerta+Stencil&family=Archivo+Black&family=Shippori+Antique&display=swap" rel="stylesheet">
</head>

<body>
    <div id="container" style="min-height:100vh;">
        <div class="logowhite">
            <img src="img/stemblocklogo.png" alt="logo">
        </div>
        <div class="profile">
            <!--            <a href="login.html">-->
            <img src="img/profile.png" alt="profile">
            <!--           </a>-->
        </div>

        <header>
            <nav>
                <input type="checkbox" id="nav-trigger" class="nav-trigger" />
                <label class="menu" for="nav-trigger">
                    &#9776; Menu
                </label>

                <ul class="navigation">
                    <li><a href="index.html" class="current">Home</a></li>

                    <li><a href="coursea.html">Course</a>
                        <img class="down" src="img/down.png" alt="down"></li>

                    <li><a href="aboutus.html">About Us</a>
                        <img class="down" src="img/down.png" alt="down"></li>

                    <li><a href="contact.html">Contact Us</a>
                        <img class="down" src="img/down.png" alt="down"></li>
                </ul>
            </nav>
        </header>

        <img class="loginbacka" src="img/loginbacka.jpg" alt="loginbacka">
        <div class="forgot-p-a">
            <p>Reset Your Password</p>
        </div>

        <div class="forgot-p-b">
            <p class="forgotp2-1">
                Enter your new password below
            </p>
            <div class="forgotp2-2">
                <p class="forgotp2-2-1">
                    Need to go back?
                </p>
                <p class="forgotp2-2-2"><a href="forgotpassworda.html" style="color: #FCB11A; text-decoration: none;">Return to email step</a></p>
            </div>
        </div>

        <!--
        <div class="forgotp2">
            <p class="forgotp2-1">
                We’ve sent a code to your email
            </p>
            <div class="forgotp2-2">
                <p class="forgotp2-2-1">
                    Did not receive the code?
                </p>
                <p class="forgotp2-2-2">Re-send the code</p>
            </div>
        </div>
-->



        <div class="codepart">
            <form id="resetPasswordForm">
                <input type="hidden" id="token" name="token" value="">
                <div class="logininput-box">
                    <input type="password" id="password" name="password" placeholder="New Password*" required>
                </div>
                <div class="logininput-box">
                    <input type="password" id="confirm_password" name="confirm_password" placeholder="Confirm New Password*" required>
                </div>
                <div class="show-password">
                    <input type="checkbox" id="showPassword">
                    <label for="showPassword">Show Password</label>
                </div>
            </form>
            <pre id="resetPasswordResult"></pre>
        </div>

        <div class="forgotpasswordbtnb">
            <a href="forgotpassworda.html">Back</a>
        </div>

        <button type="submit" form="resetPasswordForm" class="forgotpasswordbtna">Reset Password</button>



        <img class="loginbackb" src="img/loginbackb.jpg" alt="loginbackb">





    </div>
    <footer style="width:100%;">
        <div class="contact">
            <h3>Contact Us</h3>
            <address>
               Phone: (506)9985996<br>
               Address: 50 Crowther Ln suite 140, Fredericton, NB E3C 0J1<br>
               <a href="mailto:<EMAIL>">Email: <EMAIL></a>
            </address>
        </div>
        <div class="footersocial">
            <img class="footerins" src="img/inswhite.png" alt="inswhite">
            <img class="footerfacebook" src="img/facebook.png" alt="facebook">
        </div>
    </footer>

    <!--Javascript after the page    -->
    <script src="js/config.js"></script>
    <script src="js/forgotpassword.js"></script>
    <script>
        // Extract token from URL parameters and populate the hidden field
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const token = urlParams.get('token');
            const tokenField = document.getElementById('token');
            if (token && tokenField) {
                tokenField.value = token;
            }
        });
    </script>
</body>

</html>
