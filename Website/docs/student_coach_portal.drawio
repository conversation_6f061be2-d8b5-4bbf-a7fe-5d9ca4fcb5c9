<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" version="27.1.6" pages="2">
  <diagram name="User Workflow" id="9VGQRP08fFLJNSsDVJMN">
    <mxGraphModel dx="1039" dy="757" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="H8-tO8T33itUdzrwufuf-2" value="&#xa;  &#xa;    &#xa;      &#xa;        &#xa;        &#xa;        &#xa;        &#xa;        &#xa;          &#xa;        &#xa;        &#xa;        &#xa;        &#xa;          &#xa;        &#xa;        &#xa;        &#xa;        &#xa;          &#xa;        &#xa;        &#xa;        &#xa;        &#xa;          &#xa;        &#xa;        &#xa;        &#xa;        &#xa;          &#xa;        &#xa;        &#xa;        &#xa;        &#xa;          &#xa;        &#xa;        &#xa;          &#xa;        &#xa;        &#xa;        &#xa;        &#xa;          &#xa;        &#xa;        &#xa;          &#xa;        &#xa;        &#xa;          &#xa;        &#xa;        &#xa;        &#xa;        &#xa;          &#xa;        &#xa;        &#xa;          &#xa;        &#xa;        &#xa;          &#xa;        &#xa;        &#xa;        &#xa;        &#xa;          &#xa;        &#xa;        &#xa;          &#xa;        &#xa;        &#xa;          &#xa;        &#xa;        &#xa;        &#xa;        &#xa;          &#xa;        &#xa;        &#xa;          &#xa;        &#xa;        &#xa;          &#xa;        &#xa;      &#xa;    &#xa;  &#xa;" style="text;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry width="30" height="1260" as="geometry" />
        </mxCell>
        <mxCell id="H8-tO8T33itUdzrwufuf-3" value="Webpage Login" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="380" y="20" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="H8-tO8T33itUdzrwufuf-4" value="Select Profile (Student / Coach)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="350" y="110" width="200" height="50" as="geometry" />
        </mxCell>
        <mxCell id="H8-tO8T33itUdzrwufuf-5" value="Student Portal" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="140" y="170" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="H8-tO8T33itUdzrwufuf-6" value="Coach Portal" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="600" y="180" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="H8-tO8T33itUdzrwufuf-7" value="View Enrolled Courses" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="140" y="250" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="H8-tO8T33itUdzrwufuf-8" value="Course Detail Page&#xa;- Description&#xa;- Lesson Details&#xa;- Schedule&#xa;- Materials&#xa;- Feedback" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="120" y="340" width="180" height="100" as="geometry" />
        </mxCell>
        <mxCell id="H8-tO8T33itUdzrwufuf-9" value="Review Feedback / Comments per Lesson" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="110" y="510" width="200" height="50" as="geometry" />
        </mxCell>
        <mxCell id="H8-tO8T33itUdzrwufuf-10" value="View Courses Taught / Registered Students" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="570" y="270" width="200" height="50" as="geometry" />
        </mxCell>
        <mxCell id="H8-tO8T33itUdzrwufuf-11" value="Manage Course&#xa;- Edit Description&#xa;- Upload Lessons&#xa;- Upload Materials&#xa;- Update Classroom Link" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="560" y="360" width="220" height="100" as="geometry" />
        </mxCell>
        <mxCell id="H8-tO8T33itUdzrwufuf-12" value="Assign Feedback per Student per Lesson" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="560" y="520" width="220" height="50" as="geometry" />
        </mxCell>
        <mxCell id="H8-tO8T33itUdzrwufuf-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" edge="1" parent="1" source="H8-tO8T33itUdzrwufuf-3" target="H8-tO8T33itUdzrwufuf-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="H8-tO8T33itUdzrwufuf-14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" edge="1" parent="1" source="H8-tO8T33itUdzrwufuf-4" target="H8-tO8T33itUdzrwufuf-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="H8-tO8T33itUdzrwufuf-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" edge="1" parent="1" source="H8-tO8T33itUdzrwufuf-4" target="H8-tO8T33itUdzrwufuf-6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="H8-tO8T33itUdzrwufuf-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" edge="1" parent="1" source="H8-tO8T33itUdzrwufuf-5" target="H8-tO8T33itUdzrwufuf-7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="H8-tO8T33itUdzrwufuf-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" edge="1" parent="1" source="H8-tO8T33itUdzrwufuf-7" target="H8-tO8T33itUdzrwufuf-8">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="H8-tO8T33itUdzrwufuf-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" edge="1" parent="1" source="H8-tO8T33itUdzrwufuf-8" target="H8-tO8T33itUdzrwufuf-9">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="H8-tO8T33itUdzrwufuf-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" edge="1" parent="1" source="H8-tO8T33itUdzrwufuf-6" target="H8-tO8T33itUdzrwufuf-10">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="H8-tO8T33itUdzrwufuf-20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" edge="1" parent="1" source="H8-tO8T33itUdzrwufuf-10" target="H8-tO8T33itUdzrwufuf-11">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="H8-tO8T33itUdzrwufuf-21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" edge="1" parent="1" source="H8-tO8T33itUdzrwufuf-11" target="H8-tO8T33itUdzrwufuf-12">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="R2tABX7njfLUj_4sBN_u" name="ERD">
    <mxGraphModel dx="1426" dy="757" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="xC0kqPzHEwQ2Z6sryzND-2" value="&lt;b&gt;users&lt;/b&gt;&#xa;PK user_id&#xa;email&#xa;password_hash&#xa;first_name&#xa;last_name&#xa;role&#xa;created_at" style="swimlane;whiteSpace=wrap;html=1;fillColor=#DAE8FC;swimlaneLine=1;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="400" y="20" width="200" height="180" as="geometry">
            <mxRectangle x="400" y="20" width="130" height="130" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xC0kqPzHEwQ2Z6sryzND-3" value="&lt;b&gt;student_profiles&lt;/b&gt;&#xa;PK student_id&#xa;FK user_id&#xa;additional_info" style="swimlane;whiteSpace=wrap;html=1;fillColor=#D5E8D4;verticalAlign=top;" parent="1" vertex="1" collapsed="1">
          <mxGeometry x="100" y="250" width="130" height="70" as="geometry">
            <mxRectangle x="100" y="250" width="200" height="100" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xC0kqPzHEwQ2Z6sryzND-4" value="&lt;b&gt;coach_profiles&lt;/b&gt;&#xa;PK coach_id&#xa;FK user_id&#xa;bio" style="swimlane;whiteSpace=wrap;html=1;fillColor=#F8CECC;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="750" y="220" width="200" height="100" as="geometry">
            <mxRectangle x="750" y="230" width="130" height="70" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xC0kqPzHEwQ2Z6sryzND-5" value="&lt;p style=&quot;margin-top: 0px; margin-bottom: 0px;&quot;&gt;&lt;b&gt;courses&lt;br&gt;&lt;/b&gt;PK course_id&lt;br&gt;FK coach_id&lt;br&gt;title&lt;br&gt;description&lt;br&gt;schedule&lt;br&gt;google_classroom_link&lt;br&gt;created_at&lt;/p&gt;" style="swimlane;whiteSpace=wrap;html=1;fillColor=#FFE6CC;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="740" y="410" width="220" height="160" as="geometry">
            <mxRectangle x="720" y="410" width="160" height="130" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xC0kqPzHEwQ2Z6sryzND-6" value="&lt;b&gt;lessons&lt;/b&gt;&#xa;PK lesson_id&#xa;FK course_id&#xa;title&#xa;description&#xa;lesson_order&#xa;material_link" style="swimlane;whiteSpace=wrap;html=1;fillColor=#FFF2CC;swimlaneLine=1;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="740" y="670" width="220" height="140" as="geometry">
            <mxRectangle x="745" y="670" width="110" height="110" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xC0kqPzHEwQ2Z6sryzND-7" value="&lt;b&gt;enrollments&lt;/b&gt;&#xa;PK enrollment_id&#xa;FK student_id&#xa;FK course_id&#xa;enrolled_at" style="swimlane;whiteSpace=wrap;html=1;fillColor=#E1D5E7;verticalAlign=top;" parent="1" vertex="1" collapsed="1">
          <mxGeometry x="100" y="445" width="130" height="90" as="geometry">
            <mxRectangle x="100" y="400" width="200" height="100" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xC0kqPzHEwQ2Z6sryzND-8" value="&lt;b&gt;feedback&lt;/b&gt;&#xa;PK feedback_id&#xa;FK enrollment_id&#xa;FK lesson_id&#xa;comment&#xa;created_at" style="swimlane;whiteSpace=wrap;html=1;fillColor=#E1D5E7;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="65" y="680" width="200" height="120" as="geometry" />
        </mxCell>
        <mxCell id="xC0kqPzHEwQ2Z6sryzND-9" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;" parent="1" source="xC0kqPzHEwQ2Z6sryzND-2" target="xC0kqPzHEwQ2Z6sryzND-3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="xC0kqPzHEwQ2Z6sryzND-19" value="1:1" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="xC0kqPzHEwQ2Z6sryzND-9" vertex="1" connectable="0">
          <mxGeometry x="0.4507" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xC0kqPzHEwQ2Z6sryzND-10" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;" parent="1" source="xC0kqPzHEwQ2Z6sryzND-2" target="xC0kqPzHEwQ2Z6sryzND-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="xC0kqPzHEwQ2Z6sryzND-20" value="1:1" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="xC0kqPzHEwQ2Z6sryzND-10" vertex="1" connectable="0">
          <mxGeometry x="0.2111" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xC0kqPzHEwQ2Z6sryzND-11" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="xC0kqPzHEwQ2Z6sryzND-4" target="xC0kqPzHEwQ2Z6sryzND-5" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xC0kqPzHEwQ2Z6sryzND-21" value="1:n" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="xC0kqPzHEwQ2Z6sryzND-11" vertex="1" connectable="0">
          <mxGeometry x="-0.1818" y="-4" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xC0kqPzHEwQ2Z6sryzND-12" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;" parent="1" source="xC0kqPzHEwQ2Z6sryzND-5" target="xC0kqPzHEwQ2Z6sryzND-6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="xC0kqPzHEwQ2Z6sryzND-22" value="1:n" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="xC0kqPzHEwQ2Z6sryzND-12" vertex="1" connectable="0">
          <mxGeometry x="-0.3" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xC0kqPzHEwQ2Z6sryzND-13" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;" parent="1" source="xC0kqPzHEwQ2Z6sryzND-3" target="xC0kqPzHEwQ2Z6sryzND-7" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="xC0kqPzHEwQ2Z6sryzND-23" value="1:n" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="xC0kqPzHEwQ2Z6sryzND-13" vertex="1" connectable="0">
          <mxGeometry x="-0.088" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xC0kqPzHEwQ2Z6sryzND-14" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;" parent="1" source="xC0kqPzHEwQ2Z6sryzND-5" target="xC0kqPzHEwQ2Z6sryzND-7" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="xC0kqPzHEwQ2Z6sryzND-24" value="1:n" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="xC0kqPzHEwQ2Z6sryzND-14" vertex="1" connectable="0">
          <mxGeometry x="0.0204" y="-3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xC0kqPzHEwQ2Z6sryzND-15" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;" parent="1" source="xC0kqPzHEwQ2Z6sryzND-7" target="xC0kqPzHEwQ2Z6sryzND-8" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="xC0kqPzHEwQ2Z6sryzND-25" value="1:n" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="xC0kqPzHEwQ2Z6sryzND-15" vertex="1" connectable="0">
          <mxGeometry x="-0.0621" y="3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xC0kqPzHEwQ2Z6sryzND-16" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;" parent="1" source="xC0kqPzHEwQ2Z6sryzND-6" target="xC0kqPzHEwQ2Z6sryzND-8" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZQXkqppLrBhmcVL03Bum-1" value="1:n&lt;div&gt;&lt;br&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="xC0kqPzHEwQ2Z6sryzND-16">
          <mxGeometry x="0.1495" y="3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
