#!/bin/bash
set -e

# OpenAPI spec file passed as the first argument
if [ "$#" -ne 1 ]; then
  echo "Usage: $0 <path_to_openapi_spec_file>"
  exit 255
fi

SPEC_FILE="$1"

# Docker image version
GENERATOR_IMAGE="openapitools/openapi-generator-cli:v7.5.0"

# Output directories
CLIENT_DIR="pkg"
MODEL_DIR="internal/persistence"
SERVER_DIR="internal/server"

# Cleanup old generated files
rm -rf "$CLIENT_DIR" "$MODEL_DIR" "$SERVER_DIR"

# Generate client code
docker run --rm \
  -v "${PWD}:/local" \
  ${GENERATOR_IMAGE} generate \
  -i "/local/${SPEC_FILE}" \
  -g go \
  -o "/local/${CLIENT_DIR}" \
  --global-property=apiTests=false,modelTests=false

# Generate server stub
docker run --rm \
  -v "${PWD}:/local" \
  ${GENERATOR_IMAGE} generate \
  -i "/local/${SPEC_FILE}" \
  -g go-server \
  -o "/local/${SERVER_DIR}" \
  --global-property=apiTests=false,modelTests=false \
  --additional-properties=router=chi

# Move models into internal/persistence
mkdir -p "${MODEL_DIR}"
mv "${SERVER_DIR}/model"/* "${MODEL_DIR}/"
rm -rf "${SERVER_DIR}/model"

echo "Generation completed."