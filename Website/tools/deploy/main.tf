
# Configure the DigitalOcean Provider
terraform {
  required_providers {
    digitalocean = {
      source  = "digitalocean/digitalocean"
      version = "~> 2.0"
    }
  }
}

# Configure the DigitalOcean Provider
provider "digitalocean" {
  token = var.do_token
}


# Create the App Platform app
resource "digitalocean_app" "static_website" {
  spec {
    name   = var.app_name
    region = "nyc"

    static_site {
      name               = "${var.app_name}-static"
      build_command      = var.build_command
      environment_slug   = var.environment_slug
      output_dir         = var.output_dir
      source_dir         = var.source_dir
      index_document     = "index.html"
      error_document     = var.use_spa_routing ? null : "404.html"
      catchall_document  = var.use_spa_routing ? "index.html" : null

      github {
        repo           = var.github_repo
        branch         = var.github_branch
        deploy_on_push = true
        # For private repos, include the access token
        # This will be set automatically if github_access_token is provided
      }
    }

    
    # Ingress configuration
    ingress {
      rule {
        component {
          name = "${var.app_name}-static"
        }
        match {
          path {
            prefix = "/"
          }
        }
      }
    }

    # Custom domain configuration
    dynamic "domain" {
      for_each = var.custom_domain != "" ? [1] : []
      content {
        name     = var.custom_domain
        type     = "PRIMARY"
        wildcard = false
      }
    }

    env {
      key   = "NODE_ENV"
      value = "staging"
    }
  }
}
