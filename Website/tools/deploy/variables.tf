# Variables
variable "do_token" {
  description = "DigitalOcean API token"
  type        = string
  sensitive   = true
}

variable "github_access_token" {
  description = "GitHub personal access token for private repos"
  type        = string
  sensitive   = true
  default     = ""
}

variable "app_name" {
  description = "Name of the app"
  type        = string
  default     = "my-static-website"
}

variable "github_repo" {
  description = "GitHub repository URL (e.g., your-username/your-repo)"
  type        = string
}

variable "github_branch" {
  description = "GitHub branch to deploy"
  type        = string
  default     = "main"
}

variable "source_dir" {
  description = "Source directory containing static files"
  type        = string
  default     = "/"
}

variable "build_command" {
  description = "Build command (leave empty for no build)"
  type        = string
  default     = ""
}

variable "output_dir" {
  description = "Output directory after build"
  type        = string
  default     = "/"
}

variable "environment_slug" {
  description = "Environment slug (e.g., html, react, vue)"
  type        = string
  default     = "html"
}

variable "instance_count" {
  description = "Number of instances"
  type        = number
  default     = 1
}

variable "instance_size_slug" {
  description = "Instance size"
  type        = string
  default     = "basic-xxs"
}

variable "custom_domain" {
  description = "Custom domain for the app (optional)"
  type        = string
  default     = ""
}

variable "use_spa_routing" {
  description = "Enable SPA routing (uses catchall_document instead of error_document)"
  type        = bool
  default     = false
}