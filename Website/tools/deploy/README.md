# Deployment Instructions for deploying STEMBlock website to Digital Ocean using Terraform

This README provides instructions for setting up and deploying STEMBlock website to Digital Ocean using Terraform.

## Prerequisites

1. **Terraform Installed**: Ensure you have Terraform installed on your local machine. You can download it from [terraform.io](https://www.terraform.io/downloads.html).
2. **Digital Ocean Account**: Ensure you have an Digital ocean API token. The existing API token and github token for STEMBlock-admin will be stored in the keeper

## Steps to Deploy

1. **Clone the Repository**:
   - Clone this repository to your local machine:
     ```
     git clone https://github.com/yourusername/do-static-website-terraform.git
     cd do-static-website-terraform/src
     ```

2. **Configure Variables**:
   - Create a file called `terraform.tfvars, with below content in `tools/deploy` folder
     ```
     do_token = "<Digital Ocean API token>"
     github_access_token = "<Github classic Personal Access Token with repo access and read:user access>"
     app_name = "stemblock-website"
     github_repo = "STEMBlock-Admin/stemblock-website"
     github_branch = "main"
     environment_slug = "html"
     build_command = ""
     source_dir = "/web"
     output_dir = "/"
     use_spa_routing = false
     ```

3. **Initialize Terraform**:
   - Run the following command to initialize Terraform:
     ```
     terraform init
     ```

4. **Plan the Deployment**:
   - Execute the plan command to see what resources will be created:
     ```
     terraform plan
     ```

5. **Apply the Configuration**:
   - Deploy the resources by running:
     ```
     terraform apply
     ```
   - Confirm the action when prompted.

6. **Access Your Website**:
   - Open a web browser and navigate to the domain outputted by above terraform apply command

## Future Work

Can enhance the security through using Let's encrypt certificate