// env.js - Environment configuration for Portal Test Frontend
// This file should be loaded before test.html to set environment variables

// =============================================================================
// ENVIRONMENT CONFIGURATION
// =============================================================================

// You can customize these values for your environment:

// Option 1: Set full URLs (takes precedence over ports)
// window.ENV_LOGIN_URL = 'http://localhost:8080';
// window.ENV_PORTAL_URL = 'http://localhost:8082';

// EXAMPLE: Uncomment these lines to test custom configuration
// window.ENV_LOGIN_PORT = '8080';
// window.ENV_PORTAL_PORT = '8082';
// window.ENV_DEBUG = 'true';

// Option 2: Set just the ports (will use localhost)
// window.ENV_LOGIN_PORT = '8080';
// window.ENV_PORTAL_PORT = '8082';

// Option 3: Set production URLs
// window.ENV_LOGIN_URL = 'https://stemblock-login-gljgs.ondigitalocean.app';
// window.ENV_PORTAL_URL = 'https://stemblock-portal-api.ondigitalocean.app';

// Debug mode (enables console logging)
// window.ENV_DEBUG = 'true';

// =============================================================================
// AUTO-DETECTION BASED ON CURRENT ENVIRONMENT
// =============================================================================

// Auto-detect environment and set defaults if not already set
(function() {
    const hostname = window.location.hostname;
    const isDevelopment = hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('192.168.');
    
    // Set defaults only if not already configured
    if (!window.ENV_LOGIN_URL && !window.ENV_LOGIN_PORT) {
        if (isDevelopment) {
            window.ENV_LOGIN_PORT = '8080';
        } else {
            window.ENV_LOGIN_URL = 'https://stemblock-login-gljgs.ondigitalocean.app';
        }
    }
    
    if (!window.ENV_PORTAL_URL && !window.ENV_PORTAL_PORT) {
        if (isDevelopment) {
            window.ENV_PORTAL_PORT = '8081';
        } else {
            window.ENV_PORTAL_URL = 'https://stemblock-portal-api.ondigitalocean.app';
        }
    }
    
    // Enable debug mode in development by default
    if (!window.ENV_DEBUG) {
        window.ENV_DEBUG = isDevelopment ? 'true' : 'false';
    }
    
    // Log configuration if debug is enabled
    if (window.ENV_DEBUG === 'true') {
        console.log('[Portal Test Environment] Auto-configuration applied:', {
            hostname: hostname,
            isDevelopment: isDevelopment,
            ENV_LOGIN_URL: window.ENV_LOGIN_URL,
            ENV_LOGIN_PORT: window.ENV_LOGIN_PORT,
            ENV_PORTAL_URL: window.ENV_PORTAL_URL,
            ENV_PORTAL_PORT: window.ENV_PORTAL_PORT,
            ENV_DEBUG: window.ENV_DEBUG
        });
    }
})();

// =============================================================================
// USAGE EXAMPLES
// =============================================================================

/*
Example 1: Custom Development Setup
-----------------------------------
// If your backends are running on different ports:
window.ENV_LOGIN_PORT = '9080';
window.ENV_PORTAL_PORT = '9082';

Example 2: Custom URLs
----------------------
// If you want to use specific URLs:
window.ENV_LOGIN_URL = 'http://*************:8080';
window.ENV_PORTAL_URL = 'http://*************:8082';

Example 3: Production Override
------------------------------
// Force production URLs even in development:
window.ENV_LOGIN_URL = 'https://stemblock-login-gljgs.ondigitalocean.app';
window.ENV_PORTAL_URL = 'https://stemblock-portal-api.ondigitalocean.app';

Example 4: Debug Mode
---------------------
// Enable detailed logging:
window.ENV_DEBUG = 'true';

To use these examples:
1. Uncomment the lines you want to use above
2. Modify the values as needed
3. Reload the test.html page
*/
