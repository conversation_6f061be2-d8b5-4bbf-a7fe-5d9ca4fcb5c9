<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portal Testing Suite</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            border-radius: 12px; 
            padding: 30px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        h1 { color: #333; text-align: center; margin-bottom: 10px; }
        h2 { color: #667eea; margin-bottom: 20px; border-bottom: 2px solid #eee; padding-bottom: 10px; }
        h3 { color: #555; margin-bottom: 15px; }
        
        /* Authentication Section */
        .auth-section { 
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); 
            padding: 25px; 
            border-radius: 12px; 
            margin-bottom: 30px; 
            border: 2px solid #dee2e6;
        }
        .auth-tabs { display: flex; gap: 10px; margin-bottom: 20px; }
        .tab-btn { 
            padding: 12px 24px; 
            border: none; 
            background: #e9ecef; 
            border-radius: 8px; 
            cursor: pointer; 
            transition: all 0.3s;
            font-weight: 500;
        }
        .tab-btn.active { background: #667eea; color: white; transform: translateY(-2px); }
        .auth-tab { display: none; }
        .auth-tab.active { display: block; }
        
        .auth-status { 
            margin-top: 20px; 
            padding: 20px; 
            background: white; 
            border-radius: 8px; 
            border-left: 4px solid #dc3545;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .auth-status.authenticated { border-left-color: #28a745; }
        .status-indicator { font-weight: bold; margin-bottom: 15px; font-size: 16px; }
        .token-display { margin-top: 15px; }
        .token-text { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 6px; 
            font-family: 'Courier New', monospace; 
            font-size: 11px; 
            word-break: break-all; 
            margin: 10px 0;
            border: 1px solid #dee2e6;
            max-height: 100px;
            overflow-y: auto;
        }
        
        /* Form Elements */
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 600; color: #495057; }
        input[type="text"], input[type="email"], input[type="password"], input[type="file"], textarea, select {
            width: 100%; 
            padding: 12px 16px; 
            border: 2px solid #e1e5e9; 
            border-radius: 8px; 
            font-size: 14px;
            transition: all 0.3s ease;
        }
        input:focus, textarea:focus, select:focus { 
            outline: none; 
            border-color: #667eea; 
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }
        
        /* Buttons */
        .btn { 
            padding: 12px 24px; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            font-size: 14px; 
            font-weight: 600; 
            transition: all 0.3s ease; 
            margin: 8px 4px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .btn-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .btn-success { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; }
        .btn-danger { background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%); color: white; }
        .btn-warning { background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: #212529; }
        .btn-sm { padding: 8px 16px; font-size: 12px; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 6px 20px rgba(0,0,0,0.15); }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
        
        /* Test Sections */
        .test-section { 
            background: #f8f9fa; 
            padding: 25px; 
            border-radius: 12px; 
            margin-bottom: 25px; 
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
        }
        .test-section:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(0,0,0,0.1); }
        .test-section.disabled { opacity: 0.6; pointer-events: none; }
        
        .endpoint-group { 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            margin-bottom: 20px; 
            border: 1px solid #e1e5e9;
            transition: all 0.3s ease;
        }
        .endpoint-group:hover { box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        
        .endpoint-title { 
            font-weight: bold; 
            margin-bottom: 15px; 
            display: flex; 
            align-items: center; 
            gap: 12px;
            font-size: 16px;
        }
        .method { 
            padding: 6px 12px; 
            border-radius: 6px; 
            color: white; 
            font-size: 12px; 
            font-weight: bold; 
            min-width: 60px; 
            text-align: center;
            text-transform: uppercase;
        }
        .get { background: linear-gradient(135deg, #61affe 0%, #4dabf7 100%); }
        .post { background: linear-gradient(135deg, #49cc90 0%, #38d9a9 100%); }
        .put { background: linear-gradient(135deg, #fca130 0%, #fd7e14 100%); }
        .delete { background: linear-gradient(135deg, #f93e3e 0%, #e83e8c 100%); }
        
        /* Response Display */
        .response { 
            background: #f8f9fa; 
            border: 2px solid #dee2e6; 
            border-radius: 8px; 
            padding: 20px; 
            margin-top: 15px; 
            white-space: pre-wrap; 
            font-family: 'Courier New', monospace; 
            font-size: 12px; 
            max-height: 400px; 
            overflow-y: auto;
            transition: all 0.3s ease;
        }
        .response.success { 
            border-color: #28a745; 
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); 
            color: #155724;
        }
        .response.error { 
            border-color: #dc3545; 
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); 
            color: #721c24;
        }
        
        /* Grid Layout */
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .grid-2 { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }

        /* CRUD Operation Styles */
        .crud-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .crud-operation {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .crud-operation h5 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 14px;
            font-weight: 600;
            text-align: center;
        }

        .form-group {
            margin-bottom: 8px;
        }

        .form-group label {
            display: block;
            font-size: 11px;
            color: #6c757d;
            margin-bottom: 3px;
            font-weight: 500;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 12px;
            box-sizing: border-box;
        }

        .form-group textarea {
            height: 50px;
            resize: vertical;
        }

        .btn-small {
            padding: 8px 12px;
            font-size: 12px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            width: 100%;
            margin-top: 8px;
            font-weight: 500;
        }

        .btn-create { background: #28a745; color: white; }
        .btn-read { background: #17a2b8; color: white; }
        .btn-update { background: #ffc107; color: #212529; }
        .btn-delete { background: #dc3545; color: white; }
        .btn-search { background: #6f42c1; color: white; }
        
        /* Status Indicators */
        .status-badge { 
            padding: 6px 12px; 
            border-radius: 20px; 
            font-size: 12px; 
            font-weight: bold; 
            margin-left: 12px;
            text-transform: uppercase;
        }
        .status-ready { background: #e3f2fd; color: #1565c0; }
        .status-working { background: #e8f5e8; color: #2e7d32; }
        .status-error { background: #ffebee; color: #c62828; }
        .status-testing { background: #fff3e0; color: #ef6c00; }
        
        /* Loading Animation */
        .loading { 
            display: inline-block; 
            width: 20px; 
            height: 20px; 
            border: 3px solid #f3f3f3; 
            border-top: 3px solid #667eea; 
            border-radius: 50%; 
            animation: spin 1s linear infinite; 
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        
        /* Notice Box */
        .notice { 
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); 
            border: 2px solid #ffc107; 
            border-radius: 10px; 
            padding: 20px; 
            margin-bottom: 25px; 
            color: #856404;
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.2);
        }
        .notice strong { color: #533f03; }
        
        /* Quick Actions */
        .quick-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .container { padding: 20px; margin: 10px; }
            .grid-2 { grid-template-columns: 1fr; }
            .auth-tabs { flex-direction: column; }
            .quick-actions { flex-direction: column; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Portal Testing Suite</h1>
        <p style="text-align: center; color: #666; margin-bottom: 20px; font-size: 16px;">
            Complete authentication + portal API testing workflow
        </p>

        <!-- Configuration Display -->
        <div class="config-display" style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
            <h3 style="margin-bottom: 10px; color: #495057;">🔧 Current Configuration</h3>
            <div id="config-info" style="font-family: monospace; font-size: 12px; color: #6c757d;">
                Loading configuration...
            </div>
        </div>
        
        <div class="notice">
            <strong>🔐 Authentication Required:</strong> This test suite integrates with the stemblock-login system. 
            Register or login to get a JWT token, then test all portal APIs with proper authentication.
        </div>
    </div>

    <!-- Step 1: Authentication -->
    <div class="container">
        <div class="auth-section" id="auth-section">
            <h2>🔐 Step 1: Authentication</h2>
            
            <div class="auth-tabs">
                <button class="tab-btn active" onclick="showAuthTab('login')">
                    🔑 Login
                </button>
                <button class="tab-btn" onclick="showAuthTab('register')">
                    👤 Register
                </button>
            </div>
            
            <!-- Login Tab -->
            <div id="login-tab" class="auth-tab active">
                <h3>Login to Portal</h3>
                <div class="grid-2">
                    <div>
                        <div class="form-group">
                            <label>Username:</label>
                            <input type="text" id="login-username" placeholder="Enter your username">
                        </div>
                        <div class="form-group">
                            <label>Password:</label>
                            <input type="password" id="login-password" placeholder="Enter your password">
                        </div>
                        <button onclick="handleLogin()" class="btn btn-primary">
                            🔑 Login
                        </button>
                    </div>
                    <div>
                        <h4>💡 Quick Test Accounts:</h4>
                        <p style="color: #666; margin-bottom: 15px;">
                            Use these pre-created accounts for testing:
                        </p>
                        <div class="quick-actions">
                            <button onclick="quickLogin('demo', 'Demo123!')" class="btn btn-sm">Demo User</button>
                            <button onclick="quickLogin('student1', 'Student123!')" class="btn btn-sm">Student 1</button>
                            <button onclick="quickLogin('teacher1', 'Teacher123!')" class="btn btn-sm">Teacher 1</button>
                        </div>
                        <p style="font-size: 12px; color: #666; margin-top: 10px;">
                            If these don't work, register a new account first.
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Register Tab -->
            <div id="register-tab" class="auth-tab">
                <h3>Register New Account</h3>
                <div class="grid-2">
                    <div>
                        <div class="form-group">
                            <label>Username:</label>
                            <input type="text" id="register-username" placeholder="Choose a username">
                        </div>
                        <div class="form-group">
                            <label>Email:</label>
                            <input type="email" id="register-email" placeholder="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label>Password:</label>
                            <input type="password" id="register-password" placeholder="Strong password (8+ chars)">
                        </div>
                        <button onclick="handleRegister()" class="btn btn-success">
                            👤 Register Account
                        </button>
                    </div>
                    <div>
                        <h4>📋 Password Requirements:</h4>
                        <ul style="color: #666; margin: 15px 0; padding-left: 20px;">
                            <li>At least 8 characters long</li>
                            <li>Include uppercase and lowercase letters</li>
                            <li>Include at least one number</li>
                            <li>Include at least one special character</li>
                        </ul>
                        <p style="font-size: 12px; color: #666;">
                            <strong>Note:</strong> You may need to verify your email before logging in.
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Auth Status -->
            <div class="auth-status" id="auth-status">
                <div class="status-indicator" id="status-indicator">❌ Not Authenticated</div>
                <div class="token-display" id="token-display" style="display: none;">
                    <strong>🎫 JWT Token:</strong>
                    <div class="token-text" id="token-text"></div>
                    <div style="margin-top: 10px;">
                        <button onclick="copyToken()" class="btn btn-sm">📋 Copy Token</button>
                        <button onclick="logout()" class="btn btn-sm btn-danger">🚪 Logout</button>
                        <button onclick="enablePortalTesting()" class="btn btn-sm btn-success">✅ Enable Portal Testing</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Step 2: Basic Portal Testing -->
    <div class="container" id="portal-testing" style="display: none;">
        <h2>🏥 Step 2: Basic Portal Testing</h2>
        <p style="color: #666; margin-bottom: 25px;">
            Test basic portal functionality: health check and dashboard access
        </p>

        <!-- Server Health -->
        <div class="test-section">
            <h3>🏥 Server Health Check</h3>
            <div class="endpoint-group">
                <div class="endpoint-title">
                    <span class="method get">GET</span>
                    <span>/health</span>
                    <span class="status-badge status-ready" id="health-status">Ready</span>
                </div>
                <button onclick="testHealth()" class="btn btn-primary">Test Health</button>
                <div id="health-response" class="response"></div>
            </div>
        </div>





        <!-- Dashboard -->
        <div class="test-section">
            <h3>🏠 Dashboard</h3>
            <div class="endpoint-group">
                <div class="endpoint-title">
                    <span class="method get">GET</span>
                    <span>/dashboard</span>
                    <span class="status-badge status-ready" id="dashboard-status">Ready</span>
                </div>
                <button onclick="testDashboard()" class="btn btn-primary">Get Dashboard</button>
                <div id="dashboard-response" class="response"></div>
            </div>
        </div>

        <!-- CRUD Testing Section -->
        <div class="test-section">
            <h3>🔧 Complete CRUD Operations Testing</h3>
            <p style="color: #666; margin-bottom: 20px;">
                Comprehensive testing interface for all 13 models with Create, Read, Update, Delete, and Search operations
            </p>

            <!-- CRUD Endpoints Summary -->
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
                <h4 style="margin-top: 0; color: #333;">📋 Available CRUD Endpoints</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; font-size: 14px;">
                    <div>
                        <strong>👥 User Management:</strong><br>
                        • <code>/api/users</code> - Users (Create, Read, Update, Delete, Search)<br>
                        • <code>/api/students</code> - Students (Create, Read, Update, Delete, Search)<br>
                        • <code>/api/coaches</code> - Coaches (Create, Read, Update, Delete, Search)
                    </div>
                    <div>
                        <strong>📚 Course Management:</strong><br>
                        • <code>/api/courses</code> - Courses (Create, Read, Update, Delete, Search)<br>
                        • <code>/api/enrollments</code> - Enrollments (Create, Read, Delete, Search)<br>
                        • <code>/api/materials</code> - Materials (Create, Read, Update, Delete, Search)
                    </div>
                    <div>
                        <strong>📝 Assignments & Quizzes:</strong><br>
                        • <code>/api/assignments</code> - Assignments (Create, Read, Update, Delete, Search)<br>
                        • <code>/api/quizzes</code> - Quizzes (Create, Read, Update, Delete, Search)<br>
                        • <code>/api/submissions</code> - Submissions (Create, Read, Update, Delete, Search)
                    </div>
                    <div>
                        <strong>📊 Academic Records:</strong><br>
                        • <code>/api/grades</code> - Grades (Create, Read, Update, Delete, Search)<br>
                        • <code>/api/attendances</code> - Attendance (Create, Read, Update, Delete, Search)
                    </div>
                    <div>
                        <strong>📢 Communication:</strong><br>
                        • <code>/api/announcements</code> - Announcements (Create, Read, Update, Delete, Search)<br>
                        • <code>/api/notifications</code> - Notifications (Create, Read, Update, Delete, Search)
                    </div>
                    <div>
                        <strong>🔐 Authentication:</strong><br>
                        • <code>/login</code> - User login<br>
                        • <code>/dashboard</code> - Student dashboard<br>
                        • All endpoints require JWT authentication
                    </div>
                </div>
                <div style="margin-top: 15px; padding: 10px; background: #e3f2fd; border-radius: 4px;">
                    <strong>🎯 Total: 13 Complete CRUD Models</strong> - All endpoints support standard HTTP methods (GET, POST, PUT, DELETE) with search and filtering capabilities.
                </div>
            </div>

            <!-- User CRUD -->
            <div class="endpoint-group">
                <div class="endpoint-title">
                    <span class="method get">CRUD</span>
                    <span>/api/users</span>
                    <span class="status-badge status-ready" id="users-crud-status">Ready</span>
                </div>

                <div class="crud-section">
                    <!-- Create User -->
                    <div class="crud-operation">
                        <h5>📝 Create User</h5>
                        <div class="form-group">
                            <label>Username *</label>
                            <input type="text" id="user-create-username" placeholder="Enter username">
                        </div>
                        <div class="form-group">
                            <label>Email *</label>
                            <input type="email" id="user-create-email" placeholder="Enter email">
                        </div>
                        <div class="form-group">
                            <label>First Name *</label>
                            <input type="text" id="user-create-firstname" placeholder="Enter first name">
                        </div>
                        <div class="form-group">
                            <label>Last Name *</label>
                            <input type="text" id="user-create-lastname" placeholder="Enter last name">
                        </div>
                        <div class="form-group">
                            <label>Role *</label>
                            <select id="user-create-role">
                                <option value="">Select role</option>
                                <option value="student">Student</option>
                                <option value="coach">Coach</option>
                                <option value="admin">Admin</option>
                            </select>
                        </div>
                        <button onclick="createUser()" class="btn-small btn-create">Create User</button>
                    </div>

                    <!-- Read/List Users -->
                    <div class="crud-operation">
                        <h5>📋 List Users</h5>
                        <div class="form-group">
                            <label>Search</label>
                            <input type="text" id="user-list-search" placeholder="Search by name/email">
                        </div>
                        <div class="form-group">
                            <label>Filter by Role</label>
                            <select id="user-list-role">
                                <option value="">All Roles</option>
                                <option value="student">Student</option>
                                <option value="coach">Coach</option>
                                <option value="admin">Admin</option>
                            </select>
                        </div>
                        <button onclick="listUsers()" class="btn-small btn-read">List All Users</button>
                    </div>

                    <!-- Search User by ID -->
                    <div class="crud-operation">
                        <h5>🔍 Search User by ID</h5>
                        <div class="form-group">
                            <label>User ID *</label>
                            <input type="text" id="user-search-id" placeholder="Enter user ID">
                        </div>
                        <button onclick="searchUserById()" class="btn-small btn-search">Search User</button>
                    </div>

                    <!-- Update User -->
                    <div class="crud-operation">
                        <h5>✏️ Update User</h5>
                        <div class="form-group">
                            <label>User ID *</label>
                            <input type="text" id="user-update-id" placeholder="Enter user ID">
                        </div>
                        <div class="form-group">
                            <label>Email</label>
                            <input type="email" id="user-update-email" placeholder="New email">
                        </div>
                        <div class="form-group">
                            <label>First Name</label>
                            <input type="text" id="user-update-firstname" placeholder="New first name">
                        </div>
                        <div class="form-group">
                            <label>Last Name</label>
                            <input type="text" id="user-update-lastname" placeholder="New last name">
                        </div>
                        <button onclick="updateUser()" class="btn-small btn-update">Update User</button>
                    </div>

                    <!-- Delete User -->
                    <div class="crud-operation">
                        <h5>🗑️ Delete User</h5>
                        <div class="form-group">
                            <label>User ID *</label>
                            <input type="text" id="user-delete-id" placeholder="Enter user ID to delete">
                        </div>
                        <button onclick="deleteUser()" class="btn-small btn-delete">Delete User</button>
                    </div>
                </div>

                <div id="users-crud-response" class="response"></div>
            </div>

            <!-- Student CRUD -->
            <div class="endpoint-group">
                <div class="endpoint-title">
                    <span class="method get">CRUD</span>
                    <span>/api/students</span>
                    <span class="status-badge status-ready" id="students-crud-status">Ready</span>
                </div>

                <div class="crud-section">
                    <!-- Create Student -->
                    <div class="crud-operation">
                        <h5>📝 Create Student</h5>
                        <div class="form-group">
                            <label>User ID *</label>
                            <input type="text" id="student-create-userid" placeholder="Enter user ID">
                        </div>
                        <button onclick="createStudent()" class="btn-small btn-create">Create Student</button>
                    </div>

                    <!-- List Students -->
                    <div class="crud-operation">
                        <h5>📋 List Students</h5>
                        <div class="form-group">
                            <label>Search</label>
                            <input type="text" id="student-list-search" placeholder="Search by user info">
                        </div>
                        <button onclick="listStudents()" class="btn-small btn-read">List All Students</button>
                    </div>

                    <!-- Search Student by ID -->
                    <div class="crud-operation">
                        <h5>🔍 Search Student by ID</h5>
                        <div class="form-group">
                            <label>Student ID *</label>
                            <input type="text" id="student-search-id" placeholder="Enter student ID">
                        </div>
                        <button onclick="searchStudentById()" class="btn-small btn-search">Search Student</button>
                    </div>

                    <!-- Update Student -->
                    <div class="crud-operation">
                        <h5>✏️ Update Student</h5>
                        <div class="form-group">
                            <label>Student ID *</label>
                            <input type="text" id="student-update-id" placeholder="Enter student ID">
                        </div>
                        <div class="form-group">
                            <label>User ID</label>
                            <input type="text" id="student-update-userid" placeholder="New user ID">
                        </div>
                        <button onclick="updateStudent()" class="btn-small btn-update">Update Student</button>
                    </div>

                    <!-- Delete Student -->
                    <div class="crud-operation">
                        <h5>🗑️ Delete Student</h5>
                        <div class="form-group">
                            <label>Student ID *</label>
                            <input type="text" id="student-delete-id" placeholder="Enter student ID to delete">
                        </div>
                        <button onclick="deleteStudent()" class="btn-small btn-delete">Delete Student</button>
                    </div>
                </div>

                <div id="students-crud-response" class="response"></div>
            </div>

            <!-- Coach CRUD -->
            <div class="endpoint-group">
                <div class="endpoint-title">
                    <span class="method get">CRUD</span>
                    <span>/api/coaches</span>
                    <span class="status-badge status-ready" id="coaches-crud-status">Ready</span>
                </div>

                <div class="crud-section">
                    <!-- Create Coach -->
                    <div class="crud-operation">
                        <h5>📝 Create Coach</h5>
                        <div class="form-group">
                            <label>User ID *</label>
                            <input type="text" id="coach-create-userid" placeholder="Enter user ID">
                        </div>
                        <button onclick="createCoach()" class="btn-small btn-create">Create Coach</button>
                    </div>

                    <!-- List Coaches -->
                    <div class="crud-operation">
                        <h5>📋 List Coaches</h5>
                        <div class="form-group">
                            <label>Search</label>
                            <input type="text" id="coach-list-search" placeholder="Search by user info">
                        </div>
                        <button onclick="listCoaches()" class="btn-small btn-read">List All Coaches</button>
                    </div>

                    <!-- Search Coach by ID -->
                    <div class="crud-operation">
                        <h5>🔍 Search Coach by ID</h5>
                        <div class="form-group">
                            <label>Coach ID *</label>
                            <input type="text" id="coach-search-id" placeholder="Enter coach ID">
                        </div>
                        <button onclick="searchCoachById()" class="btn-small btn-search">Search Coach</button>
                    </div>

                    <!-- Update Coach -->
                    <div class="crud-operation">
                        <h5>✏️ Update Coach</h5>
                        <div class="form-group">
                            <label>Coach ID *</label>
                            <input type="text" id="coach-update-id" placeholder="Enter coach ID">
                        </div>
                        <div class="form-group">
                            <label>User ID</label>
                            <input type="text" id="coach-update-userid" placeholder="New user ID">
                        </div>
                        <button onclick="updateCoach()" class="btn-small btn-update">Update Coach</button>
                    </div>

                    <!-- Delete Coach -->
                    <div class="crud-operation">
                        <h5>🗑️ Delete Coach</h5>
                        <div class="form-group">
                            <label>Coach ID *</label>
                            <input type="text" id="coach-delete-id" placeholder="Enter coach ID to delete">
                        </div>
                        <button onclick="deleteCoach()" class="btn-small btn-delete">Delete Coach</button>
                    </div>
                </div>

                <div id="coaches-crud-response" class="response"></div>
            </div>

            <!-- Course CRUD -->
            <div class="endpoint-group">
                <div class="endpoint-title">
                    <span class="method get">CRUD</span>
                    <span>/api/courses</span>
                    <span class="status-badge status-ready" id="courses-crud-status">Ready</span>
                </div>

                <div class="crud-section">
                    <!-- Create Course -->
                    <div class="crud-operation">
                        <h5>📝 Create Course</h5>
                        <div class="form-group">
                            <label>Name *</label>
                            <input type="text" id="course-create-name" placeholder="Enter course name">
                        </div>
                        <div class="form-group">
                            <label>Description *</label>
                            <textarea id="course-create-description" placeholder="Enter course description"></textarea>
                        </div>
                        <div class="form-group">
                            <label>Instructor *</label>
                            <input type="text" id="course-create-instructor" placeholder="Enter instructor name">
                        </div>
                        <div class="form-group">
                            <label>Schedule</label>
                            <input type="text" id="course-create-schedule" placeholder="Enter schedule">
                        </div>
                        <button onclick="createCourse()" class="btn-small btn-create">Create Course</button>
                    </div>

                    <!-- List Courses -->
                    <div class="crud-operation">
                        <h5>📋 List Courses</h5>
                        <div class="form-group">
                            <label>Search</label>
                            <input type="text" id="course-list-search" placeholder="Search by name/description">
                        </div>
                        <div class="form-group">
                            <label>Filter by Instructor</label>
                            <input type="text" id="course-list-instructor" placeholder="Filter by instructor">
                        </div>
                        <button onclick="listCourses()" class="btn-small btn-read">List All Courses</button>
                    </div>

                    <!-- Search Course by ID -->
                    <div class="crud-operation">
                        <h5>🔍 Search Course by ID</h5>
                        <div class="form-group">
                            <label>Course ID *</label>
                            <input type="text" id="course-search-id" placeholder="Enter course ID">
                        </div>
                        <button onclick="searchCourseById()" class="btn-small btn-search">Search Course</button>
                    </div>

                    <!-- Update Course -->
                    <div class="crud-operation">
                        <h5>✏️ Update Course</h5>
                        <div class="form-group">
                            <label>Course ID *</label>
                            <input type="text" id="course-update-id" placeholder="Enter course ID">
                        </div>
                        <div class="form-group">
                            <label>Name</label>
                            <input type="text" id="course-update-name" placeholder="New course name">
                        </div>
                        <div class="form-group">
                            <label>Description</label>
                            <textarea id="course-update-description" placeholder="New description"></textarea>
                        </div>
                        <div class="form-group">
                            <label>Instructor</label>
                            <input type="text" id="course-update-instructor" placeholder="New instructor">
                        </div>
                        <button onclick="updateCourse()" class="btn-small btn-update">Update Course</button>
                    </div>

                    <!-- Delete Course -->
                    <div class="crud-operation">
                        <h5>🗑️ Delete Course</h5>
                        <div class="form-group">
                            <label>Course ID *</label>
                            <input type="text" id="course-delete-id" placeholder="Enter course ID to delete">
                        </div>
                        <button onclick="deleteCourse()" class="btn-small btn-delete">Delete Course</button>
                    </div>
                </div>

                <div id="courses-crud-response" class="response"></div>
            </div>

            <!-- Assignment CRUD -->
            <div class="endpoint-group">
                <div class="endpoint-title">
                    <span class="method get">CRUD</span>
                    <span>/api/assignments</span>
                    <span class="status-badge status-ready" id="assignments-crud-status">Ready</span>
                </div>

                <div class="crud-section">
                    <!-- Create Assignment -->
                    <div class="crud-operation">
                        <h5>📝 Create Assignment</h5>
                        <div class="form-group">
                            <label>Title *</label>
                            <input type="text" id="assignment-create-title" placeholder="Enter assignment title">
                        </div>
                        <div class="form-group">
                            <label>Description *</label>
                            <textarea id="assignment-create-description" placeholder="Enter assignment description"></textarea>
                        </div>
                        <div class="form-group">
                            <label>Course ID *</label>
                            <input type="text" id="assignment-create-courseid" placeholder="Enter course ID">
                        </div>
                        <div class="form-group">
                            <label>Due Date (optional)</label>
                            <input type="datetime-local" id="assignment-create-duedate" title="Select date and time for assignment due date">
                        </div>
                        <button onclick="createAssignment()" class="btn-small btn-create">Create Assignment</button>
                    </div>

                    <!-- List Assignments -->
                    <div class="crud-operation">
                        <h5>📋 List Assignments</h5>
                        <div class="form-group">
                            <label>Search</label>
                            <input type="text" id="assignment-list-search" placeholder="Search by title/description">
                        </div>
                        <div class="form-group">
                            <label>Filter by Course ID</label>
                            <input type="text" id="assignment-list-courseid" placeholder="Filter by course ID">
                        </div>
                        <div class="form-group">
                            <label>Filter by Status</label>
                            <select id="assignment-list-status">
                                <option value="">All Statuses</option>
                                <option value="draft">Draft</option>
                                <option value="published">Published</option>
                                <option value="closed">Closed</option>
                            </select>
                        </div>
                        <button onclick="listAssignments()" class="btn-small btn-read">List All Assignments</button>
                    </div>

                    <!-- Search Assignment by ID -->
                    <div class="crud-operation">
                        <h5>🔍 Search Assignment by ID</h5>
                        <div class="form-group">
                            <label>Assignment ID *</label>
                            <input type="text" id="assignment-search-id" placeholder="Enter assignment ID">
                        </div>
                        <button onclick="searchAssignmentById()" class="btn-small btn-search">Search Assignment</button>
                    </div>

                    <!-- Update Assignment -->
                    <div class="crud-operation">
                        <h5>✏️ Update Assignment</h5>
                        <div class="form-group">
                            <label>Assignment ID *</label>
                            <input type="text" id="assignment-update-id" placeholder="Enter assignment ID">
                        </div>
                        <div class="form-group">
                            <label>Title</label>
                            <input type="text" id="assignment-update-title" placeholder="New title">
                        </div>
                        <div class="form-group">
                            <label>Description</label>
                            <textarea id="assignment-update-description" placeholder="New description"></textarea>
                        </div>
                        <div class="form-group">
                            <label>Due Date</label>
                            <input type="datetime-local" id="assignment-update-duedate">
                        </div>
                        <button onclick="updateAssignment()" class="btn-small btn-update">Update Assignment</button>
                    </div>

                    <!-- Delete Assignment -->
                    <div class="crud-operation">
                        <h5>🗑️ Delete Assignment</h5>
                        <div class="form-group">
                            <label>Assignment ID *</label>
                            <input type="text" id="assignment-delete-id" placeholder="Enter assignment ID to delete">
                        </div>
                        <button onclick="deleteAssignment()" class="btn-small btn-delete">Delete Assignment</button>
                    </div>
                </div>

                <div id="assignments-crud-response" class="response"></div>
            </div>

            <!-- Quiz CRUD -->
            <div class="endpoint-group">
                <div class="endpoint-title">
                    <span class="method get">CRUD</span>
                    <span>/api/quizzes</span>
                    <span class="status-badge status-ready" id="quizzes-crud-status">Ready</span>
                </div>

                <div class="crud-section">
                    <!-- Create Quiz -->
                    <div class="crud-operation">
                        <h5>📝 Create Quiz</h5>
                        <div class="form-group">
                            <label>Title *</label>
                            <input type="text" id="quiz-create-title" placeholder="Enter quiz title">
                        </div>
                        <div class="form-group">
                            <label>Description *</label>
                            <textarea id="quiz-create-description" placeholder="Enter quiz description"></textarea>
                        </div>
                        <div class="form-group">
                            <label>Course ID *</label>
                            <input type="text" id="quiz-create-courseid" placeholder="Enter course ID">
                        </div>
                        <div class="form-group">
                            <label>Due Date</label>
                            <input type="datetime-local" id="quiz-create-duedate">
                        </div>
                        <button onclick="createQuiz()" class="btn-small btn-create">Create Quiz</button>
                    </div>

                    <!-- List Quizzes -->
                    <div class="crud-operation">
                        <h5>📋 List Quizzes</h5>
                        <div class="form-group">
                            <label>Search</label>
                            <input type="text" id="quiz-list-search" placeholder="Search by title/description">
                        </div>
                        <div class="form-group">
                            <label>Filter by Course ID</label>
                            <input type="text" id="quiz-list-courseid" placeholder="Filter by course ID">
                        </div>
                        <div class="form-group">
                            <label>Filter by Status</label>
                            <select id="quiz-list-status">
                                <option value="">All Statuses</option>
                                <option value="draft">Draft</option>
                                <option value="published">Published</option>
                                <option value="closed">Closed</option>
                            </select>
                        </div>
                        <button onclick="listQuizzes()" class="btn-small btn-read">List All Quizzes</button>
                    </div>

                    <!-- Search Quiz by ID -->
                    <div class="crud-operation">
                        <h5>🔍 Search Quiz by ID</h5>
                        <div class="form-group">
                            <label>Quiz ID *</label>
                            <input type="text" id="quiz-search-id" placeholder="Enter quiz ID">
                        </div>
                        <button onclick="searchQuizById()" class="btn-small btn-search">Search Quiz</button>
                    </div>

                    <!-- Update Quiz -->
                    <div class="crud-operation">
                        <h5>✏️ Update Quiz</h5>
                        <div class="form-group">
                            <label>Quiz ID *</label>
                            <input type="text" id="quiz-update-id" placeholder="Enter quiz ID">
                        </div>
                        <div class="form-group">
                            <label>Title</label>
                            <input type="text" id="quiz-update-title" placeholder="New title">
                        </div>
                        <div class="form-group">
                            <label>Description</label>
                            <textarea id="quiz-update-description" placeholder="New description"></textarea>
                        </div>
                        <div class="form-group">
                            <label>Due Date</label>
                            <input type="datetime-local" id="quiz-update-duedate">
                        </div>
                        <button onclick="updateQuiz()" class="btn-small btn-update">Update Quiz</button>
                    </div>

                    <!-- Delete Quiz -->
                    <div class="crud-operation">
                        <h5>🗑️ Delete Quiz</h5>
                        <div class="form-group">
                            <label>Quiz ID *</label>
                            <input type="text" id="quiz-delete-id" placeholder="Enter quiz ID to delete">
                        </div>
                        <button onclick="deleteQuiz()" class="btn-small btn-delete">Delete Quiz</button>
                    </div>
                </div>

                <div id="quizzes-crud-response" class="response"></div>
            </div>

            <!-- Announcement CRUD -->
            <div class="endpoint-group">
                <div class="endpoint-title">
                    <span class="method get">CRUD</span>
                    <span>/api/announcements</span>
                    <span class="status-badge status-ready" id="announcements-crud-status">Ready</span>
                </div>

                <div class="crud-section">
                    <!-- Create Announcement -->
                    <div class="crud-operation">
                        <h5>📝 Create Announcement</h5>
                        <div class="form-group">
                            <label>Title *</label>
                            <input type="text" id="announcement-create-title" placeholder="Enter announcement title">
                        </div>
                        <div class="form-group">
                            <label>Content *</label>
                            <textarea id="announcement-create-content" placeholder="Enter announcement content"></textarea>
                        </div>
                        <div class="form-group">
                            <label>Course ID *</label>
                            <input type="text" id="announcement-create-courseid" placeholder="Enter course ID">
                        </div>
                        <button onclick="createAnnouncement()" class="btn-small btn-create">Create Announcement</button>
                    </div>

                    <!-- List Announcements -->
                    <div class="crud-operation">
                        <h5>📋 List Announcements</h5>
                        <div class="form-group">
                            <label>Search</label>
                            <input type="text" id="announcement-list-search" placeholder="Search by title/content">
                        </div>
                        <div class="form-group">
                            <label>Filter by Course ID</label>
                            <input type="text" id="announcement-list-courseid" placeholder="Filter by course ID">
                        </div>
                        <button onclick="listAnnouncements()" class="btn-small btn-read">List All Announcements</button>
                    </div>

                    <!-- Search Announcement by ID -->
                    <div class="crud-operation">
                        <h5>🔍 Search Announcement by ID</h5>
                        <div class="form-group">
                            <label>Announcement ID *</label>
                            <input type="text" id="announcement-search-id" placeholder="Enter announcement ID">
                        </div>
                        <button onclick="searchAnnouncementById()" class="btn-small btn-search">Search Announcement</button>
                    </div>

                    <!-- Update Announcement -->
                    <div class="crud-operation">
                        <h5>✏️ Update Announcement</h5>
                        <div class="form-group">
                            <label>Announcement ID *</label>
                            <input type="text" id="announcement-update-id" placeholder="Enter announcement ID">
                        </div>
                        <div class="form-group">
                            <label>Title</label>
                            <input type="text" id="announcement-update-title" placeholder="New title">
                        </div>
                        <div class="form-group">
                            <label>Content</label>
                            <textarea id="announcement-update-content" placeholder="New content"></textarea>
                        </div>
                        <button onclick="updateAnnouncement()" class="btn-small btn-update">Update Announcement</button>
                    </div>

                    <!-- Delete Announcement -->
                    <div class="crud-operation">
                        <h5>🗑️ Delete Announcement</h5>
                        <div class="form-group">
                            <label>Announcement ID *</label>
                            <input type="text" id="announcement-delete-id" placeholder="Enter announcement ID to delete">
                        </div>
                        <button onclick="deleteAnnouncement()" class="btn-small btn-delete">Delete Announcement</button>
                    </div>
                </div>

                <div id="announcements-crud-response" class="response"></div>
            </div>

            <!-- Notification CRUD -->
            <div class="endpoint-group">
                <div class="endpoint-title">
                    <span class="method get">CRUD</span>
                    <span>/api/notifications</span>
                    <span class="status-badge status-ready" id="notifications-crud-status">Ready</span>
                </div>

                <div class="crud-section">
                    <!-- Create Notification -->
                    <div class="crud-operation">
                        <h5>📝 Create Notification</h5>
                        <div class="form-group">
                            <label>Message *</label>
                            <textarea id="notification-create-message" placeholder="Enter notification message"></textarea>
                        </div>
                        <div class="form-group">
                            <label>Student ID *</label>
                            <input type="text" id="notification-create-studentid" placeholder="Enter student ID">
                        </div>
                        <div class="form-group">
                            <label>Type</label>
                            <select id="notification-create-type">
                                <option value="">Select type</option>
                                <option value="info">Info</option>
                                <option value="warning">Warning</option>
                                <option value="success">Success</option>
                                <option value="error">Error</option>
                            </select>
                        </div>
                        <button onclick="createNotification()" class="btn-small btn-create">Create Notification</button>
                    </div>

                    <!-- List Notifications -->
                    <div class="crud-operation">
                        <h5>📋 List Notifications</h5>
                        <div class="form-group">
                            <label>Search</label>
                            <input type="text" id="notification-list-search" placeholder="Search by message">
                        </div>
                        <div class="form-group">
                            <label>Filter by Student ID</label>
                            <input type="text" id="notification-list-studentid" placeholder="Filter by student ID">
                        </div>
                        <button onclick="listNotifications()" class="btn-small btn-read">List All Notifications</button>
                    </div>

                    <!-- Search Notification by ID -->
                    <div class="crud-operation">
                        <h5>🔍 Search Notification by ID</h5>
                        <div class="form-group">
                            <label>Notification ID *</label>
                            <input type="text" id="notification-search-id" placeholder="Enter notification ID">
                        </div>
                        <button onclick="searchNotificationById()" class="btn-small btn-search">Search Notification</button>
                    </div>

                    <!-- Update Notification -->
                    <div class="crud-operation">
                        <h5>✏️ Update Notification</h5>
                        <div class="form-group">
                            <label>Notification ID *</label>
                            <input type="text" id="notification-update-id" placeholder="Enter notification ID">
                        </div>
                        <div class="form-group">
                            <label>Message</label>
                            <textarea id="notification-update-message" placeholder="New message"></textarea>
                        </div>
                        <div class="form-group">
                            <label>Type</label>
                            <select id="notification-update-type">
                                <option value="">Keep current</option>
                                <option value="info">Info</option>
                                <option value="warning">Warning</option>
                                <option value="success">Success</option>
                                <option value="error">Error</option>
                            </select>
                        </div>
                        <button onclick="updateNotification()" class="btn-small btn-update">Update Notification</button>
                    </div>

                    <!-- Delete Notification -->
                    <div class="crud-operation">
                        <h5>🗑️ Delete Notification</h5>
                        <div class="form-group">
                            <label>Notification ID *</label>
                            <input type="text" id="notification-delete-id" placeholder="Enter notification ID to delete">
                        </div>
                        <button onclick="deleteNotification()" class="btn-small btn-delete">Delete Notification</button>
                    </div>
                </div>

                <div id="notifications-crud-response" class="response"></div>
            </div>

            <!-- Submission CRUD -->
            <div class="endpoint-group">
                <div class="endpoint-title">
                    <span class="method get">CRUD</span>
                    <span>/api/submissions</span>
                    <span class="status-badge status-ready" id="submissions-crud-status">Ready</span>
                </div>

                <div class="crud-section">
                    <!-- Create Submission -->
                    <div class="crud-operation">
                        <h5>📝 Create Submission</h5>
                        <div class="form-group">
                            <label>Assignment ID *</label>
                            <input type="text" id="submission-create-assignmentid" placeholder="Enter assignment ID">
                        </div>
                        <div class="form-group">
                            <label>Student ID *</label>
                            <input type="text" id="submission-create-studentid" placeholder="Enter student ID">
                        </div>
                        <div class="form-group">
                            <label>Status</label>
                            <select id="submission-create-status">
                                <option value="draft">Draft</option>
                                <option value="submitted">Submitted</option>
                                <option value="graded">Graded</option>
                            </select>
                        </div>
                        <button onclick="createSubmission()" class="btn-small btn-create">Create Submission</button>
                    </div>

                    <!-- List Submissions -->
                    <div class="crud-operation">
                        <h5>📋 List Submissions</h5>
                        <div class="form-group">
                            <label>Search</label>
                            <input type="text" id="submission-list-search" placeholder="Search by status">
                        </div>
                        <div class="form-group">
                            <label>Filter by Student ID</label>
                            <input type="text" id="submission-list-studentid" placeholder="Filter by student ID">
                        </div>
                        <div class="form-group">
                            <label>Filter by Assignment ID</label>
                            <input type="text" id="submission-list-assignmentid" placeholder="Filter by assignment ID">
                        </div>
                        <button onclick="listSubmissions()" class="btn-small btn-read">List All Submissions</button>
                    </div>

                    <!-- Search Submission by ID -->
                    <div class="crud-operation">
                        <h5>🔍 Search Submission by ID</h5>
                        <div class="form-group">
                            <label>Submission ID *</label>
                            <input type="text" id="submission-search-id" placeholder="Enter submission ID">
                        </div>
                        <button onclick="searchSubmissionById()" class="btn-small btn-search">Search Submission</button>
                    </div>

                    <!-- Update Submission -->
                    <div class="crud-operation">
                        <h5>✏️ Update Submission</h5>
                        <div class="form-group">
                            <label>Submission ID *</label>
                            <input type="text" id="submission-update-id" placeholder="Enter submission ID">
                        </div>
                        <div class="form-group">
                            <label>Status</label>
                            <select id="submission-update-status">
                                <option value="">Keep current</option>
                                <option value="draft">Draft</option>
                                <option value="submitted">Submitted</option>
                                <option value="graded">Graded</option>
                            </select>
                        </div>
                        <button onclick="updateSubmission()" class="btn-small btn-update">Update Submission</button>
                    </div>

                    <!-- Delete Submission -->
                    <div class="crud-operation">
                        <h5>🗑️ Delete Submission</h5>
                        <div class="form-group">
                            <label>Submission ID *</label>
                            <input type="text" id="submission-delete-id" placeholder="Enter submission ID to delete">
                        </div>
                        <button onclick="deleteSubmission()" class="btn-small btn-delete">Delete Submission</button>
                    </div>
                </div>

                <div id="submissions-crud-response" class="response"></div>
            </div>

            <!-- Material CRUD -->
            <div class="endpoint-group">
                <div class="endpoint-title">
                    <span class="method get">CRUD</span>
                    <span>/api/materials</span>
                    <span class="status-badge status-ready" id="materials-crud-status">Ready</span>
                </div>

                <div class="crud-section">
                    <!-- Create Material -->
                    <div class="crud-operation">
                        <h5>📝 Create Material</h5>
                        <div class="form-group">
                            <label>Title *</label>
                            <input type="text" id="material-create-title" placeholder="Enter material title">
                        </div>
                        <div class="form-group">
                            <label>Description *</label>
                            <textarea id="material-create-description" placeholder="Enter material description"></textarea>
                        </div>
                        <div class="form-group">
                            <label>Course ID *</label>
                            <input type="text" id="material-create-courseid" placeholder="Enter course ID">
                        </div>
                        <div class="form-group">
                            <label>Uploaded By *</label>
                            <input type="text" id="material-create-uploadedby" placeholder="Enter uploader name">
                        </div>
                        <button onclick="createMaterial()" class="btn-small btn-create">Create Material</button>
                    </div>

                    <!-- List Materials -->
                    <div class="crud-operation">
                        <h5>📋 List Materials</h5>
                        <div class="form-group">
                            <label>Search</label>
                            <input type="text" id="material-list-search" placeholder="Search by title/description">
                        </div>
                        <div class="form-group">
                            <label>Filter by Course ID</label>
                            <input type="text" id="material-list-courseid" placeholder="Filter by course ID">
                        </div>
                        <button onclick="listMaterials()" class="btn-small btn-read">List All Materials</button>
                    </div>

                    <!-- Search Material by ID -->
                    <div class="crud-operation">
                        <h5>🔍 Search Material by ID</h5>
                        <div class="form-group">
                            <label>Material ID *</label>
                            <input type="text" id="material-search-id" placeholder="Enter material ID">
                        </div>
                        <button onclick="searchMaterialById()" class="btn-small btn-search">Search Material</button>
                    </div>

                    <!-- Update Material -->
                    <div class="crud-operation">
                        <h5>✏️ Update Material</h5>
                        <div class="form-group">
                            <label>Material ID *</label>
                            <input type="text" id="material-update-id" placeholder="Enter material ID">
                        </div>
                        <div class="form-group">
                            <label>Title</label>
                            <input type="text" id="material-update-title" placeholder="New title">
                        </div>
                        <div class="form-group">
                            <label>Description</label>
                            <textarea id="material-update-description" placeholder="New description"></textarea>
                        </div>
                        <button onclick="updateMaterial()" class="btn-small btn-update">Update Material</button>
                    </div>

                    <!-- Delete Material -->
                    <div class="crud-operation">
                        <h5>🗑️ Delete Material</h5>
                        <div class="form-group">
                            <label>Material ID *</label>
                            <input type="text" id="material-delete-id" placeholder="Enter material ID to delete">
                        </div>
                        <button onclick="deleteMaterial()" class="btn-small btn-delete">Delete Material</button>
                    </div>
                </div>

                <div id="materials-crud-response" class="response"></div>
            </div>

            <!-- Enrollment CRUD -->
            <div class="endpoint-group">
                <div class="endpoint-title">
                    <span class="method get">CRUD</span>
                    <span>/api/enrollments</span>
                    <span class="status-badge status-ready" id="enrollments-crud-status">Ready</span>
                </div>

                <div class="crud-section">
                    <!-- Create Enrollment -->
                    <div class="crud-operation">
                        <h5>📝 Create Enrollment</h5>
                        <div class="form-group">
                            <label>Student ID *</label>
                            <input type="text" id="enrollment-create-studentid" placeholder="Enter student ID">
                        </div>
                        <div class="form-group">
                            <label>Course ID *</label>
                            <input type="text" id="enrollment-create-courseid" placeholder="Enter course ID">
                        </div>
                        <button onclick="createEnrollment()" class="btn-small btn-create">Create Enrollment</button>
                    </div>

                    <!-- List Enrollments -->
                    <div class="crud-operation">
                        <h5>📋 List Enrollments</h5>
                        <div class="form-group">
                            <label>Filter by Student ID</label>
                            <input type="text" id="enrollment-list-studentid" placeholder="Filter by student ID">
                        </div>
                        <div class="form-group">
                            <label>Filter by Course ID</label>
                            <input type="text" id="enrollment-list-courseid" placeholder="Filter by course ID">
                        </div>
                        <button onclick="listEnrollments()" class="btn-small btn-read">List All Enrollments</button>
                    </div>

                    <!-- Search Enrollment by ID -->
                    <div class="crud-operation">
                        <h5>🔍 Search Enrollment by ID</h5>
                        <div class="form-group">
                            <label>Enrollment ID *</label>
                            <input type="text" id="enrollment-search-id" placeholder="Enter enrollment ID">
                        </div>
                        <button onclick="searchEnrollmentById()" class="btn-small btn-search">Search Enrollment</button>
                    </div>

                    <!-- Delete Enrollment -->
                    <div class="crud-operation">
                        <h5>🗑️ Delete Enrollment</h5>
                        <div class="form-group">
                            <label>Enrollment ID *</label>
                            <input type="text" id="enrollment-delete-id" placeholder="Enter enrollment ID to delete">
                        </div>
                        <button onclick="deleteEnrollment()" class="btn-small btn-delete">Delete Enrollment</button>
                    </div>
                </div>

                <div id="enrollments-crud-response" class="response"></div>
            </div>

            <!-- Grade CRUD -->
            <div class="endpoint-group">
                <div class="endpoint-title">
                    <span class="method get">CRUD</span>
                    <span>/api/grades</span>
                    <span class="status-badge status-ready" id="grades-crud-status">Ready</span>
                </div>

                <div class="crud-section">
                    <!-- Create Grade -->
                    <div class="crud-operation">
                        <h5>📝 Create Grade</h5>
                        <div class="form-group">
                            <label>Student ID *</label>
                            <input type="text" id="grade-create-studentid" placeholder="Enter student ID">
                        </div>
                        <div class="form-group">
                            <label>Assignment ID</label>
                            <input type="text" id="grade-create-assignmentid" placeholder="Enter assignment ID">
                        </div>
                        <div class="form-group">
                            <label>Course ID *</label>
                            <input type="text" id="grade-create-courseid" placeholder="Enter course ID">
                        </div>
                        <div class="form-group">
                            <label>Score *</label>
                            <input type="number" id="grade-create-score" placeholder="Enter score" step="0.1">
                        </div>
                        <div class="form-group">
                            <label>Max Score *</label>
                            <input type="number" id="grade-create-maxscore" placeholder="Enter max score" step="0.1">
                        </div>
                        <div class="form-group">
                            <label>Letter Grade</label>
                            <input type="text" id="grade-create-lettergrade" placeholder="Enter letter grade">
                        </div>
                        <div class="form-group">
                            <label>Graded By *</label>
                            <input type="text" id="grade-create-gradedby" placeholder="Enter grader ID">
                        </div>
                        <button onclick="createGrade()" class="btn-small btn-create">Create Grade</button>
                    </div>

                    <!-- List Grades -->
                    <div class="crud-operation">
                        <h5>📋 List Grades</h5>
                        <div class="form-group">
                            <label>Filter by Student ID</label>
                            <input type="text" id="grade-list-studentid" placeholder="Filter by student ID">
                        </div>
                        <div class="form-group">
                            <label>Filter by Course ID</label>
                            <input type="text" id="grade-list-courseid" placeholder="Filter by course ID">
                        </div>
                        <button onclick="listGrades()" class="btn-small btn-read">List All Grades</button>
                    </div>

                    <!-- Search Grade by ID -->
                    <div class="crud-operation">
                        <h5>🔍 Search Grade by ID</h5>
                        <div class="form-group">
                            <label>Grade ID *</label>
                            <input type="text" id="grade-search-id" placeholder="Enter grade ID">
                        </div>
                        <button onclick="searchGradeById()" class="btn-small btn-search">Search Grade</button>
                    </div>

                    <!-- Update Grade -->
                    <div class="crud-operation">
                        <h5>✏️ Update Grade</h5>
                        <div class="form-group">
                            <label>Grade ID *</label>
                            <input type="text" id="grade-update-id" placeholder="Enter grade ID">
                        </div>
                        <div class="form-group">
                            <label>Score</label>
                            <input type="number" id="grade-update-score" placeholder="New score" step="0.1">
                        </div>
                        <div class="form-group">
                            <label>Letter Grade</label>
                            <input type="text" id="grade-update-lettergrade" placeholder="New letter grade">
                        </div>
                        <div class="form-group">
                            <label>Comments</label>
                            <textarea id="grade-update-comments" placeholder="New comments"></textarea>
                        </div>
                        <button onclick="updateGrade()" class="btn-small btn-update">Update Grade</button>
                    </div>

                    <!-- Delete Grade -->
                    <div class="crud-operation">
                        <h5>🗑️ Delete Grade</h5>
                        <div class="form-group">
                            <label>Grade ID *</label>
                            <input type="text" id="grade-delete-id" placeholder="Enter grade ID to delete">
                        </div>
                        <button onclick="deleteGrade()" class="btn-small btn-delete">Delete Grade</button>
                    </div>
                </div>

                <div id="grades-crud-response" class="response"></div>
            </div>

            <!-- Attendance CRUD -->
            <div class="endpoint-group">
                <div class="endpoint-title">
                    <span class="method get">CRUD</span>
                    <span>/api/attendances</span>
                    <span class="status-badge status-ready" id="attendances-crud-status">Ready</span>
                </div>

                <div class="crud-section">
                    <!-- Create Attendance -->
                    <div class="crud-operation">
                        <h5>📝 Create Attendance</h5>
                        <div class="form-group">
                            <label>Student ID *</label>
                            <input type="text" id="attendance-create-studentid" placeholder="Enter student ID">
                        </div>
                        <div class="form-group">
                            <label>Course ID *</label>
                            <input type="text" id="attendance-create-courseid" placeholder="Enter course ID">
                        </div>
                        <div class="form-group">
                            <label>Date *</label>
                            <input type="datetime-local" id="attendance-create-date">
                        </div>
                        <div class="form-group">
                            <label>Status *</label>
                            <select id="attendance-create-status">
                                <option value="present">Present</option>
                                <option value="absent">Absent</option>
                                <option value="late">Late</option>
                                <option value="excused">Excused</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Recorded By *</label>
                            <input type="text" id="attendance-create-recordedby" placeholder="Enter recorder ID">
                        </div>
                        <div class="form-group">
                            <label>Notes</label>
                            <textarea id="attendance-create-notes" placeholder="Enter notes"></textarea>
                        </div>
                        <button onclick="createAttendance()" class="btn-small btn-create">Create Attendance</button>
                    </div>

                    <!-- List Attendances -->
                    <div class="crud-operation">
                        <h5>📋 List Attendances</h5>
                        <div class="form-group">
                            <label>Filter by Student ID</label>
                            <input type="text" id="attendance-list-studentid" placeholder="Filter by student ID">
                        </div>
                        <div class="form-group">
                            <label>Filter by Course ID</label>
                            <input type="text" id="attendance-list-courseid" placeholder="Filter by course ID">
                        </div>
                        <div class="form-group">
                            <label>Filter by Date</label>
                            <input type="date" id="attendance-list-date" placeholder="Filter by date">
                        </div>
                        <button onclick="listAttendances()" class="btn-small btn-read">List All Attendances</button>
                    </div>

                    <!-- Search Attendance by ID -->
                    <div class="crud-operation">
                        <h5>🔍 Search Attendance by ID</h5>
                        <div class="form-group">
                            <label>Attendance ID *</label>
                            <input type="text" id="attendance-search-id" placeholder="Enter attendance ID">
                        </div>
                        <button onclick="searchAttendanceById()" class="btn-small btn-search">Search Attendance</button>
                    </div>

                    <!-- Update Attendance -->
                    <div class="crud-operation">
                        <h5>✏️ Update Attendance</h5>
                        <div class="form-group">
                            <label>Attendance ID *</label>
                            <input type="text" id="attendance-update-id" placeholder="Enter attendance ID">
                        </div>
                        <div class="form-group">
                            <label>Status</label>
                            <select id="attendance-update-status">
                                <option value="">Keep current</option>
                                <option value="present">Present</option>
                                <option value="absent">Absent</option>
                                <option value="late">Late</option>
                                <option value="excused">Excused</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Notes</label>
                            <textarea id="attendance-update-notes" placeholder="New notes"></textarea>
                        </div>
                        <button onclick="updateAttendance()" class="btn-small btn-update">Update Attendance</button>
                    </div>

                    <!-- Delete Attendance -->
                    <div class="crud-operation">
                        <h5>🗑️ Delete Attendance</h5>
                        <div class="form-group">
                            <label>Attendance ID *</label>
                            <input type="text" id="attendance-delete-id" placeholder="Enter attendance ID to delete">
                        </div>
                        <button onclick="deleteAttendance()" class="btn-small btn-delete">Delete Attendance</button>
                    </div>
                </div>

                <div id="attendances-crud-response" class="response"></div>
            </div>
        </div>

        <!-- Quick Test Section -->
        <div class="test-section">
            <h3>⚡ Quick Tests</h3>
            <p style="color: #666; margin-bottom: 20px;">
                Run comprehensive tests to verify all endpoints are working
            </p>
            <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-bottom: 20px;">
                <button onclick="runAllTests()" class="btn btn-primary" style="font-size: 16px; padding: 15px 30px;">
                    🧪 Run All Portal Tests
                </button>
                <button onclick="runAllCRUDTests()" class="btn btn-success" style="font-size: 16px; padding: 15px 30px;">
                    🔧 Run All CRUD Tests
                </button>
                <button onclick="runQuickSmokeTest()" class="btn" style="font-size: 16px; padding: 15px 30px; background: #ff9800; color: white;">
                    🔥 Quick Smoke Test
                </button>
            </div>
            <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #ffc107;">
                <strong>💡 Testing Tips:</strong><br>
                • Use "Quick Smoke Test" to verify core functionality<br>
                • Use "Run All CRUD Tests" to test all 13 models<br>
                • Individual tests can be run using buttons in each section below<br>
                • All tests require authentication - make sure to login first
            </div>
            <div id="test-all-response" class="response"></div>
        </div>
    </div>

    <!-- Load environment configuration first -->
    <script src="env.js"></script>
    <script>
        // Environment-based Configuration
        const CONFIG = {
            // Auto-detect environment and set URLs accordingly
            get environment() {
                // Check if we're in development (localhost) or production
                const hostname = window.location.hostname;
                return (hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('192.168.'))
                    ? 'development'
                    : 'production';
            },

            get loginUrl() {
                // Check for environment variables first
                if (window.ENV_LOGIN_URL) {
                    return window.ENV_LOGIN_URL;
                }

                // Auto-detect based on environment
                if (this.environment === 'development') {
                    return window.ENV_LOGIN_PORT
                        ? `http://localhost:${window.ENV_LOGIN_PORT}`
                        : 'http://localhost:8080';
                } else {
                    return 'https://stemblock-login-gljgs.ondigitalocean.app';
                }
            },

            get portalUrl() {
                // Check for environment variables first
                if (window.ENV_PORTAL_URL) {
                    return window.ENV_PORTAL_URL;
                }

                // Auto-detect based on environment
                if (this.environment === 'development') {
                    return window.ENV_PORTAL_PORT
                        ? `http://localhost:${window.ENV_PORTAL_PORT}`
                        : 'http://localhost:8082';
                } else {
                    return 'https://stemblock-portal-api.ondigitalocean.app';
                }
            },

            // Debug mode
            get debug() {
                return window.ENV_DEBUG === 'true' || this.environment === 'development';
            },

            // Log configuration if debug is enabled
            logConfig() {
                if (this.debug) {
                    console.log('[Portal Test] Environment Configuration:', {
                        environment: this.environment,
                        loginUrl: this.loginUrl,
                        portalUrl: this.portalUrl,
                        debug: this.debug,
                        envVars: {
                            ENV_LOGIN_URL: window.ENV_LOGIN_URL || 'not set',
                            ENV_PORTAL_URL: window.ENV_PORTAL_URL || 'not set',
                            ENV_LOGIN_PORT: window.ENV_LOGIN_PORT || 'not set',
                            ENV_PORTAL_PORT: window.ENV_PORTAL_PORT || 'not set',
                            ENV_DEBUG: window.ENV_DEBUG || 'not set'
                        }
                    });
                }
            }
        };

        let authToken = null;
        let currentUser = null;

        // Initialize configuration and log it
        document.addEventListener('DOMContentLoaded', function() {
            CONFIG.logConfig();
            updateConfigDisplay();
        });

        // Update configuration display in UI
        function updateConfigDisplay() {
            const configInfo = document.getElementById('config-info');
            if (configInfo) {
                configInfo.innerHTML = `
                    <strong>Environment:</strong> ${CONFIG.environment}<br>
                    <strong>Login URL:</strong> ${CONFIG.loginUrl}<br>
                    <strong>Portal URL:</strong> ${CONFIG.portalUrl}<br>
                    <strong>Debug Mode:</strong> ${CONFIG.debug ? 'Enabled' : 'Disabled'}<br>
                    <br>
                    <strong>Environment Variables:</strong><br>
                    • ENV_LOGIN_URL: ${window.ENV_LOGIN_URL || 'not set'}<br>
                    • ENV_PORTAL_URL: ${window.ENV_PORTAL_URL || 'not set'}<br>
                    • ENV_LOGIN_PORT: ${window.ENV_LOGIN_PORT || 'not set'}<br>
                    • ENV_PORTAL_PORT: ${window.ENV_PORTAL_PORT || 'not set'}<br>
                    • ENV_DEBUG: ${window.ENV_DEBUG || 'not set'}
                `;
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            checkExistingAuth();
            testServerHealth();
        });

        // Authentication Functions
        function showAuthTab(tab) {
            document.querySelectorAll('.auth-tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
            
            document.getElementById(`${tab}-tab`).classList.add('active');
            event.target.classList.add('active');
        }

        function checkExistingAuth() {
            const token = localStorage.getItem('portalAuthToken');
            const user = localStorage.getItem('portalCurrentUser');
            
            if (token && user) {
                authToken = token;
                currentUser = JSON.parse(user);
                updateAuthStatus(true);
                enablePortalTesting();
            }
        }

        async function handleLogin() {
            const username = document.getElementById('login-username').value.trim();
            const password = document.getElementById('login-password').value;
            
            if (!username || !password) {
                showAuthResponse({ error: 'Please enter both username and password' }, false);
                return;
            }
            
            try {
                showLoading('login', true);
                
                const response = await fetch(`${CONFIG.loginUrl}/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok && data.token) {
                    authToken = data.token;
                    currentUser = { username, ...data.user };
                    
                    localStorage.setItem('portalAuthToken', authToken);
                    localStorage.setItem('portalCurrentUser', JSON.stringify(currentUser));
                    
                    updateAuthStatus(true);
                    showAuthResponse({ 
                        message: 'Login successful!', 
                        user: currentUser,
                        token: authToken.substring(0, 50) + '...'
                    }, true);
                    
                    setTimeout(() => {
                        enablePortalTesting();
                    }, 1000);
                } else {
                    showAuthResponse(data, false);
                }
            } catch (error) {
                showAuthResponse({ error: `Login failed: ${error.message}` }, false);
            } finally {
                showLoading('login', false);
            }
        }

        async function handleRegister() {
            const username = document.getElementById('register-username').value.trim();
            const email = document.getElementById('register-email').value.trim();
            const password = document.getElementById('register-password').value;
            
            if (!username || !email || !password) {
                showAuthResponse({ error: 'Please fill in all fields' }, false);
                return;
            }
            
            if (password.length < 8) {
                showAuthResponse({ error: 'Password must be at least 8 characters long' }, false);
                return;
            }
            
            try {
                showLoading('register', true);
                
                const response = await fetch(`${CONFIG.loginUrl}/register`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, email, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showAuthResponse({ 
                        message: 'Registration successful! You can now login.',
                        details: 'Check your email for verification if required.'
                    }, true);
                    
                    // Switch to login tab and pre-fill username
                    showAuthTab('login');
                    document.getElementById('login-username').value = username;
                    document.querySelector('[onclick="showAuthTab(\'login\')"]').click();
                } else {
                    showAuthResponse(data, false);
                }
            } catch (error) {
                showAuthResponse({ error: `Registration failed: ${error.message}` }, false);
            } finally {
                showLoading('register', false);
            }
        }

        function quickLogin(username, password) {
            document.getElementById('login-username').value = username;
            document.getElementById('login-password').value = password;
            handleLogin();
        }

        function updateAuthStatus(authenticated) {
            const statusEl = document.getElementById('auth-status');
            const indicatorEl = document.getElementById('status-indicator');
            const tokenDisplayEl = document.getElementById('token-display');
            const tokenTextEl = document.getElementById('token-text');
            
            if (authenticated) {
                statusEl.classList.add('authenticated');
                indicatorEl.innerHTML = `✅ Authenticated as <strong>${currentUser?.username || 'User'}</strong>`;
                tokenTextEl.textContent = authToken;
                tokenDisplayEl.style.display = 'block';
            } else {
                statusEl.classList.remove('authenticated');
                indicatorEl.textContent = '❌ Not Authenticated';
                tokenDisplayEl.style.display = 'none';
            }
        }

        function enablePortalTesting() {
            const portalSection = document.getElementById('portal-testing');
            portalSection.style.display = 'block';
            portalSection.scrollIntoView({ behavior: 'smooth' });

            showAuthResponse({
                message: 'Portal testing enabled! Scroll down to test APIs.',
                token_preview: authToken?.substring(0, 30) + '...'
            }, true);
        }

        // Portal API Testing Functions
        async function testHealth() {
            try {
                updateStatus('health-status', 'testing');
                const response = await fetch(`${CONFIG.portalUrl}/health`, {
                    headers: getAuthHeaders()
                });
                const data = await response.json();

                updateStatus('health-status', response.ok ? 'working' : 'error');
                showResponse('health-response', data, response.ok);
            } catch (error) {
                updateStatus('health-status', 'error');
                showResponse('health-response', { error: error.message }, false);
            }
        }





        async function testDashboard() {
            try {
                updateStatus('dashboard-status', 'testing');
                const response = await fetch(`${CONFIG.portalUrl}/dashboard`, {
                    headers: getAuthHeaders()
                });
                const data = await response.json();

                updateStatus('dashboard-status', response.ok ? 'working' : 'error');
                showResponse('dashboard-response', data, response.ok);
            } catch (error) {
                updateStatus('dashboard-status', 'error');
                showResponse('dashboard-response', { error: error.message }, false);
            }
        }

        async function runAllTests() {
            showResponse('test-all-response', { message: 'Running complete test suite...' }, true);

            const tests = [
                { name: 'Health Check', func: testHealth },
                { name: 'Dashboard', func: testDashboard }
            ];

            const results = [];
            for (const test of tests) {
                try {
                    await test.func();
                    results.push({ test: test.name, status: 'completed' });
                } catch (error) {
                    results.push({ test: test.name, status: 'failed', error: error.message });
                }
                // Small delay between tests
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            const summary = {
                timestamp: new Date().toISOString(),
                totalTests: results.length,
                passed: results.filter(r => r.status === 'completed').length,
                failed: results.filter(r => r.status === 'failed').length,
                results: results,
                authToken: authToken ? 'Present' : 'Missing'
            };

            showResponse('test-all-response', summary, summary.failed === 0);
        }

        // Utility Functions
        function getAuthHeaders() {
            return authToken ? { 'Authorization': `Bearer ${authToken}` } : {};
        }

        function updateStatus(statusId, status) {
            const statusEl = document.getElementById(statusId);
            statusEl.className = `status-badge status-${status}`;

            switch (status) {
                case 'testing':
                    statusEl.textContent = 'Testing...';
                    break;
                case 'working':
                    statusEl.textContent = 'Working';
                    break;
                case 'error':
                    statusEl.textContent = 'Error';
                    break;
                default:
                    statusEl.textContent = 'Ready';
            }
        }

        function showResponse(elementId, data, success) {
            const element = document.getElementById(elementId);
            element.className = `response ${success ? 'success' : 'error'}`;
            element.textContent = JSON.stringify(data, null, 2);
        }

        // ===== CRUD TESTING FUNCTIONS =====

        // ===== UTILITY FUNCTIONS =====
        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }

        function formatDateTimeForAPI(datetimeLocalValue) {
            if (!datetimeLocalValue) return null;

            // datetime-local format: "2025-08-07T15:10"
            // API expects: "2006-01-02T15:04:05Z07:00" (RFC3339)

            // Add seconds if missing
            let formattedDateTime = datetimeLocalValue;
            if (formattedDateTime.length === 16) { // "2025-08-07T15:10"
                formattedDateTime += ':00'; // "2025-08-07T15:10:00"
            }

            // Add timezone offset
            const date = new Date(formattedDateTime);
            return date.toISOString(); // Converts to "2025-08-07T18:10:00.000Z"
        }

        function formatDateForAPI(dateValue) {
            if (!dateValue) return null;

            // date format: "2025-08-07"
            // Convert to start of day in local timezone, then to ISO
            const date = new Date(dateValue + 'T00:00:00');
            return date.toISOString().split('T')[0]; // Returns "2025-08-07"
        }

        // ===== USER CRUD FUNCTIONS =====
        async function createUser() {
            try {
                updateStatus('users-crud-status', 'testing');

                const userData = {
                    username: document.getElementById('user-create-username').value,
                    email: document.getElementById('user-create-email').value,
                    first_name: document.getElementById('user-create-firstname').value,
                    last_name: document.getElementById('user-create-lastname').value,
                    role: document.getElementById('user-create-role').value
                };

                if (!userData.username || !userData.email || !userData.first_name || !userData.last_name || !userData.role) {
                    throw new Error('All fields are required');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/users`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(userData)
                });

                const data = await response.json();
                updateStatus('users-crud-status', response.ok ? 'working' : 'error');
                showResponse('users-crud-response', data, response.ok);

                if (response.ok) {
                    // Clear form
                    document.getElementById('user-create-username').value = '';
                    document.getElementById('user-create-email').value = '';
                    document.getElementById('user-create-firstname').value = '';
                    document.getElementById('user-create-lastname').value = '';
                    document.getElementById('user-create-role').value = '';
                }
            } catch (error) {
                updateStatus('users-crud-status', 'error');
                showResponse('users-crud-response', { error: error.message }, false);
            }
        }

        async function listUsers() {
            try {
                updateStatus('users-crud-status', 'testing');

                const search = document.getElementById('user-list-search').value;
                const role = document.getElementById('user-list-role').value;
                const params = new URLSearchParams();
                if (search) params.append('search', search);
                if (role) params.append('role', role);

                const response = await fetch(`${CONFIG.portalUrl}/api/users?${params}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('users-crud-status', response.ok ? 'working' : 'error');
                showResponse('users-crud-response', data, response.ok);
            } catch (error) {
                updateStatus('users-crud-status', 'error');
                showResponse('users-crud-response', { error: error.message }, false);
            }
        }

        async function searchUserById() {
            try {
                updateStatus('users-crud-status', 'testing');

                const userId = document.getElementById('user-search-id').value;
                if (!userId) {
                    throw new Error('User ID is required');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/users/${userId}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('users-crud-status', response.ok ? 'working' : 'error');

                if (!response.ok && response.status === 404) {
                    showResponse('users-crud-response', { error: 'User not found' }, false);
                } else {
                    showResponse('users-crud-response', data, response.ok);
                }
            } catch (error) {
                updateStatus('users-crud-status', 'error');
                showResponse('users-crud-response', { error: error.message }, false);
            }
        }

        async function updateUser() {
            try {
                updateStatus('users-crud-status', 'testing');

                const userId = document.getElementById('user-update-id').value;
                if (!userId) {
                    throw new Error('User ID is required');
                }

                const updates = {};
                const email = document.getElementById('user-update-email').value;
                const firstName = document.getElementById('user-update-firstname').value;
                const lastName = document.getElementById('user-update-lastname').value;

                if (email) updates.email = email;
                if (firstName) updates.first_name = firstName;
                if (lastName) updates.last_name = lastName;

                if (Object.keys(updates).length === 0) {
                    throw new Error('At least one field must be provided for update');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/users/${userId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(updates)
                });

                const data = await response.json();
                updateStatus('users-crud-status', response.ok ? 'working' : 'error');
                showResponse('users-crud-response', data, response.ok);

                if (response.ok) {
                    // Clear form
                    document.getElementById('user-update-id').value = '';
                    document.getElementById('user-update-email').value = '';
                    document.getElementById('user-update-firstname').value = '';
                    document.getElementById('user-update-lastname').value = '';
                }
            } catch (error) {
                updateStatus('users-crud-status', 'error');
                showResponse('users-crud-response', { error: error.message }, false);
            }
        }

        async function deleteUser() {
            try {
                updateStatus('users-crud-status', 'testing');

                const userId = document.getElementById('user-delete-id').value;
                if (!userId) {
                    throw new Error('User ID is required');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/users/${userId}`, {
                    method: 'DELETE',
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('users-crud-status', response.ok ? 'working' : 'error');
                showResponse('users-crud-response', data, response.ok);

                if (response.ok) {
                    document.getElementById('user-delete-id').value = '';
                }
            } catch (error) {
                updateStatus('users-crud-status', 'error');
                showResponse('users-crud-response', { error: error.message }, false);
            }
        }

        // ===== STUDENT CRUD FUNCTIONS =====
        async function createStudent() {
            try {
                updateStatus('students-crud-status', 'testing');

                const userId = document.getElementById('student-create-userid').value;
                if (!userId) {
                    throw new Error('User ID is required');
                }

                const studentData = { userId: userId };

                const response = await fetch(`${CONFIG.portalUrl}/api/students`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(studentData)
                });

                const data = await response.json();
                updateStatus('students-crud-status', response.ok ? 'working' : 'error');
                showResponse('students-crud-response', data, response.ok);

                if (response.ok) {
                    document.getElementById('student-create-userid').value = '';
                }
            } catch (error) {
                updateStatus('students-crud-status', 'error');
                showResponse('students-crud-response', { error: error.message }, false);
            }
        }

        async function listStudents() {
            try {
                updateStatus('students-crud-status', 'testing');

                const search = document.getElementById('student-list-search').value;
                const params = new URLSearchParams();
                if (search) params.append('search', search);

                const response = await fetch(`${CONFIG.portalUrl}/api/students?${params}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('students-crud-status', response.ok ? 'working' : 'error');
                showResponse('students-crud-response', data, response.ok);
            } catch (error) {
                updateStatus('students-crud-status', 'error');
                showResponse('students-crud-response', { error: error.message }, false);
            }
        }

        async function searchStudentById() {
            try {
                updateStatus('students-crud-status', 'testing');

                const studentId = document.getElementById('student-search-id').value;
                if (!studentId) {
                    throw new Error('Student ID is required');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/students/${studentId}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('students-crud-status', response.ok ? 'working' : 'error');

                if (!response.ok && response.status === 404) {
                    showResponse('students-crud-response', { error: 'Student not found' }, false);
                } else {
                    showResponse('students-crud-response', data, response.ok);
                }
            } catch (error) {
                updateStatus('students-crud-status', 'error');
                showResponse('students-crud-response', { error: error.message }, false);
            }
        }

        async function updateStudent() {
            try {
                updateStatus('students-crud-status', 'testing');

                const studentId = document.getElementById('student-update-id').value;
                if (!studentId) {
                    throw new Error('Student ID is required');
                }

                const updates = {};
                const userId = document.getElementById('student-update-userid').value;
                if (userId) updates.userId = userId;

                if (Object.keys(updates).length === 0) {
                    throw new Error('At least one field must be provided for update');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/students/${studentId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(updates)
                });

                const data = await response.json();
                updateStatus('students-crud-status', response.ok ? 'working' : 'error');
                showResponse('students-crud-response', data, response.ok);

                if (response.ok) {
                    document.getElementById('student-update-id').value = '';
                    document.getElementById('student-update-userid').value = '';
                }
            } catch (error) {
                updateStatus('students-crud-status', 'error');
                showResponse('students-crud-response', { error: error.message }, false);
            }
        }

        async function deleteStudent() {
            try {
                updateStatus('students-crud-status', 'testing');

                const studentId = document.getElementById('student-delete-id').value;
                if (!studentId) {
                    throw new Error('Student ID is required');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/students/${studentId}`, {
                    method: 'DELETE',
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('students-crud-status', response.ok ? 'working' : 'error');
                showResponse('students-crud-response', data, response.ok);

                if (response.ok) {
                    document.getElementById('student-delete-id').value = '';
                }
            } catch (error) {
                updateStatus('students-crud-status', 'error');
                showResponse('students-crud-response', { error: error.message }, false);
            }
        }

        // ===== COURSE CRUD FUNCTIONS =====
        async function createCourse() {
            try {
                updateStatus('courses-crud-status', 'testing');

                const courseData = {
                    name: document.getElementById('course-create-name').value,
                    description: document.getElementById('course-create-description').value,
                    instructor: document.getElementById('course-create-instructor').value,
                    schedule: document.getElementById('course-create-schedule').value
                };

                if (!courseData.name || !courseData.description || !courseData.instructor) {
                    throw new Error('Name, description, and instructor are required');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/courses`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(courseData)
                });

                const data = await response.json();
                updateStatus('courses-crud-status', response.ok ? 'working' : 'error');
                showResponse('courses-crud-response', data, response.ok);

                if (response.ok) {
                    // Clear form
                    document.getElementById('course-create-name').value = '';
                    document.getElementById('course-create-description').value = '';
                    document.getElementById('course-create-instructor').value = '';
                    document.getElementById('course-create-schedule').value = '';
                }
            } catch (error) {
                updateStatus('courses-crud-status', 'error');
                showResponse('courses-crud-response', { error: error.message }, false);
            }
        }

        async function listCourses() {
            try {
                updateStatus('courses-crud-status', 'testing');

                const search = document.getElementById('course-list-search').value;
                const instructor = document.getElementById('course-list-instructor').value;
                const params = new URLSearchParams();
                if (search) params.append('search', search);
                if (instructor) params.append('instructor', instructor);

                const response = await fetch(`${CONFIG.portalUrl}/api/courses?${params}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('courses-crud-status', response.ok ? 'working' : 'error');
                showResponse('courses-crud-response', data, response.ok);
            } catch (error) {
                updateStatus('courses-crud-status', 'error');
                showResponse('courses-crud-response', { error: error.message }, false);
            }
        }

        async function searchCourseById() {
            try {
                updateStatus('courses-crud-status', 'testing');

                const courseId = document.getElementById('course-search-id').value;
                if (!courseId) {
                    throw new Error('Course ID is required');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/courses/${courseId}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('courses-crud-status', response.ok ? 'working' : 'error');

                if (!response.ok && response.status === 404) {
                    showResponse('courses-crud-response', { error: 'Course not found' }, false);
                } else {
                    showResponse('courses-crud-response', data, response.ok);
                }
            } catch (error) {
                updateStatus('courses-crud-status', 'error');
                showResponse('courses-crud-response', { error: error.message }, false);
            }
        }

        async function updateCourse() {
            try {
                updateStatus('courses-crud-status', 'testing');

                const courseId = document.getElementById('course-update-id').value;
                if (!courseId) {
                    throw new Error('Course ID is required');
                }

                const updates = {};
                const name = document.getElementById('course-update-name').value;
                const description = document.getElementById('course-update-description').value;
                const instructor = document.getElementById('course-update-instructor').value;

                if (name) updates.name = name;
                if (description) updates.description = description;
                if (instructor) updates.instructor = instructor;

                if (Object.keys(updates).length === 0) {
                    throw new Error('At least one field must be provided for update');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/courses/${courseId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(updates)
                });

                const data = await response.json();
                updateStatus('courses-crud-status', response.ok ? 'working' : 'error');
                showResponse('courses-crud-response', data, response.ok);

                if (response.ok) {
                    // Clear form
                    document.getElementById('course-update-id').value = '';
                    document.getElementById('course-update-name').value = '';
                    document.getElementById('course-update-description').value = '';
                    document.getElementById('course-update-instructor').value = '';
                }
            } catch (error) {
                updateStatus('courses-crud-status', 'error');
                showResponse('courses-crud-response', { error: error.message }, false);
            }
        }

        async function deleteCourse() {
            try {
                updateStatus('courses-crud-status', 'testing');

                const courseId = document.getElementById('course-delete-id').value;
                if (!courseId) {
                    throw new Error('Course ID is required');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/courses/${courseId}`, {
                    method: 'DELETE',
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('courses-crud-status', response.ok ? 'working' : 'error');
                showResponse('courses-crud-response', data, response.ok);

                if (response.ok) {
                    document.getElementById('course-delete-id').value = '';
                }
            } catch (error) {
                updateStatus('courses-crud-status', 'error');
                showResponse('courses-crud-response', { error: error.message }, false);
            }
        }

        // ===== ASSIGNMENT CRUD FUNCTIONS =====
        async function createAssignment() {
            try {
                updateStatus('assignments-crud-status', 'testing');

                const assignmentData = {
                    title: document.getElementById('assignment-create-title').value,
                    description: document.getElementById('assignment-create-description').value,
                    courseId: document.getElementById('assignment-create-courseid').value,
                    dueDate: formatDateTimeForAPI(document.getElementById('assignment-create-duedate').value)
                };

                if (!assignmentData.title || !assignmentData.description || !assignmentData.courseId) {
                    throw new Error('Title, description, and course ID are required');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/assignments`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(assignmentData)
                });

                const data = await response.json();
                updateStatus('assignments-crud-status', response.ok ? 'working' : 'error');
                showResponse('assignments-crud-response', data, response.ok);

                if (response.ok) {
                    // Clear form
                    document.getElementById('assignment-create-title').value = '';
                    document.getElementById('assignment-create-description').value = '';
                    document.getElementById('assignment-create-courseid').value = '';
                    document.getElementById('assignment-create-duedate').value = '';
                }
            } catch (error) {
                updateStatus('assignments-crud-status', 'error');
                showResponse('assignments-crud-response', { error: error.message }, false);
            }
        }

        async function listAssignments() {
            try {
                updateStatus('assignments-crud-status', 'testing');

                const search = document.getElementById('assignment-list-search').value;
                const courseId = document.getElementById('assignment-list-courseid').value;
                const status = document.getElementById('assignment-list-status').value;
                const params = new URLSearchParams();
                if (search) params.append('search', search);
                if (courseId) params.append('courseId', courseId);
                if (status) params.append('status', status);

                const response = await fetch(`${CONFIG.portalUrl}/api/assignments?${params}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('assignments-crud-status', response.ok ? 'working' : 'error');
                showResponse('assignments-crud-response', data, response.ok);
            } catch (error) {
                updateStatus('assignments-crud-status', 'error');
                showResponse('assignments-crud-response', { error: error.message }, false);
            }
        }

        async function searchAssignmentById() {
            try {
                updateStatus('assignments-crud-status', 'testing');

                const assignmentId = document.getElementById('assignment-search-id').value || generateUUID();

                const response = await fetch(`${CONFIG.portalUrl}/api/assignments/${assignmentId}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('assignments-crud-status', response.ok ? 'working' : 'error');

                if (!response.ok && response.status === 404) {
                    showResponse('assignments-crud-response', { error: 'Assignment not found' }, false);
                } else {
                    showResponse('assignments-crud-response', data, response.ok);
                }
            } catch (error) {
                updateStatus('assignments-crud-status', 'error');
                showResponse('assignments-crud-response', { error: error.message }, false);
            }
        }

        async function updateAssignment() {
            try {
                updateStatus('assignments-crud-status', 'testing');

                const assignmentId = document.getElementById('assignment-update-id').value || generateUUID();

                const updates = {};
                const title = document.getElementById('assignment-update-title').value;
                const description = document.getElementById('assignment-update-description').value;
                const dueDate = document.getElementById('assignment-update-duedate').value;

                if (title) updates.title = title;
                if (description) updates.description = description;
                if (dueDate) updates.dueDate = formatDateTimeForAPI(dueDate);

                if (Object.keys(updates).length === 0) {
                    throw new Error('At least one field must be provided for update');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/assignments/${assignmentId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(updates)
                });

                const data = await response.json();
                updateStatus('assignments-crud-status', response.ok ? 'working' : 'error');
                showResponse('assignments-crud-response', data, response.ok);

                if (response.ok) {
                    // Clear form
                    document.getElementById('assignment-update-id').value = '';
                    document.getElementById('assignment-update-title').value = '';
                    document.getElementById('assignment-update-description').value = '';
                    document.getElementById('assignment-update-duedate').value = '';
                }
            } catch (error) {
                updateStatus('assignments-crud-status', 'error');
                showResponse('assignments-crud-response', { error: error.message }, false);
            }
        }

        async function deleteAssignment() {
            try {
                updateStatus('assignments-crud-status', 'testing');

                const assignmentId = document.getElementById('assignment-delete-id').value || generateUUID();

                const response = await fetch(`${CONFIG.portalUrl}/api/assignments/${assignmentId}`, {
                    method: 'DELETE',
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('assignments-crud-status', response.ok ? 'working' : 'error');
                showResponse('assignments-crud-response', data, response.ok);

                if (response.ok) {
                    document.getElementById('assignment-delete-id').value = '';
                }
            } catch (error) {
                updateStatus('assignments-crud-status', 'error');
                showResponse('assignments-crud-response', { error: error.message }, false);
            }
        }

        // ===== QUIZ CRUD FUNCTIONS =====
        async function createQuiz() {
            try {
                updateStatus('quizzes-crud-status', 'testing');

                const quizData = {
                    title: document.getElementById('quiz-create-title').value,
                    description: document.getElementById('quiz-create-description').value,
                    courseId: document.getElementById('quiz-create-courseid').value,
                    dueDate: formatDateTimeForAPI(document.getElementById('quiz-create-duedate').value)
                };

                if (!quizData.title || !quizData.description || !quizData.courseId) {
                    throw new Error('Title, description, and course ID are required');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/quizzes`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(quizData)
                });

                const data = await response.json();
                updateStatus('quizzes-crud-status', response.ok ? 'working' : 'error');
                showResponse('quizzes-crud-response', data, response.ok);

                if (response.ok) {
                    // Clear form
                    document.getElementById('quiz-create-title').value = '';
                    document.getElementById('quiz-create-description').value = '';
                    document.getElementById('quiz-create-courseid').value = '';
                    document.getElementById('quiz-create-duedate').value = '';
                }
            } catch (error) {
                updateStatus('quizzes-crud-status', 'error');
                showResponse('quizzes-crud-response', { error: error.message }, false);
            }
        }

        async function listQuizzes() {
            try {
                updateStatus('quizzes-crud-status', 'testing');

                const search = document.getElementById('quiz-list-search').value;
                const courseId = document.getElementById('quiz-list-courseid').value;
                const status = document.getElementById('quiz-list-status').value;
                const params = new URLSearchParams();
                if (search) params.append('search', search);
                if (courseId) params.append('courseId', courseId);
                if (status) params.append('status', status);

                const response = await fetch(`${CONFIG.portalUrl}/api/quizzes?${params}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('quizzes-crud-status', response.ok ? 'working' : 'error');
                showResponse('quizzes-crud-response', data, response.ok);
            } catch (error) {
                updateStatus('quizzes-crud-status', 'error');
                showResponse('quizzes-crud-response', { error: error.message }, false);
            }
        }

        async function searchQuizById() {
            try {
                updateStatus('quizzes-crud-status', 'testing');

                const quizId = document.getElementById('quiz-search-id').value || generateUUID();

                const response = await fetch(`${CONFIG.portalUrl}/api/quizzes/${quizId}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('quizzes-crud-status', response.ok ? 'working' : 'error');

                if (!response.ok && response.status === 404) {
                    showResponse('quizzes-crud-response', { error: 'Quiz not found' }, false);
                } else {
                    showResponse('quizzes-crud-response', data, response.ok);
                }
            } catch (error) {
                updateStatus('quizzes-crud-status', 'error');
                showResponse('quizzes-crud-response', { error: error.message }, false);
            }
        }

        async function updateQuiz() {
            try {
                updateStatus('quizzes-crud-status', 'testing');

                const quizId = document.getElementById('quiz-update-id').value || generateUUID();

                const updates = {};
                const title = document.getElementById('quiz-update-title').value;
                const description = document.getElementById('quiz-update-description').value;
                const dueDate = document.getElementById('quiz-update-duedate').value;

                if (title) updates.title = title;
                if (description) updates.description = description;
                if (dueDate) updates.dueDate = formatDateTimeForAPI(dueDate);

                if (Object.keys(updates).length === 0) {
                    throw new Error('At least one field must be provided for update');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/quizzes/${quizId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(updates)
                });

                const data = await response.json();
                updateStatus('quizzes-crud-status', response.ok ? 'working' : 'error');
                showResponse('quizzes-crud-response', data, response.ok);

                if (response.ok) {
                    // Clear form
                    document.getElementById('quiz-update-id').value = '';
                    document.getElementById('quiz-update-title').value = '';
                    document.getElementById('quiz-update-description').value = '';
                    document.getElementById('quiz-update-duedate').value = '';
                }
            } catch (error) {
                updateStatus('quizzes-crud-status', 'error');
                showResponse('quizzes-crud-response', { error: error.message }, false);
            }
        }

        async function deleteQuiz() {
            try {
                updateStatus('quizzes-crud-status', 'testing');

                const quizId = document.getElementById('quiz-delete-id').value || generateUUID();

                const response = await fetch(`${CONFIG.portalUrl}/api/quizzes/${quizId}`, {
                    method: 'DELETE',
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('quizzes-crud-status', response.ok ? 'working' : 'error');
                showResponse('quizzes-crud-response', data, response.ok);

                if (response.ok) {
                    document.getElementById('quiz-delete-id').value = '';
                }
            } catch (error) {
                updateStatus('quizzes-crud-status', 'error');
                showResponse('quizzes-crud-response', { error: error.message }, false);
            }
        }

        // ===== ANNOUNCEMENT CRUD FUNCTIONS =====
        async function createAnnouncement() {
            try {
                updateStatus('announcements-crud-status', 'testing');

                const announcementData = {
                    title: document.getElementById('announcement-create-title').value,
                    content: document.getElementById('announcement-create-content').value,
                    courseId: document.getElementById('announcement-create-courseid').value
                };

                if (!announcementData.title || !announcementData.content || !announcementData.courseId) {
                    throw new Error('Title, content, and course ID are required');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/announcements`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(announcementData)
                });

                const data = await response.json();
                updateStatus('announcements-crud-status', response.ok ? 'working' : 'error');
                showResponse('announcements-crud-response', data, response.ok);

                if (response.ok) {
                    // Clear form
                    document.getElementById('announcement-create-title').value = '';
                    document.getElementById('announcement-create-content').value = '';
                    document.getElementById('announcement-create-courseid').value = '';
                }
            } catch (error) {
                updateStatus('announcements-crud-status', 'error');
                showResponse('announcements-crud-response', { error: error.message }, false);
            }
        }

        async function listAnnouncements() {
            try {
                updateStatus('announcements-crud-status', 'testing');

                const search = document.getElementById('announcement-list-search').value;
                const courseId = document.getElementById('announcement-list-courseid').value;
                const params = new URLSearchParams();
                if (search) params.append('search', search);
                if (courseId) params.append('courseId', courseId);

                const response = await fetch(`${CONFIG.portalUrl}/api/announcements?${params}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('announcements-crud-status', response.ok ? 'working' : 'error');
                showResponse('announcements-crud-response', data, response.ok);
            } catch (error) {
                updateStatus('announcements-crud-status', 'error');
                showResponse('announcements-crud-response', { error: error.message }, false);
            }
        }

        async function searchAnnouncementById() {
            try {
                updateStatus('announcements-crud-status', 'testing');

                const announcementId = document.getElementById('announcement-search-id').value || generateUUID();

                const response = await fetch(`${CONFIG.portalUrl}/api/announcements/${announcementId}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('announcements-crud-status', response.ok ? 'working' : 'error');

                if (!response.ok && response.status === 404) {
                    showResponse('announcements-crud-response', { error: 'Announcement not found' }, false);
                } else {
                    showResponse('announcements-crud-response', data, response.ok);
                }
            } catch (error) {
                updateStatus('announcements-crud-status', 'error');
                showResponse('announcements-crud-response', { error: error.message }, false);
            }
        }

        async function updateAnnouncement() {
            try {
                updateStatus('announcements-crud-status', 'testing');

                const announcementId = document.getElementById('announcement-update-id').value || generateUUID();

                const updates = {};
                const title = document.getElementById('announcement-update-title').value;
                const content = document.getElementById('announcement-update-content').value;

                if (title) updates.title = title;
                if (content) updates.content = content;

                if (Object.keys(updates).length === 0) {
                    throw new Error('At least one field must be provided for update');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/announcements/${announcementId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(updates)
                });

                const data = await response.json();
                updateStatus('announcements-crud-status', response.ok ? 'working' : 'error');
                showResponse('announcements-crud-response', data, response.ok);

                if (response.ok) {
                    // Clear form
                    document.getElementById('announcement-update-id').value = '';
                    document.getElementById('announcement-update-title').value = '';
                    document.getElementById('announcement-update-content').value = '';
                }
            } catch (error) {
                updateStatus('announcements-crud-status', 'error');
                showResponse('announcements-crud-response', { error: error.message }, false);
            }
        }

        async function deleteAnnouncement() {
            try {
                updateStatus('announcements-crud-status', 'testing');

                const announcementId = document.getElementById('announcement-delete-id').value || generateUUID();

                const response = await fetch(`${CONFIG.portalUrl}/api/announcements/${announcementId}`, {
                    method: 'DELETE',
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('announcements-crud-status', response.ok ? 'working' : 'error');
                showResponse('announcements-crud-response', data, response.ok);

                if (response.ok) {
                    document.getElementById('announcement-delete-id').value = '';
                }
            } catch (error) {
                updateStatus('announcements-crud-status', 'error');
                showResponse('announcements-crud-response', { error: error.message }, false);
            }
        }

        // ===== NOTIFICATION CRUD FUNCTIONS =====
        async function createNotification() {
            try {
                updateStatus('notifications-crud-status', 'testing');

                const notificationData = {
                    message: document.getElementById('notification-create-message').value,
                    studentId: document.getElementById('notification-create-studentid').value,
                    type: document.getElementById('notification-create-type').value
                };

                if (!notificationData.message || !notificationData.studentId) {
                    throw new Error('Message and student ID are required');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/notifications`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(notificationData)
                });

                const data = await response.json();
                updateStatus('notifications-crud-status', response.ok ? 'working' : 'error');
                showResponse('notifications-crud-response', data, response.ok);

                if (response.ok) {
                    // Clear form
                    document.getElementById('notification-create-message').value = '';
                    document.getElementById('notification-create-studentid').value = '';
                    document.getElementById('notification-create-type').value = '';
                }
            } catch (error) {
                updateStatus('notifications-crud-status', 'error');
                showResponse('notifications-crud-response', { error: error.message }, false);
            }
        }

        async function listNotifications() {
            try {
                updateStatus('notifications-crud-status', 'testing');

                const search = document.getElementById('notification-list-search').value;
                const studentId = document.getElementById('notification-list-studentid').value;
                const params = new URLSearchParams();
                if (search) params.append('search', search);
                if (studentId) params.append('studentId', studentId);

                const response = await fetch(`${CONFIG.portalUrl}/api/notifications?${params}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('notifications-crud-status', response.ok ? 'working' : 'error');
                showResponse('notifications-crud-response', data, response.ok);
            } catch (error) {
                updateStatus('notifications-crud-status', 'error');
                showResponse('notifications-crud-response', { error: error.message }, false);
            }
        }

        async function searchNotificationById() {
            try {
                updateStatus('notifications-crud-status', 'testing');

                const notificationId = document.getElementById('notification-search-id').value || generateUUID();

                const response = await fetch(`${CONFIG.portalUrl}/api/notifications/${notificationId}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('notifications-crud-status', response.ok ? 'working' : 'error');

                if (!response.ok && response.status === 404) {
                    showResponse('notifications-crud-response', { error: 'Notification not found' }, false);
                } else {
                    showResponse('notifications-crud-response', data, response.ok);
                }
            } catch (error) {
                updateStatus('notifications-crud-status', 'error');
                showResponse('notifications-crud-response', { error: error.message }, false);
            }
        }

        async function updateNotification() {
            try {
                updateStatus('notifications-crud-status', 'testing');

                const notificationId = document.getElementById('notification-update-id').value || generateUUID();

                const updates = {};
                const message = document.getElementById('notification-update-message').value;
                const type = document.getElementById('notification-update-type').value;

                if (message) updates.message = message;
                if (type) updates.type = type;

                if (Object.keys(updates).length === 0) {
                    throw new Error('At least one field must be provided for update');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/notifications/${notificationId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(updates)
                });

                const data = await response.json();
                updateStatus('notifications-crud-status', response.ok ? 'working' : 'error');
                showResponse('notifications-crud-response', data, response.ok);

                if (response.ok) {
                    // Clear form
                    document.getElementById('notification-update-id').value = '';
                    document.getElementById('notification-update-message').value = '';
                    document.getElementById('notification-update-type').value = '';
                }
            } catch (error) {
                updateStatus('notifications-crud-status', 'error');
                showResponse('notifications-crud-response', { error: error.message }, false);
            }
        }

        async function deleteNotification() {
            try {
                updateStatus('notifications-crud-status', 'testing');

                const notificationId = document.getElementById('notification-delete-id').value || generateUUID();

                const response = await fetch(`${CONFIG.portalUrl}/api/notifications/${notificationId}`, {
                    method: 'DELETE',
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('notifications-crud-status', response.ok ? 'working' : 'error');
                showResponse('notifications-crud-response', data, response.ok);

                if (response.ok) {
                    document.getElementById('notification-delete-id').value = '';
                }
            } catch (error) {
                updateStatus('notifications-crud-status', 'error');
                showResponse('notifications-crud-response', { error: error.message }, false);
            }
        }

        // ===== COACH CRUD FUNCTIONS =====
        async function createCoach() {
            try {
                updateStatus('coaches-crud-status', 'testing');

                const userId = document.getElementById('coach-create-userid').value;
                if (!userId) {
                    throw new Error('User ID is required');
                }

                const coachData = { userId: userId };

                const response = await fetch(`${CONFIG.portalUrl}/api/coaches`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(coachData)
                });

                const data = await response.json();
                updateStatus('coaches-crud-status', response.ok ? 'working' : 'error');
                showResponse('coaches-crud-response', data, response.ok);

                if (response.ok) {
                    document.getElementById('coach-create-userid').value = '';
                }
            } catch (error) {
                updateStatus('coaches-crud-status', 'error');
                showResponse('coaches-crud-response', { error: error.message }, false);
            }
        }

        async function listCoaches() {
            try {
                updateStatus('coaches-crud-status', 'testing');

                const search = document.getElementById('coach-list-search').value;
                const params = new URLSearchParams();
                if (search) params.append('search', search);

                const response = await fetch(`${CONFIG.portalUrl}/api/coaches?${params}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('coaches-crud-status', response.ok ? 'working' : 'error');
                showResponse('coaches-crud-response', data, response.ok);
            } catch (error) {
                updateStatus('coaches-crud-status', 'error');
                showResponse('coaches-crud-response', { error: error.message }, false);
            }
        }

        async function searchCoachById() {
            try {
                updateStatus('coaches-crud-status', 'testing');

                const coachId = document.getElementById('coach-search-id').value;
                if (!coachId) {
                    throw new Error('Coach ID is required');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/coaches/${coachId}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('coaches-crud-status', response.ok ? 'working' : 'error');

                if (!response.ok && response.status === 404) {
                    showResponse('coaches-crud-response', { error: 'Coach not found' }, false);
                } else {
                    showResponse('coaches-crud-response', data, response.ok);
                }
            } catch (error) {
                updateStatus('coaches-crud-status', 'error');
                showResponse('coaches-crud-response', { error: error.message }, false);
            }
        }

        async function updateCoach() {
            try {
                updateStatus('coaches-crud-status', 'testing');

                const coachId = document.getElementById('coach-update-id').value;
                if (!coachId) {
                    throw new Error('Coach ID is required');
                }

                const updates = {};
                const userId = document.getElementById('coach-update-userid').value;
                if (userId) updates.userId = userId;

                if (Object.keys(updates).length === 0) {
                    throw new Error('At least one field must be provided for update');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/coaches/${coachId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(updates)
                });

                const data = await response.json();
                updateStatus('coaches-crud-status', response.ok ? 'working' : 'error');
                showResponse('coaches-crud-response', data, response.ok);

                if (response.ok) {
                    document.getElementById('coach-update-id').value = '';
                    document.getElementById('coach-update-userid').value = '';
                }
            } catch (error) {
                updateStatus('coaches-crud-status', 'error');
                showResponse('coaches-crud-response', { error: error.message }, false);
            }
        }

        async function deleteCoach() {
            try {
                updateStatus('coaches-crud-status', 'testing');

                const coachId = document.getElementById('coach-delete-id').value;
                if (!coachId) {
                    throw new Error('Coach ID is required');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/coaches/${coachId}`, {
                    method: 'DELETE',
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('coaches-crud-status', response.ok ? 'working' : 'error');
                showResponse('coaches-crud-response', data, response.ok);

                if (response.ok) {
                    document.getElementById('coach-delete-id').value = '';
                }
            } catch (error) {
                updateStatus('coaches-crud-status', 'error');
                showResponse('coaches-crud-response', { error: error.message }, false);
            }
        }

        async function testStudentsCRUD(action) {
            try {
                updateStatus('students-crud-status', 'testing');
                let response, data;

                if (action === 'list') {
                    const search = document.getElementById('student-search').value;
                    const params = new URLSearchParams();
                    if (search) params.append('search', search);

                    response = await fetch(`${CONFIG.portalUrl}/api/students?${params}`, {
                        headers: getAuthHeaders()
                    });
                } else if (action === 'create') {
                    const userId = document.getElementById('student-user-id').value || '0728de3c-c67f-4c92-805c-86e4501978de';
                    const studentData = {
                        userId: userId
                    };

                    response = await fetch(`${CONFIG.portalUrl}/api/students`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            ...getAuthHeaders()
                        },
                        body: JSON.stringify(studentData)
                    });
                }

                data = await response.json();
                updateStatus('students-crud-status', response.ok ? 'working' : 'error');
                showResponse('students-crud-response', data, response.ok);
            } catch (error) {
                updateStatus('students-crud-status', 'error');
                showResponse('students-crud-response', { error: error.message }, false);
            }
        }

        async function testCoachesCRUD(action) {
            try {
                updateStatus('coaches-crud-status', 'testing');
                let response, data;

                if (action === 'list') {
                    const search = document.getElementById('coach-search').value;
                    const params = new URLSearchParams();
                    if (search) params.append('search', search);

                    response = await fetch(`${CONFIG.portalUrl}/api/coaches?${params}`, {
                        headers: getAuthHeaders()
                    });
                } else if (action === 'create') {
                    const userId = document.getElementById('coach-user-id').value || '0728de3c-c67f-4c92-805c-86e4501978de';
                    const coachData = {
                        userId: userId
                    };

                    response = await fetch(`${CONFIG.portalUrl}/api/coaches`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            ...getAuthHeaders()
                        },
                        body: JSON.stringify(coachData)
                    });
                }

                data = await response.json();
                updateStatus('coaches-crud-status', response.ok ? 'working' : 'error');
                showResponse('coaches-crud-response', data, response.ok);
            } catch (error) {
                updateStatus('coaches-crud-status', 'error');
                showResponse('coaches-crud-response', { error: error.message }, false);
            }
        }

        // ===== SUBMISSION CRUD FUNCTIONS =====
        async function createSubmission() {
            try {
                updateStatus('submissions-crud-status', 'testing');

                const submissionData = {
                    assignmentId: document.getElementById('submission-create-assignmentid').value,
                    studentId: document.getElementById('submission-create-studentid').value,
                    status: document.getElementById('submission-create-status').value || 'draft'
                };

                if (!submissionData.assignmentId || !submissionData.studentId) {
                    throw new Error('Assignment ID and student ID are required');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/submissions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(submissionData)
                });

                const data = await response.json();
                updateStatus('submissions-crud-status', response.ok ? 'working' : 'error');
                showResponse('submissions-crud-response', data, response.ok);

                if (response.ok) {
                    // Clear form
                    document.getElementById('submission-create-assignmentid').value = '';
                    document.getElementById('submission-create-studentid').value = '';
                    document.getElementById('submission-create-status').value = 'draft';
                }
            } catch (error) {
                updateStatus('submissions-crud-status', 'error');
                showResponse('submissions-crud-response', { error: error.message }, false);
            }
        }

        async function listSubmissions() {
            try {
                updateStatus('submissions-crud-status', 'testing');

                const search = document.getElementById('submission-list-search').value;
                const studentId = document.getElementById('submission-list-studentid').value;
                const assignmentId = document.getElementById('submission-list-assignmentid').value;
                const params = new URLSearchParams();
                if (search) params.append('search', search);
                if (studentId) params.append('studentId', studentId);
                if (assignmentId) params.append('assignmentId', assignmentId);

                const response = await fetch(`${CONFIG.portalUrl}/api/submissions?${params}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('submissions-crud-status', response.ok ? 'working' : 'error');
                showResponse('submissions-crud-response', data, response.ok);
            } catch (error) {
                updateStatus('submissions-crud-status', 'error');
                showResponse('submissions-crud-response', { error: error.message }, false);
            }
        }

        async function searchSubmissionById() {
            try {
                updateStatus('submissions-crud-status', 'testing');

                const submissionId = document.getElementById('submission-search-id').value || generateUUID();

                const response = await fetch(`${CONFIG.portalUrl}/api/submissions/${submissionId}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('submissions-crud-status', response.ok ? 'working' : 'error');

                if (!response.ok && response.status === 404) {
                    showResponse('submissions-crud-response', { error: 'Submission not found' }, false);
                } else {
                    showResponse('submissions-crud-response', data, response.ok);
                }
            } catch (error) {
                updateStatus('submissions-crud-status', 'error');
                showResponse('submissions-crud-response', { error: error.message }, false);
            }
        }

        async function updateSubmission() {
            try {
                updateStatus('submissions-crud-status', 'testing');

                const submissionId = document.getElementById('submission-update-id').value || generateUUID();

                const updates = {};
                const status = document.getElementById('submission-update-status').value;

                if (status) updates.status = status;

                if (Object.keys(updates).length === 0) {
                    throw new Error('At least one field must be provided for update');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/submissions/${submissionId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(updates)
                });

                const data = await response.json();
                updateStatus('submissions-crud-status', response.ok ? 'working' : 'error');
                showResponse('submissions-crud-response', data, response.ok);

                if (response.ok) {
                    // Clear form
                    document.getElementById('submission-update-id').value = '';
                    document.getElementById('submission-update-status').value = '';
                }
            } catch (error) {
                updateStatus('submissions-crud-status', 'error');
                showResponse('submissions-crud-response', { error: error.message }, false);
            }
        }

        async function deleteSubmission() {
            try {
                updateStatus('submissions-crud-status', 'testing');

                const submissionId = document.getElementById('submission-delete-id').value || generateUUID();

                const response = await fetch(`${CONFIG.portalUrl}/api/submissions/${submissionId}`, {
                    method: 'DELETE',
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('submissions-crud-status', response.ok ? 'working' : 'error');
                showResponse('submissions-crud-response', data, response.ok);

                if (response.ok) {
                    document.getElementById('submission-delete-id').value = '';
                }
            } catch (error) {
                updateStatus('submissions-crud-status', 'error');
                showResponse('submissions-crud-response', { error: error.message }, false);
            }
        }

        // ===== MATERIAL CRUD FUNCTIONS =====
        async function createMaterial() {
            try {
                updateStatus('materials-crud-status', 'testing');

                const materialData = {
                    title: document.getElementById('material-create-title').value,
                    description: document.getElementById('material-create-description').value,
                    courseId: document.getElementById('material-create-courseid').value,
                    uploadedBy: document.getElementById('material-create-uploadedby').value
                };

                if (!materialData.title || !materialData.description || !materialData.courseId || !materialData.uploadedBy) {
                    throw new Error('Title, description, course ID, and uploaded by are required');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/materials`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(materialData)
                });

                const data = await response.json();
                updateStatus('materials-crud-status', response.ok ? 'working' : 'error');
                showResponse('materials-crud-response', data, response.ok);

                if (response.ok) {
                    // Clear form
                    document.getElementById('material-create-title').value = '';
                    document.getElementById('material-create-description').value = '';
                    document.getElementById('material-create-courseid').value = '';
                    document.getElementById('material-create-uploadedby').value = '';
                }
            } catch (error) {
                updateStatus('materials-crud-status', 'error');
                showResponse('materials-crud-response', { error: error.message }, false);
            }
        }

        async function listMaterials() {
            try {
                updateStatus('materials-crud-status', 'testing');

                const search = document.getElementById('material-list-search').value;
                const courseId = document.getElementById('material-list-courseid').value;
                const params = new URLSearchParams();
                if (search) params.append('search', search);
                if (courseId) params.append('courseId', courseId);

                const response = await fetch(`${CONFIG.portalUrl}/api/materials?${params}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('materials-crud-status', response.ok ? 'working' : 'error');
                showResponse('materials-crud-response', data, response.ok);
            } catch (error) {
                updateStatus('materials-crud-status', 'error');
                showResponse('materials-crud-response', { error: error.message }, false);
            }
        }

        async function searchMaterialById() {
            try {
                updateStatus('materials-crud-status', 'testing');

                const materialId = document.getElementById('material-search-id').value || generateUUID();

                const response = await fetch(`${CONFIG.portalUrl}/api/materials/${materialId}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('materials-crud-status', response.ok ? 'working' : 'error');

                if (!response.ok && response.status === 404) {
                    showResponse('materials-crud-response', { error: 'Material not found' }, false);
                } else {
                    showResponse('materials-crud-response', data, response.ok);
                }
            } catch (error) {
                updateStatus('materials-crud-status', 'error');
                showResponse('materials-crud-response', { error: error.message }, false);
            }
        }

        async function updateMaterial() {
            try {
                updateStatus('materials-crud-status', 'testing');

                const materialId = document.getElementById('material-update-id').value || generateUUID();

                const updates = {};
                const title = document.getElementById('material-update-title').value;
                const description = document.getElementById('material-update-description').value;

                if (title) updates.title = title;
                if (description) updates.description = description;

                if (Object.keys(updates).length === 0) {
                    throw new Error('At least one field must be provided for update');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/materials/${materialId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(updates)
                });

                const data = await response.json();
                updateStatus('materials-crud-status', response.ok ? 'working' : 'error');
                showResponse('materials-crud-response', data, response.ok);

                if (response.ok) {
                    // Clear form
                    document.getElementById('material-update-id').value = '';
                    document.getElementById('material-update-title').value = '';
                    document.getElementById('material-update-description').value = '';
                }
            } catch (error) {
                updateStatus('materials-crud-status', 'error');
                showResponse('materials-crud-response', { error: error.message }, false);
            }
        }

        async function deleteMaterial() {
            try {
                updateStatus('materials-crud-status', 'testing');

                const materialId = document.getElementById('material-delete-id').value || generateUUID();

                const response = await fetch(`${CONFIG.portalUrl}/api/materials/${materialId}`, {
                    method: 'DELETE',
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('materials-crud-status', response.ok ? 'working' : 'error');
                showResponse('materials-crud-response', data, response.ok);

                if (response.ok) {
                    document.getElementById('material-delete-id').value = '';
                }
            } catch (error) {
                updateStatus('materials-crud-status', 'error');
                showResponse('materials-crud-response', { error: error.message }, false);
            }
        }

        // ===== ENROLLMENT CRUD FUNCTIONS =====
        async function createEnrollment() {
            try {
                updateStatus('enrollments-crud-status', 'testing');

                const studentId = document.getElementById('enrollment-create-studentid').value;
                const courseId = document.getElementById('enrollment-create-courseid').value;

                if (!studentId || !courseId) {
                    throw new Error('Both Student ID and Course ID are required');
                }

                const enrollmentData = {
                    studentId: studentId,
                    courseId: courseId
                };

                const response = await fetch(`${CONFIG.portalUrl}/api/enrollments`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(enrollmentData)
                });

                const data = await response.json();
                updateStatus('enrollments-crud-status', response.ok ? 'working' : 'error');
                showResponse('enrollments-crud-response', data, response.ok);

                if (response.ok) {
                    // Clear form
                    document.getElementById('enrollment-create-studentid').value = '';
                    document.getElementById('enrollment-create-courseid').value = '';
                }
            } catch (error) {
                updateStatus('enrollments-crud-status', 'error');
                showResponse('enrollments-crud-response', { error: error.message }, false);
            }
        }

        async function listEnrollments() {
            try {
                updateStatus('enrollments-crud-status', 'testing');

                const studentId = document.getElementById('enrollment-list-studentid').value;
                const courseId = document.getElementById('enrollment-list-courseid').value;
                const params = new URLSearchParams();
                if (studentId) params.append('studentId', studentId);
                if (courseId) params.append('courseId', courseId);

                const response = await fetch(`${CONFIG.portalUrl}/api/enrollments?${params}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('enrollments-crud-status', response.ok ? 'working' : 'error');
                showResponse('enrollments-crud-response', data, response.ok);
            } catch (error) {
                updateStatus('enrollments-crud-status', 'error');
                showResponse('enrollments-crud-response', { error: error.message }, false);
            }
        }

        async function searchEnrollmentById() {
            try {
                updateStatus('enrollments-crud-status', 'testing');

                const enrollmentId = document.getElementById('enrollment-search-id').value;
                if (!enrollmentId) {
                    throw new Error('Enrollment ID is required');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/enrollments/${enrollmentId}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('enrollments-crud-status', response.ok ? 'working' : 'error');

                if (!response.ok && response.status === 404) {
                    showResponse('enrollments-crud-response', { error: 'Enrollment not found' }, false);
                } else {
                    showResponse('enrollments-crud-response', data, response.ok);
                }
            } catch (error) {
                updateStatus('enrollments-crud-status', 'error');
                showResponse('enrollments-crud-response', { error: error.message }, false);
            }
        }

        async function deleteEnrollment() {
            try {
                updateStatus('enrollments-crud-status', 'testing');

                const enrollmentId = document.getElementById('enrollment-delete-id').value;
                if (!enrollmentId) {
                    throw new Error('Enrollment ID is required');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/enrollments/${enrollmentId}`, {
                    method: 'DELETE',
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('enrollments-crud-status', response.ok ? 'working' : 'error');
                showResponse('enrollments-crud-response', data, response.ok);

                if (response.ok) {
                    document.getElementById('enrollment-delete-id').value = '';
                }
            } catch (error) {
                updateStatus('enrollments-crud-status', 'error');
                showResponse('enrollments-crud-response', { error: error.message }, false);
            }
        }

        async function testEnrollmentsCRUD(action) {
            try {
                updateStatus('enrollments-crud-status', 'testing');
                let response, data;

                if (action === 'list') {
                    const studentId = document.getElementById('enrollment-student-filter').value;
                    const courseId = document.getElementById('enrollment-course-filter').value;
                    const params = new URLSearchParams();
                    if (studentId) params.append('studentId', studentId);
                    if (courseId) params.append('courseId', courseId);

                    response = await fetch(`${CONFIG.portalUrl}/api/enrollments?${params}`, {
                        headers: getAuthHeaders()
                    });
                } else if (action === 'create') {
                    const enrollmentData = {
                        studentId: '217ca0c6-cc90-4762-be38-0ddf04ee5853',
                        courseId: '3de98672-d40d-45e3-9c36-259de11781ca'
                    };

                    response = await fetch(`${CONFIG.portalUrl}/api/enrollments`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            ...getAuthHeaders()
                        },
                        body: JSON.stringify(enrollmentData)
                    });
                }

                data = await response.json();
                updateStatus('enrollments-crud-status', response.ok ? 'working' : 'error');
                showResponse('enrollments-crud-response', data, response.ok);
            } catch (error) {
                updateStatus('enrollments-crud-status', 'error');
                showResponse('enrollments-crud-response', { error: error.message }, false);
            }
        }

        // ===== GRADE CRUD FUNCTIONS =====
        async function createGrade() {
            try {
                updateStatus('grades-crud-status', 'testing');

                const gradeData = {
                    studentId: document.getElementById('grade-create-studentid').value,
                    assignmentId: document.getElementById('grade-create-assignmentid').value,
                    courseId: document.getElementById('grade-create-courseid').value,
                    score: parseFloat(document.getElementById('grade-create-score').value),
                    maxScore: parseFloat(document.getElementById('grade-create-maxscore').value),
                    letterGrade: document.getElementById('grade-create-lettergrade').value,
                    gradedBy: document.getElementById('grade-create-gradedby').value
                };

                if (!gradeData.studentId || !gradeData.courseId || isNaN(gradeData.score) || isNaN(gradeData.maxScore) || !gradeData.gradedBy) {
                    throw new Error('Student ID, course ID, score, max score, and graded by are required');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/grades`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(gradeData)
                });

                const data = await response.json();
                updateStatus('grades-crud-status', response.ok ? 'working' : 'error');
                showResponse('grades-crud-response', data, response.ok);

                if (response.ok) {
                    // Clear form
                    document.getElementById('grade-create-studentid').value = '';
                    document.getElementById('grade-create-assignmentid').value = '';
                    document.getElementById('grade-create-courseid').value = '';
                    document.getElementById('grade-create-score').value = '';
                    document.getElementById('grade-create-maxscore').value = '';
                    document.getElementById('grade-create-lettergrade').value = '';
                    document.getElementById('grade-create-gradedby').value = '';
                }
            } catch (error) {
                updateStatus('grades-crud-status', 'error');
                showResponse('grades-crud-response', { error: error.message }, false);
            }
        }

        async function listGrades() {
            try {
                updateStatus('grades-crud-status', 'testing');

                const studentId = document.getElementById('grade-list-studentid').value;
                const courseId = document.getElementById('grade-list-courseid').value;
                const params = new URLSearchParams();
                if (studentId) params.append('studentId', studentId);
                if (courseId) params.append('courseId', courseId);

                const response = await fetch(`${CONFIG.portalUrl}/api/grades?${params}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('grades-crud-status', response.ok ? 'working' : 'error');
                showResponse('grades-crud-response', data, response.ok);
            } catch (error) {
                updateStatus('grades-crud-status', 'error');
                showResponse('grades-crud-response', { error: error.message }, false);
            }
        }

        async function searchGradeById() {
            try {
                updateStatus('grades-crud-status', 'testing');

                const gradeId = document.getElementById('grade-search-id').value || generateUUID();

                const response = await fetch(`${CONFIG.portalUrl}/api/grades/${gradeId}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('grades-crud-status', response.ok ? 'working' : 'error');

                if (!response.ok && response.status === 404) {
                    showResponse('grades-crud-response', { error: 'Grade not found' }, false);
                } else {
                    showResponse('grades-crud-response', data, response.ok);
                }
            } catch (error) {
                updateStatus('grades-crud-status', 'error');
                showResponse('grades-crud-response', { error: error.message }, false);
            }
        }

        async function updateGrade() {
            try {
                updateStatus('grades-crud-status', 'testing');

                const gradeId = document.getElementById('grade-update-id').value || generateUUID();

                const updates = {};
                const score = document.getElementById('grade-update-score').value;
                const letterGrade = document.getElementById('grade-update-lettergrade').value;
                const comments = document.getElementById('grade-update-comments').value;

                if (score) updates.score = parseFloat(score);
                if (letterGrade) updates.letterGrade = letterGrade;
                if (comments) updates.comments = comments;

                if (Object.keys(updates).length === 0) {
                    throw new Error('At least one field must be provided for update');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/grades/${gradeId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(updates)
                });

                const data = await response.json();
                updateStatus('grades-crud-status', response.ok ? 'working' : 'error');
                showResponse('grades-crud-response', data, response.ok);

                if (response.ok) {
                    // Clear form
                    document.getElementById('grade-update-id').value = '';
                    document.getElementById('grade-update-score').value = '';
                    document.getElementById('grade-update-lettergrade').value = '';
                    document.getElementById('grade-update-comments').value = '';
                }
            } catch (error) {
                updateStatus('grades-crud-status', 'error');
                showResponse('grades-crud-response', { error: error.message }, false);
            }
        }

        async function deleteGrade() {
            try {
                updateStatus('grades-crud-status', 'testing');

                const gradeId = document.getElementById('grade-delete-id').value || generateUUID();

                const response = await fetch(`${CONFIG.portalUrl}/api/grades/${gradeId}`, {
                    method: 'DELETE',
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('grades-crud-status', response.ok ? 'working' : 'error');
                showResponse('grades-crud-response', data, response.ok);

                if (response.ok) {
                    document.getElementById('grade-delete-id').value = '';
                }
            } catch (error) {
                updateStatus('grades-crud-status', 'error');
                showResponse('grades-crud-response', { error: error.message }, false);
            }
        }

        // ===== ATTENDANCE CRUD FUNCTIONS =====
        async function createAttendance() {
            try {
                updateStatus('attendances-crud-status', 'testing');

                const attendanceData = {
                    studentId: document.getElementById('attendance-create-studentid').value,
                    courseId: document.getElementById('attendance-create-courseid').value,
                    date: formatDateTimeForAPI(document.getElementById('attendance-create-date').value),
                    status: document.getElementById('attendance-create-status').value || 'present',
                    recordedBy: document.getElementById('attendance-create-recordedby').value,
                    notes: document.getElementById('attendance-create-notes').value
                };

                if (!attendanceData.studentId || !attendanceData.courseId || !attendanceData.date || !attendanceData.recordedBy) {
                    throw new Error('Student ID, course ID, date, and recorded by are required');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/attendances`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(attendanceData)
                });

                const data = await response.json();
                updateStatus('attendances-crud-status', response.ok ? 'working' : 'error');
                showResponse('attendances-crud-response', data, response.ok);

                if (response.ok) {
                    // Clear form
                    document.getElementById('attendance-create-studentid').value = '';
                    document.getElementById('attendance-create-courseid').value = '';
                    document.getElementById('attendance-create-date').value = '';
                    document.getElementById('attendance-create-status').value = 'present';
                    document.getElementById('attendance-create-recordedby').value = '';
                    document.getElementById('attendance-create-notes').value = '';
                }
            } catch (error) {
                updateStatus('attendances-crud-status', 'error');
                showResponse('attendances-crud-response', { error: error.message }, false);
            }
        }

        async function listAttendances() {
            try {
                updateStatus('attendances-crud-status', 'testing');

                const studentId = document.getElementById('attendance-list-studentid').value;
                const courseId = document.getElementById('attendance-list-courseid').value;
                const date = document.getElementById('attendance-list-date').value;
                const params = new URLSearchParams();
                if (studentId) params.append('studentId', studentId);
                if (courseId) params.append('courseId', courseId);
                if (date) params.append('date', date);

                const response = await fetch(`${CONFIG.portalUrl}/api/attendances?${params}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('attendances-crud-status', response.ok ? 'working' : 'error');
                showResponse('attendances-crud-response', data, response.ok);
            } catch (error) {
                updateStatus('attendances-crud-status', 'error');
                showResponse('attendances-crud-response', { error: error.message }, false);
            }
        }

        async function searchAttendanceById() {
            try {
                updateStatus('attendances-crud-status', 'testing');

                const attendanceId = document.getElementById('attendance-search-id').value || generateUUID();

                const response = await fetch(`${CONFIG.portalUrl}/api/attendances/${attendanceId}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('attendances-crud-status', response.ok ? 'working' : 'error');

                if (!response.ok && response.status === 404) {
                    showResponse('attendances-crud-response', { error: 'Attendance not found' }, false);
                } else {
                    showResponse('attendances-crud-response', data, response.ok);
                }
            } catch (error) {
                updateStatus('attendances-crud-status', 'error');
                showResponse('attendances-crud-response', { error: error.message }, false);
            }
        }

        async function updateAttendance() {
            try {
                updateStatus('attendances-crud-status', 'testing');

                const attendanceId = document.getElementById('attendance-update-id').value || generateUUID();

                const updates = {};
                const status = document.getElementById('attendance-update-status').value;
                const notes = document.getElementById('attendance-update-notes').value;

                if (status) updates.status = status;
                if (notes) updates.notes = notes;

                if (Object.keys(updates).length === 0) {
                    throw new Error('At least one field must be provided for update');
                }

                const response = await fetch(`${CONFIG.portalUrl}/api/attendances/${attendanceId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(updates)
                });

                const data = await response.json();
                updateStatus('attendances-crud-status', response.ok ? 'working' : 'error');
                showResponse('attendances-crud-response', data, response.ok);

                if (response.ok) {
                    // Clear form
                    document.getElementById('attendance-update-id').value = '';
                    document.getElementById('attendance-update-status').value = '';
                    document.getElementById('attendance-update-notes').value = '';
                }
            } catch (error) {
                updateStatus('attendances-crud-status', 'error');
                showResponse('attendances-crud-response', { error: error.message }, false);
            }
        }

        async function deleteAttendance() {
            try {
                updateStatus('attendances-crud-status', 'testing');

                const attendanceId = document.getElementById('attendance-delete-id').value || generateUUID();

                const response = await fetch(`${CONFIG.portalUrl}/api/attendances/${attendanceId}`, {
                    method: 'DELETE',
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                updateStatus('attendances-crud-status', response.ok ? 'working' : 'error');
                showResponse('attendances-crud-response', data, response.ok);

                if (response.ok) {
                    document.getElementById('attendance-delete-id').value = '';
                }
            } catch (error) {
                updateStatus('attendances-crud-status', 'error');
                showResponse('attendances-crud-response', { error: error.message }, false);
            }
        }

        async function runAllCRUDTests() {
            showResponse('test-all-response', { message: 'Running all CRUD tests...' }, true);

            const crudTests = [
                { name: 'List Users', func: listUsers },
                { name: 'List Students', func: listStudents },
                { name: 'List Coaches', func: listCoaches },
                { name: 'List Courses', func: listCourses },
                { name: 'List Assignments', func: listAssignments },
                { name: 'List Quizzes', func: listQuizzes },
                { name: 'List Announcements', func: listAnnouncements },
                { name: 'List Notifications', func: listNotifications },
                { name: 'List Submissions', func: listSubmissions },
                { name: 'List Materials', func: listMaterials },
                { name: 'List Enrollments', func: listEnrollments },
                { name: 'List Grades', func: listGrades },
                { name: 'List Attendances', func: listAttendances }
            ];

            const results = [];
            for (const test of crudTests) {
                try {
                    await test.func();
                    results.push({ test: test.name, status: 'completed' });
                } catch (error) {
                    results.push({ test: test.name, status: 'failed', error: error.message });
                }
                // Small delay between tests
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            const summary = {
                timestamp: new Date().toISOString(),
                totalTests: results.length,
                passed: results.filter(r => r.status === 'completed').length,
                failed: results.filter(r => r.status === 'failed').length,
                results: results,
                authToken: authToken ? 'Present' : 'Missing'
            };

            showResponse('test-all-response', summary, summary.failed === 0);
        }

        async function runQuickSmokeTest() {
            showResponse('test-all-response', { message: 'Running quick smoke test...' }, true);

            const smokeTests = [
                { name: 'Dashboard', func: testDashboard },
                { name: 'List Users', func: listUsers },
                { name: 'List Courses', func: listCourses },
                { name: 'List Students', func: listStudents },
                { name: 'List Assignments', func: listAssignments },
                { name: 'List Grades', func: listGrades }
            ];

            const results = [];
            for (const test of smokeTests) {
                try {
                    await test.func();
                    results.push({ test: test.name, status: 'passed' });
                } catch (error) {
                    results.push({ test: test.name, status: 'failed', error: error.message });
                }
                // Small delay between tests
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            const summary = {
                timestamp: new Date().toISOString(),
                testType: 'Quick Smoke Test',
                totalTests: results.length,
                passed: results.filter(r => r.status === 'passed').length,
                failed: results.filter(r => r.status === 'failed').length,
                results: results,
                authToken: authToken ? 'Present' : 'Missing'
            };

            showResponse('test-all-response', summary, summary.failed === 0);
        }

        function logout() {
            authToken = null;
            currentUser = null;
            localStorage.removeItem('portalAuthToken');
            localStorage.removeItem('portalCurrentUser');
            updateAuthStatus(false);
            showAuthResponse({ message: 'Logged out successfully' }, true);
        }

        function copyToken() {
            navigator.clipboard.writeText(authToken).then(() => {
                alert('JWT Token copied to clipboard!');
            }).catch(() => {
                alert('Failed to copy token. Please copy manually from the text box.');
            });
        }

        async function testServerHealth() {
            try {
                const loginHealth = await fetch(`${CONFIG.loginUrl}/health`);
                const portalHealth = await fetch(`${CONFIG.portalUrl}/health`);
                
                console.log('Server Health:', {
                    login: loginHealth.ok ? 'OK' : 'ERROR',
                    portal: portalHealth.ok ? 'OK' : 'ERROR'
                });
            } catch (error) {
                console.warn('Server health check failed:', error.message);
            }
        }

        // Utility Functions
        function showAuthResponse(data, success) {
            const statusEl = document.getElementById('auth-status');
            const existingResponse = statusEl.querySelector('.auth-response');
            if (existingResponse) existingResponse.remove();
            
            const responseEl = document.createElement('div');
            responseEl.className = `response auth-response ${success ? 'success' : 'error'}`;
            responseEl.textContent = JSON.stringify(data, null, 2);
            statusEl.appendChild(responseEl);
            
            // Auto-remove after 10 seconds
            setTimeout(() => {
                if (responseEl.parentNode) responseEl.remove();
            }, 10000);
        }

        function showLoading(context, show) {
            const button = document.querySelector(`#${context}-tab button[onclick*="${context === 'login' ? 'handleLogin' : 'handleRegister'}"]`);
            if (button) {
                if (show) {
                    button.disabled = true;
                    button.innerHTML = '<span class="loading"></span> Processing...';
                } else {
                    button.disabled = false;
                    button.innerHTML = context === 'login' ? '🔑 Login' : '👤 Register Account';
                }
            }
        }
    </script>
</body>
</html>
