# Portal Test Frontend - Environment Configuration

The Portal Test Frontend now supports flexible environment-based configuration using JavaScript environment variables.

## 🚀 Quick Start

### Default Behavior (No Configuration Needed)
The test frontend automatically detects your environment:

- **Development** (localhost): Uses `localhost:8080` for login, `localhost:8082` for portal
- **Production** (any other domain): Uses production URLs

### Custom Configuration
To customize the configuration:

1. **Copy the example file:**
   ```bash
   cp env.example.js env.js
   ```

2. **Edit env.js** and uncomment/modify the configuration you need

3. **Reload test.html** to apply changes

## 📋 Configuration Options

### Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `ENV_LOGIN_URL` | Full URL for login backend | `http://localhost:8080` |
| `ENV_PORTAL_URL` | Full URL for portal backend | `http://localhost:8082` |
| `ENV_LOGIN_PORT` | Port for login backend (localhost) | `8080` |
| `ENV_PORTAL_PORT` | Port for portal backend (localhost) | `8082` |
| `ENV_DEBUG` | Enable debug logging | `true` or `false` |

### Priority Order
1. **Full URLs** (`ENV_LOGIN_URL`, `ENV_PORTAL_URL`) - highest priority
2. **Ports** (`ENV_LOGIN_PORT`, `ENV_PORTAL_PORT`) - uses localhost
3. **Auto-detection** - based on current hostname

## 🛠️ Common Scenarios

### Scenario 1: Different Ports
Your backends are running on custom ports:

```javascript
window.ENV_LOGIN_PORT = '9080';
window.ENV_PORTAL_PORT = '9082';
```

### Scenario 2: Remote Development
Connecting to backends on another machine:

```javascript
window.ENV_LOGIN_URL = 'http://*************:8080';
window.ENV_PORTAL_URL = 'http://*************:8082';
```

### Scenario 3: Mixed Environment
Using production login with local portal:

```javascript
window.ENV_LOGIN_URL = 'https://stemblock-login-gljgs.ondigitalocean.app';
window.ENV_PORTAL_URL = 'http://localhost:8082';
```

### Scenario 4: Production Testing
Testing against production APIs:

```javascript
window.ENV_LOGIN_URL = 'https://stemblock-login-gljgs.ondigitalocean.app';
window.ENV_PORTAL_URL = 'https://stemblock-portal-api.ondigitalocean.app';
window.ENV_DEBUG = 'false';
```

## 🔍 Debugging

### View Current Configuration
The test frontend displays the current configuration at the top of the page, showing:
- Detected environment
- Active URLs
- Environment variable values
- Debug mode status

### Console Logging
When debug mode is enabled, detailed configuration information is logged to the browser console.

### Enable Debug Mode
```javascript
window.ENV_DEBUG = 'true';
```

## 📁 File Structure

```
Website/portal/
├── test.html              # Main test interface
├── env.js                 # Your custom configuration (create this)
├── env.example.js         # Example configurations
└── ENV_CONFIG_README.md   # This documentation
```

## 🔧 How It Works

1. **env.js** is loaded first and sets environment variables
2. **test.html** reads these variables in the CONFIG object
3. **Auto-detection** fills in missing values based on hostname
4. **Configuration display** shows the final resolved configuration

The system uses JavaScript getters to dynamically resolve URLs based on the environment variables, allowing for flexible configuration without code changes.

## 🚨 Troubleshooting

### Configuration Not Applied
- Ensure `env.js` exists and is being loaded
- Check browser console for JavaScript errors
- Verify environment variables are set correctly

### CORS Errors
- Ensure the backend URLs are correct
- Check that CORS is enabled on the backend
- Verify the backend is running and accessible

### Authentication Issues
- Ensure you're using the correct login URL
- Check that demo users exist in the login backend
- Verify JWT tokens are being passed correctly

## 📝 Example env.js

```javascript
// Custom development setup
window.ENV_LOGIN_PORT = '8080';
window.ENV_PORTAL_PORT = '8082';
window.ENV_DEBUG = 'true';
```

This configuration system provides maximum flexibility while maintaining ease of use for common scenarios.
