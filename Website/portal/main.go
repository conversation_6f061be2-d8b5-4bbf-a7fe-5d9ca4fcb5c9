/*
 * Student Portal API
 *
 * API specification for a student portal system supporting authentication, course management, announcements, and student profiles.
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package main

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"time"

	openapi "portal/go-server/go"
	"portal/internal/db"
	"portal/internal/handlers"
	"portal/internal/model"
	"portal/internal/persistence"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"gorm.io/gorm"
)

func main() {
	// Load environment variables
	if os.Getenv("DEVELOPMENT") == "true" {
		if err := godotenv.Load(); err != nil {
			log.Printf("Warning: Could not load .env file: %v", err)
		}
	}

	// Initialize database
	dsn := getEnvOrDefault("DATABASE_URL", "host=localhost user=postgres password=******** dbname=new port=5432 sslmode=disable")
	database := persistence.InitDB(dsn)
	db.SetDB(database)

	log.Printf("Database connected")

	// Add sample data for testing
	addSampleData(database)

	// Initialize API routes
	routes := openapi.ApiHandleFunctions{
		DefaultAPI: openapi.DefaultAPI{},
	}

	port := getEnvOrDefault("PORT", "8081")
	log.Printf("Server starting on port %s", port)

	// Create router
	router := openapi.NewRouter(routes)

	// Add CORS middleware
	router.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// Add test endpoints without authentication
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "Portal API is running",
			"endpoints": []string{
				"GET /courses",
				"GET /dashboard",
				"POST /logout",
				"GET /students/:studentId",
				"PUT /students/:studentId",
				"GET /user",
			},
		})
	})

	router.GET("/test/courses", func(c *gin.Context) {
		c.JSON(200, []gin.H{
			{"id": "1", "name": "Introduction to Programming", "description": "Learn the basics of programming"},
			{"id": "2", "name": "Web Development", "description": "Build modern web applications"},
			{"id": "3", "name": "Data Science", "description": "Analyze data and build models"},
		})
	})

	// Add more test endpoints without authentication
	router.GET("/test/dashboard", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"recentAnnouncements": []gin.H{
				{"id": "1", "title": "Welcome to the Portal", "content": "Welcome to our student portal!", "date": "2024-01-15"},
				{"id": "2", "title": "Assignment Due", "content": "Don't forget your assignment is due tomorrow", "date": "2024-01-16"},
			},
			"upcomingAssignments": []gin.H{
				{"id": "1", "title": "Math Homework", "description": "Complete exercises 1-10", "dueDate": "2024-01-20"},
				{"id": "2", "title": "Science Project", "description": "Build a volcano model", "dueDate": "2024-01-25"},
			},
		})
	})

	router.GET("/test/assignments/:courseId", func(c *gin.Context) {
		courseId := c.Param("courseId")
		c.JSON(200, []gin.H{
			{"id": "1", "title": "Assignment 1", "description": "First assignment for course " + courseId, "dueDate": "2024-01-20", "status": "pending"},
			{"id": "2", "title": "Assignment 2", "description": "Second assignment for course " + courseId, "dueDate": "2024-01-25", "status": "submitted"},
		})
	})

	router.GET("/test/quizzes/:courseId", func(c *gin.Context) {
		courseId := c.Param("courseId")
		c.JSON(200, []gin.H{
			{"id": "1", "title": "Quiz 1", "description": "First quiz for course " + courseId, "questions": []gin.H{
				{"id": "1", "question": "What is 2+2?", "type": "multiple_choice", "options": []string{"3", "4", "5", "6"}},
			}},
		})
	})

	router.GET("/test/materials/:courseId", func(c *gin.Context) {
		courseId := c.Param("courseId")
		c.JSON(200, []gin.H{
			{"id": "1", "title": "Lecture Notes for " + courseId, "type": "pdf", "url": "https://example.com/notes.pdf"},
			{"id": "2", "title": "Video Tutorial for " + courseId, "type": "video", "url": "https://example.com/tutorial.mp4"},
		})
	})

	// Add file upload endpoints with actual implementation
	router.POST("/files/upload", func(c *gin.Context) {
		handleFileUpload(c, database)
	})
	router.GET("/files/:fileId/download", func(c *gin.Context) {
		handleFileDownload(c, database)
	})
	router.GET("/files/:fileId/info", func(c *gin.Context) {
		handleFileInfo(c, database)
	})
	router.DELETE("/files/:fileId", func(c *gin.Context) {
		handleFileDelete(c, database)
	})

	// Add course creation endpoint
	router.POST("/courses", func(c *gin.Context) {
		handleCreateCourse(c, database)
	})

	// Add course update endpoint
	router.PUT("/courses/:courseId", func(c *gin.Context) {
		handleUpdateCourse(c, database)
	})

	// Add course deletion endpoint
	router.DELETE("/courses/:courseId", func(c *gin.Context) {
		handleDeleteCourse(c, database)
	})

	// ===== CRUD ENDPOINTS =====

	// User CRUD
	router.POST("/api/users", handlers.CreateUser)
	router.GET("/api/users", handlers.GetUsers)
	router.GET("/api/users/:id", handlers.GetUser)
	router.PUT("/api/users/:id", handlers.UpdateUser)
	router.DELETE("/api/users/:id", handlers.DeleteUser)

	// Student CRUD
	router.POST("/api/students", handlers.CreateStudent)
	router.GET("/api/students", handlers.GetStudents)
	router.GET("/api/students/:id", handlers.GetStudent)
	router.PUT("/api/students/:id", handlers.UpdateStudent)
	router.DELETE("/api/students/:id", handlers.DeleteStudent)

	// Coach CRUD
	router.POST("/api/coaches", handlers.CreateCoach)
	router.GET("/api/coaches", handlers.GetCoaches)
	router.GET("/api/coaches/:id", handlers.GetCoach)
	router.PUT("/api/coaches/:id", handlers.UpdateCoach)
	router.DELETE("/api/coaches/:id", handlers.DeleteCoach)

	// Course CRUD (enhanced)
	router.GET("/api/courses", handlers.GetCourses)
	router.GET("/api/courses/:id", handlers.GetCourse)
	router.POST("/api/courses", handlers.CreateCourse)
	router.PUT("/api/courses/:id", handlers.UpdateCourse)
	router.DELETE("/api/courses/:id", handlers.DeleteCourse)

	// Assignment CRUD
	router.POST("/api/assignments", handlers.CreateAssignment)
	router.GET("/api/assignments", handlers.GetAssignments)
	router.GET("/api/assignments/:id", handlers.GetAssignment)
	router.PUT("/api/assignments/:id", handlers.UpdateAssignment)
	router.DELETE("/api/assignments/:id", handlers.DeleteAssignment)

	// Quiz CRUD
	router.POST("/api/quizzes", handlers.CreateQuiz)
	router.GET("/api/quizzes", handlers.GetQuizzes)
	router.GET("/api/quizzes/:id", handlers.GetQuiz)
	router.PUT("/api/quizzes/:id", handlers.UpdateQuiz)
	router.DELETE("/api/quizzes/:id", handlers.DeleteQuiz)

	// Announcement CRUD
	router.POST("/api/announcements", handlers.CreateAnnouncement)
	router.GET("/api/announcements", handlers.GetAnnouncements)
	router.GET("/api/announcements/:id", handlers.GetAnnouncement)
	router.PUT("/api/announcements/:id", handlers.UpdateAnnouncement)
	router.DELETE("/api/announcements/:id", handlers.DeleteAnnouncement)

	// Notification CRUD
	router.POST("/api/notifications", handlers.CreateNotification)
	router.GET("/api/notifications", handlers.GetNotifications)
	router.GET("/api/notifications/:id", handlers.GetNotification)
	router.PUT("/api/notifications/:id", handlers.UpdateNotification)
	router.DELETE("/api/notifications/:id", handlers.DeleteNotification)

	// Submission CRUD
	router.POST("/api/submissions", handlers.CreateSubmission)
	router.GET("/api/submissions", handlers.GetSubmissions)
	router.GET("/api/submissions/:id", handlers.GetSubmission)
	router.PUT("/api/submissions/:id", handlers.UpdateSubmission)
	router.DELETE("/api/submissions/:id", handlers.DeleteSubmission)

	// Material CRUD
	router.POST("/api/materials", handlers.CreateMaterial)
	router.GET("/api/materials", handlers.GetMaterials)
	router.GET("/api/materials/:id", handlers.GetMaterial)
	router.PUT("/api/materials/:id", handlers.UpdateMaterial)
	router.DELETE("/api/materials/:id", handlers.DeleteMaterial)

	// Enrollment CRUD
	router.POST("/api/enrollments", handlers.CreateEnrollment)
	router.GET("/api/enrollments", handlers.GetEnrollments)
	router.GET("/api/enrollments/:id", handlers.GetEnrollment)
	router.DELETE("/api/enrollments/:id", handlers.DeleteEnrollment)

	// Grade CRUD
	router.POST("/api/grades", handlers.CreateGrade)
	router.GET("/api/grades", handlers.GetGrades)
	router.GET("/api/grades/:id", handlers.GetGrade)
	router.PUT("/api/grades/:id", handlers.UpdateGrade)
	router.DELETE("/api/grades/:id", handlers.DeleteGrade)

	// Attendance CRUD
	router.POST("/api/attendances", handlers.CreateAttendance)
	router.GET("/api/attendances", handlers.GetAttendances)
	router.GET("/api/attendances/:id", handlers.GetAttendance)
	router.PUT("/api/attendances/:id", handlers.UpdateAttendance)
	router.DELETE("/api/attendances/:id", handlers.DeleteAttendance)

	// Serve static files (HTML test pages and configuration)
	router.StaticFile("/test.html", "./test.html")
	router.StaticFile("/env.js", "./env.js")

	log.Fatal(router.Run(":" + port))
}

func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func addSampleData(database *gorm.DB) {
	// Add sample courses using GORM models (let database generate UUIDs)
	courses := []model.Course{
		{Name: "Introduction to Programming", Description: "Learn the basics of programming"},
		{Name: "Web Development", Description: "Build modern web applications"},
		{Name: "Data Science", Description: "Analyze data and build models"},
	}

	for _, course := range courses {
		// Check if course with this name already exists
		var existingCourse model.Course
		if err := database.Where("name = ?", course.Name).First(&existingCourse).Error; err == gorm.ErrRecordNotFound {
			// Course doesn't exist, create it
			if err := database.Create(&course).Error; err != nil {
				log.Printf("Error creating course %s: %v", course.Name, err)
			} else {
				log.Printf("Created course: %s (ID: %s)", course.Name, course.Id)
			}
		}
	}

	log.Printf("Sample data added")
}

// File upload handler functions
func handleFileUpload(c *gin.Context, db *gorm.DB) {
	// Parse multipart form
	err := c.Request.ParseMultipartForm(10 << 20) // 10 MB max
	if err != nil {
		c.JSON(400, gin.H{"error": "File too large or invalid form"})
		return
	}

	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(400, gin.H{"error": "No file provided"})
		return
	}
	defer file.Close()

	// Validate file type
	allowedTypes := map[string]bool{
		"image/jpeg":      true,
		"image/png":       true,
		"image/gif":       true,
		"application/pdf": true,
		"text/plain":      true,
	}

	contentType := header.Header.Get("Content-Type")
	if contentType == "" {
		// Try to detect from file extension
		ext := filepath.Ext(header.Filename)
		switch ext {
		case ".jpg", ".jpeg":
			contentType = "image/jpeg"
		case ".png":
			contentType = "image/png"
		case ".gif":
			contentType = "image/gif"
		case ".pdf":
			contentType = "application/pdf"
		case ".txt":
			contentType = "text/plain"
		default:
			c.JSON(400, gin.H{"error": "File type not allowed"})
			return
		}
	}

	if !allowedTypes[contentType] {
		c.JSON(400, gin.H{"error": "File type not allowed"})
		return
	}

	// Validate file size (10MB max)
	if header.Size > 10<<20 {
		c.JSON(400, gin.H{"error": "File too large (max 10MB)"})
		return
	}

	// Generate unique filename for storage
	tempID := fmt.Sprintf("%d", time.Now().UnixNano())
	ext := filepath.Ext(header.Filename)
	storedName := tempID + ext

	// Create upload directory if it doesn't exist
	uploadDir := "uploads"
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		c.JSON(500, gin.H{"error": "Failed to create upload directory"})
		return
	}

	// Save file to disk
	filePath := filepath.Join(uploadDir, storedName)
	dst, err := os.Create(filePath)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to create file"})
		return
	}
	defer dst.Close()

	if _, err := io.Copy(dst, file); err != nil {
		c.JSON(500, gin.H{"error": "Failed to save file"})
		return
	}

	// Save file metadata to database
	fileRecord := model.File{
		ID:         "", // Will be auto-generated by database
		FileName:   header.Filename,
		FilePath:   filePath,
		FileSize:   header.Size,
		UploadedAt: time.Now(),
	}

	if err := db.Create(&fileRecord).Error; err != nil {
		// Clean up file if database save fails
		os.Remove(filePath)
		c.JSON(500, gin.H{"error": "Failed to save file metadata"})
		return
	}

	c.JSON(201, gin.H{
		"id":       fileRecord.ID, // Use the generated ID
		"filename": header.Filename,
		"size":     header.Size,
		"message":  "File uploaded successfully",
	})
}

func handleFileDownload(c *gin.Context, db *gorm.DB) {
	fileID := c.Param("fileId")
	if fileID == "" {
		c.JSON(400, gin.H{"error": "File ID is required"})
		return
	}

	var fileRecord model.File
	if err := db.First(&fileRecord, "id = ?", fileID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(404, gin.H{"error": "File not found"})
		} else {
			c.JSON(500, gin.H{"error": "Database error"})
		}
		return
	}

	// Check if file exists on disk
	if _, err := os.Stat(fileRecord.FilePath); os.IsNotExist(err) {
		c.JSON(404, gin.H{"error": "File not found on disk"})
		return
	}

	// Set headers for file download
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileRecord.FileName))
	c.Header("Content-Type", "application/octet-stream")
	c.Header("Content-Length", fmt.Sprintf("%d", fileRecord.FileSize))

	// Serve the file
	c.File(fileRecord.FilePath)
}

func handleFileInfo(c *gin.Context, db *gorm.DB) {
	fileID := c.Param("fileId")
	if fileID == "" {
		c.JSON(400, gin.H{"error": "File ID is required"})
		return
	}

	var fileRecord model.File
	if err := db.First(&fileRecord, "id = ?", fileID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(404, gin.H{"error": "File not found"})
		} else {
			c.JSON(500, gin.H{"error": "Database error"})
		}
		return
	}

	c.JSON(200, fileRecord)
}

func handleFileDelete(c *gin.Context, db *gorm.DB) {
	fileID := c.Param("fileId")
	if fileID == "" {
		c.JSON(400, gin.H{"error": "File ID is required"})
		return
	}

	var fileRecord model.File
	if err := db.First(&fileRecord, "id = ?", fileID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(404, gin.H{"error": "File not found"})
		} else {
			c.JSON(500, gin.H{"error": "Database error"})
		}
		return
	}

	// Delete file from disk
	if err := os.Remove(fileRecord.FilePath); err != nil && !os.IsNotExist(err) {
		c.JSON(500, gin.H{"error": "Failed to delete file from disk"})
		return
	}

	// Delete file record from database
	if err := db.Delete(&fileRecord).Error; err != nil {
		c.JSON(500, gin.H{"error": "Failed to delete file record"})
		return
	}

	c.JSON(200, gin.H{"message": "File deleted successfully"})
}

// Course management functions
func handleCreateCourse(c *gin.Context, db *gorm.DB) {
	var courseData struct {
		Name        string `json:"name" binding:"required"`
		Description string `json:"description"`
		Instructor  string `json:"instructor"`
		Schedule    string `json:"schedule"`
	}

	if err := c.ShouldBindJSON(&courseData); err != nil {
		c.JSON(400, gin.H{"error": "Invalid input: " + err.Error()})
		return
	}

	// Generate unique course ID (let database generate UUID)
	courseID := "" // Will be auto-generated by database

	// Create course record
	course := model.Course{
		Id:          courseID,
		Name:        courseData.Name,
		Description: courseData.Description,
	}

	if err := db.Create(&course).Error; err != nil {
		c.JSON(500, gin.H{"error": "Failed to create course"})
		return
	}

	c.JSON(201, gin.H{
		"id":          course.Id, // Use the generated ID
		"name":        courseData.Name,
		"description": courseData.Description,
		"message":     "Course created successfully",
	})
}

func handleUpdateCourse(c *gin.Context, db *gorm.DB) {
	courseID := c.Param("courseId")
	if courseID == "" {
		c.JSON(400, gin.H{"error": "Course ID is required"})
		return
	}

	var updateData struct {
		Name        *string `json:"name,omitempty"`
		Description *string `json:"description,omitempty"`
	}

	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(400, gin.H{"error": "Invalid input: " + err.Error()})
		return
	}

	// Check if course exists
	var course model.Course
	if err := db.First(&course, "id = ?", courseID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(404, gin.H{"error": "Course not found"})
		} else {
			c.JSON(500, gin.H{"error": "Database error"})
		}
		return
	}

	// Update fields if provided
	updates := make(map[string]interface{})
	if updateData.Name != nil {
		updates["name"] = *updateData.Name
	}
	if updateData.Description != nil {
		updates["description"] = *updateData.Description
	}

	if len(updates) == 0 {
		c.JSON(400, gin.H{"error": "No fields to update"})
		return
	}

	if err := db.Model(&course).Updates(updates).Error; err != nil {
		c.JSON(500, gin.H{"error": "Failed to update course"})
		return
	}

	c.JSON(200, gin.H{"message": "Course updated successfully"})
}

func handleDeleteCourse(c *gin.Context, db *gorm.DB) {
	courseID := c.Param("courseId")
	if courseID == "" {
		c.JSON(400, gin.H{"error": "Course ID is required"})
		return
	}

	// Check if course exists
	var course model.Course
	if err := db.First(&course, "id = ?", courseID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(404, gin.H{"error": "Course not found"})
		} else {
			c.JSON(500, gin.H{"error": "Database error"})
		}
		return
	}

	// TODO: Check if user has permission to delete this course
	// For now, allow any authenticated user

	// Delete course (this will cascade to related records if properly configured)
	if err := db.Delete(&course).Error; err != nil {
		c.JSON(500, gin.H{"error": "Failed to delete course"})
		return
	}

	c.JSON(200, gin.H{"message": "Course deleted successfully"})
}
