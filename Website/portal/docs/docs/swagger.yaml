info:
  contact: {}
  description: API for login and registration system
  title: Login API
  version: "1.0.0"
paths:
  /dashboard:
    get:
      description: Returns personalized dashboard message. Requires valid JWT cookie.
      produces:
      - application/json
      responses:
        "200":
          description: Welcome message
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Missing or invalid token
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - ApiKeyAuth: []
      summary: Access user dashboard
      tags:
      - dashboard
  /login:
    post:
      consumes:
      - application/json
      description: Validates user credentials and returns a JWT token as a cookie.
      parameters:
      - description: Username and Password
        in: body
        name: credentials
        required: true
        schema:
          additionalProperties:
            type: string
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: Login successful
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Invalid username or password
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Failed to generate token
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Log in a user
      tags:
      - auth
  /register:
    post:
      consumes:
      - application/json
      description: Creates a new user account with a username and password.
      parameters:
      - description: Username and Password
        in: body
        name: user
        required: true
        schema:
          additionalProperties:
            type: string
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: User registered
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Failed to hash password
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Register a new user
      tags:
      - auth
securityDefinitions:
  ApiKeyAuth:
    in: cookie
    name: token
    type: apiKey
swagger: "2.0"
