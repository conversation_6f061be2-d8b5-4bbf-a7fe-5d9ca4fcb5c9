openapi: 3.0.3
info:
  title: Student Portal API
  version: "1.0.0"
  description: API specification for a student portal system supporting authentication, course management, announcements, and student profiles.
servers:
  - url: http://localhost:8080
paths:
  /auth/login:
    post:
      summary: Login a student
      description: Authenticates a student and returns a JWT token.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                password:
                  type: string
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
        '401':
          description: Invalid credentials
  /auth/register:
    post:
      summary: Register a new student
      description: Creates a new student account.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                password:
                  type: string
                email:
                  type: string
      responses:
        '201':
          description: Student registered
        '400':
          description: Invalid input
  /students/{studentId}:
    get:
      summary: Get current student profile
      description: Returns the profile of the student.
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: studentId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Student profile
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Student'
        '401':
          description: Unauthorized
    put:
      summary: Update current student profile
      description: Updates the profile of the authenticated student.
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StudentUpdate'
      parameters:
        - in: path
          name: studentId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Profile updated
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
  /dashboard:
    get:
      summary: Get student dashboard
      description: Returns the dashboard for the authenticated student, including course list, announcements, and user info.
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Dashboard data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Dashboard'
        '401':
          description: Unauthorized
  /announcements:
    get:
      summary: List announcements
      description: Returns a list of announcements for the student.
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of announcements
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Announcement'
  /notification:
    get:
      summary: List notifications
      description: Returns a list of notifications for the student.
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of notifications
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Notification'
  /courses:
    get:
      summary: List all courses
      description: Returns a list of all available courses.
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of courses
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Course'
  /courses/{courseId}:
    get:
      summary: Get course details
      description: Returns details for a specific course, including materials and assignments.
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: courseId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Course details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CourseDetail'
        '404':
          description: Course not found
  /courses/{courseId}/materials:
    get:
      summary: List course materials
      description: Returns a list of materials for a specific course.
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: courseId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: List of materials
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Material'
  /courses/{courseId}/assignments:
    get:
      summary: List assignments
      description: Returns a list of assignments for a specific course.
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: courseId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: List of assignments
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Assignment'
    post:
      summary: Create assignment
      description: Create a new assignment for a specific course.
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: courseId
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AssignmentCreate'
      responses:
        '201':
          description: Assignment created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Assignment'
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
  /courses/{courseId}/assignments/{assignmentId}:
    get:
        summary: Get assignment details
        description: Returns details for a specific assignment in a course.
        security:
          - bearerAuth: []
        parameters:
          - in: path
            name: courseId
            required: true
            schema:
              type: string
          - in: path
            name: assignmentId
            required: true
            schema:
              type: string
        responses:
          '200':
            description: Assignment details
            content:
              application/json:
                schema:
                  type: array
                  items:
                    $ref: '#/components/schemas/Assignment'
          '404':
            description: Assignment not found
    put:
      summary: Update assignment
      description: Update an existing assignment for a specific course.
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: courseId
          required: true
          schema:
            type: string
        - in: path
          name: assignmentId
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AssignmentUpdate'
      responses:
        '200':
          description: Assignment updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Assignment'
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Assignment not found
  /courses/{courseId}/assignments/{assignmentId}/submit:
    post:
      summary: Submit homework
      description: Submit homework for a specific assignment.
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: courseId
          required: true
          schema:
            type: string
        - in: path
          name: assignmentId
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
      responses:
        '200':
          description: Submission successful
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Assignment not found
  /courses/{courseId}/quizzes:
    get:
      summary: List quizzes
      description: Returns a list of quizzes for a specific course.
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: courseId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: List of quizzes
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Quiz'
    post:
      summary: Create quiz
      description: Create a new quiz for a specific course.
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: courseId
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QuizCreate'
      responses:
        '201':
          description: Quiz created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Quiz'
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
  /courses/{courseId}/quizzes/{quizId}:
    get:
      summary: Get quiz details
      description: Returns details for a specific quiz in a course.
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: courseId
          required: true
          schema:
            type: string
        - in: path
          name: quizId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Quiz details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Quiz'
        '404':
          description: Quiz not found
    put:
      summary: Update quiz
      description: Update an existing quiz for a specific course.
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: courseId
          required: true
          schema:
            type: string
        - in: path
          name: quizId
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QuizUpdate'
      responses:
        '200':
          description: Quiz updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Quiz'
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Quiz not found
  /courses/{courseId}/quizzes/{quizId}/submit:
    post:
      summary: Submit quiz
      description: Submit answers for a specific quiz.
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: courseId
          required: true
          schema:
            type: string
        - in: path
          name: quizId
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QuizSubmission'
      responses:
        '200':
          description: Submission successful
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Quiz not found
  /user:
    get:
      summary: Get current user info
      description: Returns the profile of the currently authenticated user (for user button dropdown).
      security:
        - bearerAuth: []
      responses:
        '200':
          description: User info
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserButtonInfo'
        '401':
          description: Unauthorized
  /logout:
    post:
      summary: Logout
      description: Logs out the current user and invalidates the session or JWT token.
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Logout successful
        '401':
          description: Unauthorized
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    Student:
      type: object
      properties:
        id:
          type: string
        username:
          type: string
        email:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        enrolledCourses:
          type: array
          items:
            $ref: '#/components/schemas/Course'
    StudentUpdate:
      type: object
      properties:
        email:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        password:
          type: string
          format: password
    Course:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        instructor:
          type: string
    CourseDetail:
      allOf:
        - $ref: '#/components/schemas/Course'
        - type: object
          properties:
            materials:
              type: array
              items:
                $ref: '#/components/schemas/Material'
            assignments:
              type: array
              items:
                $ref: '#/components/schemas/Assignment'
    Material:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        url:
          type: string
        type:
          type: string
          enum: [material, pdf, video, link, assignment, quiz, other]
    Assignment:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        description:
          type: string
        dueDate:
          type: string
          format: date-time
        status:
          type: string
          enum: [pending, submitted, graded]
    AssignmentCreate:
      type: object
      required:
        - title
        - description
        - dueDate
      properties:
        title:
          type: string
        description:
          type: string
        dueDate:
          type: string
          format: date-time
    AssignmentUpdate:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        dueDate:
          type: string
          format: date-time
        status:
          type: string
          enum: [pending, submitted, graded]
    Announcement:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        content:
          type: string
        date:
          type: string
          format: date-time
    AnnouncementCreate:
      type: object
      required:
        - title
        - content
      properties:
        title:
          type: string
        content:
          type: string
    AnnouncementUpdate:
      type: object
      properties:
        title:
          type: string
        content:
          type: string
    Notification:
      type: object
      properties:
        id:
          type: string
        message:
          type: string
        date:
          type: string
          format: date-time
    Dashboard:
      type: object
      properties:
        student:
          $ref: '#/components/schemas/Student'
        courses:
          type: array
          items:
            $ref: '#/components/schemas/Course'
        announcements:
          type: array
          items:
            $ref: '#/components/schemas/Announcement'
    Quiz:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        description:
          type: string
        questions:
          type: array
          items:
            $ref: '#/components/schemas/QuizQuestion'
        dueDate:
          type: string
          format: date-time
        status:
          type: string
          enum: [pending, submitted, graded]
    QuizCreate:
      type: object
      required:
        - title
        - questions
        - dueDate
      properties:
        title:
          type: string
        description:
          type: string
        questions:
          type: array
          items:
            $ref: '#/components/schemas/QuizQuestion'
        dueDate:
          type: string
          format: date-time
    QuizUpdate:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        questions:
          type: array
          items:
            $ref: '#/components/schemas/QuizQuestion'
        dueDate:
          type: string
          format: date-time
        status:
          type: string
          enum: [pending, submitted, graded]
    QuizQuestion:
      type: object
      properties:
        id:
          type: string
        question:
          type: string
        options:
          type: array
          items:
            type: string
        answer:
          type: string
    QuizSubmission:
      type: object
      properties:
        answers:
          type: array
          items:
            type: object
            properties:
              questionId:
                type: string
              answer:
                type: string
    UserButtonInfo:
      type: object
      properties:
        id:
          type: string
        username:
          type: string
        email:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        avatarUrl:
          type: string
          format: uri
