openapi: 3.0.3
info:
  description: "API specification for a student portal system supporting authentication,\
    \ course management, announcements, and student profiles."
  title: Student Portal API
  version: 1.0.0
servers:
- url: http://localhost:8080
paths:
  /students/{studentId}:
    get:
      description: Returns the profile of the student.
      parameters:
      - explode: false
        in: path
        name: studentId
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Student'
          description: Student profile
        "401":
          description: Unauthorized
      security:
      - bearerAuth: []
      summary: Get current student profile
    put:
      description: Updates the profile of the authenticated student.
      parameters:
      - explode: false
        in: path
        name: studentId
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StudentUpdate'
        required: true
      responses:
        "200":
          description: Profile updated
        "400":
          description: Invalid input
        "401":
          description: Unauthorized
      security:
      - bearerAuth: []
      summary: Update current student profile
  /dashboard:
    get:
      description: "Returns the dashboard for the authenticated student, including\
        \ course list, announcements, and user info."
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Dashboard'
          description: Dashboard data
        "401":
          description: Unauthorized
      security:
      - bearerAuth: []
      summary: Get student dashboard
  /announcements:
    get:
      deprecated: true
      description: |
        DEPRECATED: Use /me/announcements instead. Returns a list of announcements for the student.
      responses:
        "200":
          description: List of announcements
      security:
      - bearerAuth: []
      summary: List announcements (DEPRECATED)
  /me/announcements:
    get:
      description: Returns a list of announcements for the authenticated user/student.
      responses:
        "200":
          description: List of announcements
        "401":
          description: Unauthorized
      security:
      - bearerAuth: []
      summary: List my announcements
  /notification:
    get:
      deprecated: true
      description: |
        DEPRECATED: Use /me/notifications instead. Returns a list of notifications for the student.
      responses:
        "200":
          description: List of notifications
      security:
      - bearerAuth: []
      summary: List notifications (DEPRECATED)
  /me/notifications:
    get:
      description: Returns a list of notifications for the authenticated user/student.
      responses:
        "200":
          description: List of notifications
        "401":
          description: Unauthorized
      security:
      - bearerAuth: []
      summary: List my notifications
  /courses:
    get:
      description: Returns a list of all available courses.
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Course'
                type: array
          description: List of courses
      security:
      - bearerAuth: []
      summary: List all courses
  /courses/{courseId}:
    get:
      description: "Returns details for a specific course, including materials and\
        \ assignments."
      parameters:
      - explode: false
        in: path
        name: courseId
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CourseDetail'
          description: Course details
        "404":
          description: Course not found
      security:
      - bearerAuth: []
      summary: Get course details
  /courses/{courseId}/materials:
    get:
      description: Returns a list of materials for a specific course.
      parameters:
      - explode: false
        in: path
        name: courseId
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Material'
                type: array
          description: List of materials
      security:
      - bearerAuth: []
      summary: List course materials
  /courses/{courseId}/assignments:
    get:
      description: Returns a list of assignments for a specific course.
      parameters:
      - explode: false
        in: path
        name: courseId
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Assignment'
                type: array
          description: List of assignments
      security:
      - bearerAuth: []
      summary: List assignments
    post:
      description: Create a new assignment for a specific course.
      parameters:
      - explode: false
        in: path
        name: courseId
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AssignmentCreate'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Assignment'
          description: Assignment created
        "400":
          description: Invalid input
        "401":
          description: Unauthorized
      security:
      - bearerAuth: []
      summary: Create assignment
  /courses/{courseId}/assignments/{assignmentId}:
    get:
      description: Returns details for a specific assignment in a course.
      parameters:
      - explode: false
        in: path
        name: courseId
        required: true
        schema:
          type: string
        style: simple
      - explode: false
        in: path
        name: assignmentId
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Assignment'
                type: array
          description: Assignment details
        "404":
          description: Assignment not found
      security:
      - bearerAuth: []
      summary: Get assignment details
    put:
      description: Update an existing assignment for a specific course.
      parameters:
      - explode: false
        in: path
        name: courseId
        required: true
        schema:
          type: string
        style: simple
      - explode: false
        in: path
        name: assignmentId
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AssignmentUpdate'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Assignment'
          description: Assignment updated
        "400":
          description: Invalid input
        "401":
          description: Unauthorized
        "404":
          description: Assignment not found
      security:
      - bearerAuth: []
      summary: Update assignment
  /courses/{courseId}/assignments/{assignmentId}/submit:
    post:
      description: Submit homework for a specific assignment.
      parameters:
      - explode: false
        in: path
        name: courseId
        required: true
        schema:
          type: string
        style: simple
      - explode: false
        in: path
        name: assignmentId
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/_courses__courseId__assignments__assignmentId__submit_post_request'
        required: true
      responses:
        "200":
          description: Submission successful
        "400":
          description: Invalid input
        "401":
          description: Unauthorized
        "404":
          description: Assignment not found
      security:
      - bearerAuth: []
      summary: Submit homework
  /courses/{courseId}/quizzes:
    get:
      description: Returns a list of quizzes for a specific course.
      parameters:
      - explode: false
        in: path
        name: courseId
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Quiz'
                type: array
          description: List of quizzes
      security:
      - bearerAuth: []
      summary: List quizzes
    post:
      description: Create a new quiz for a specific course.
      parameters:
      - explode: false
        in: path
        name: courseId
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QuizCreate'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Quiz'
          description: Quiz created
        "400":
          description: Invalid input
        "401":
          description: Unauthorized
      security:
      - bearerAuth: []
      summary: Create quiz
  /courses/{courseId}/quizzes/{quizId}:
    get:
      description: Returns details for a specific quiz in a course.
      parameters:
      - explode: false
        in: path
        name: courseId
        required: true
        schema:
          type: string
        style: simple
      - explode: false
        in: path
        name: quizId
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Quiz'
          description: Quiz details
        "404":
          description: Quiz not found
      security:
      - bearerAuth: []
      summary: Get quiz details
    put:
      description: Update an existing quiz for a specific course.
      parameters:
      - explode: false
        in: path
        name: courseId
        required: true
        schema:
          type: string
        style: simple
      - explode: false
        in: path
        name: quizId
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QuizUpdate'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Quiz'
          description: Quiz updated
        "400":
          description: Invalid input
        "401":
          description: Unauthorized
        "404":
          description: Quiz not found
      security:
      - bearerAuth: []
      summary: Update quiz
  /courses/{courseId}/quizzes/{quizId}/submit:
    post:
      description: Submit answers for a specific quiz.
      parameters:
      - explode: false
        in: path
        name: courseId
        required: true
        schema:
          type: string
        style: simple
      - explode: false
        in: path
        name: quizId
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QuizSubmission'
        required: true
      responses:
        "200":
          description: Submission successful
        "400":
          description: Invalid input
        "401":
          description: Unauthorized
        "404":
          description: Quiz not found
      security:
      - bearerAuth: []
      summary: Submit quiz
  /user:
    get:
      description: Returns the profile of the currently authenticated user (for user
        button dropdown).
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserButtonInfo'
          description: User info
        "401":
          description: Unauthorized
      security:
      - bearerAuth: []
      summary: Get current user info
  /logout:
    post:
      description: Logs out the current user and invalidates the session or JWT token.
      responses:
        "200":
          description: Logout successful
        "401":
          description: Unauthorized
      security:
      - bearerAuth: []
      summary: Logout
components:
  schemas:
    Student:
      example:
        firstName: firstName
        lastName: lastName
        enrolledCourses:
        - instructor: instructor
          name: name
          description: description
          id: id
        - instructor: instructor
          name: name
          description: description
          id: id
        id: id
        email: email
        username: username
      properties:
        id:
          type: string
        username:
          type: string
        email:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        enrolledCourses:
          items:
            $ref: '#/components/schemas/Course'
          type: array
      type: object
    StudentUpdate:
      example:
        firstName: firstName
        lastName: lastName
        password: password
        email: email
      properties:
        email:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        password:
          format: password
          type: string
      type: object
    Course:
      example:
        instructor: instructor
        name: name
        description: description
        id: id
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        instructor:
          type: string
      type: object
    CourseDetail:
      allOf:
      - $ref: '#/components/schemas/Course'
      - properties:
          materials:
            items:
              $ref: '#/components/schemas/Material'
            type: array
          assignments:
            items:
              $ref: '#/components/schemas/Assignment'
            type: array
        type: object
      example:
        assignments:
        - dueDate: 2000-01-23T04:56:07.000+00:00
          description: description
          id: id
          title: title
          status: pending
        - dueDate: 2000-01-23T04:56:07.000+00:00
          description: description
          id: id
          title: title
          status: pending
        instructor: instructor
        materials:
        - id: id
          title: title
          type: material
          url: url
        - id: id
          title: title
          type: material
          url: url
        name: name
        description: description
        id: id
    Material:
      example:
        id: id
        title: title
        type: material
        url: url
      properties:
        id:
          type: string
        title:
          type: string
        url:
          type: string
        type:
          enum:
          - material
          - pdf
          - video
          - link
          - assignment
          - quiz
          - other
          type: string
      type: object
    Assignment:
      example:
        dueDate: 2000-01-23T04:56:07.000+00:00
        description: description
        id: id
        title: title
        status: pending
      properties:
        id:
          type: string
        title:
          type: string
        description:
          type: string
        dueDate:
          format: date-time
          type: string
        status:
          enum:
          - pending
          - submitted
          - graded
          type: string
      type: object
    AssignmentCreate:
      example:
        dueDate: 2000-01-23T04:56:07.000+00:00
        description: description
        title: title
      properties:
        title:
          type: string
        description:
          type: string
        dueDate:
          format: date-time
          type: string
      required:
      - description
      - dueDate
      - title
      type: object
    AssignmentUpdate:
      example:
        dueDate: 2000-01-23T04:56:07.000+00:00
        description: description
        title: title
        status: pending
      properties:
        title:
          type: string
        description:
          type: string
        dueDate:
          format: date-time
          type: string
        status:
          enum:
          - pending
          - submitted
          - graded
          type: string
      type: object
    Announcement:
      example:
        date: 2000-01-23T04:56:07.000+00:00
        id: id
        title: title
        content: content
      properties:
        id:
          type: string
        title:
          type: string
        content:
          type: string
        date:
          format: date-time
          type: string
      type: object
    AnnouncementCreate:
      properties:
        title:
          type: string
        content:
          type: string
      required:
      - content
      - title
      type: object
    AnnouncementUpdate:
      properties:
        title:
          type: string
        content:
          type: string
      type: object
    Notification:
      properties:
        id:
          type: string
        message:
          type: string
        date:
          format: date-time
          type: string
      type: object
    Dashboard:
      example:
        courses:
        - instructor: instructor
          name: name
          description: description
          id: id
        - instructor: instructor
          name: name
          description: description
          id: id
        student:
          firstName: firstName
          lastName: lastName
          enrolledCourses:
          - instructor: instructor
            name: name
            description: description
            id: id
          - instructor: instructor
            name: name
            description: description
            id: id
          id: id
          email: email
          username: username
        announcements:
        - date: 2000-01-23T04:56:07.000+00:00
          id: id
          title: title
          content: content
        - date: 2000-01-23T04:56:07.000+00:00
          id: id
          title: title
          content: content
      properties:
        student:
          $ref: '#/components/schemas/Student'
        courses:
          items:
            $ref: '#/components/schemas/Course'
          type: array
        announcements:
          items:
            $ref: '#/components/schemas/Announcement'
          type: array
      type: object
    Quiz:
      example:
        dueDate: 2000-01-23T04:56:07.000+00:00
        questions:
        - question: question
          answer: answer
          options:
          - options
          - options
          id: id
        - question: question
          answer: answer
          options:
          - options
          - options
          id: id
        description: description
        id: id
        title: title
        status: pending
      properties:
        id:
          type: string
        title:
          type: string
        description:
          type: string
        questions:
          items:
            $ref: '#/components/schemas/QuizQuestion'
          type: array
        dueDate:
          format: date-time
          type: string
        status:
          enum:
          - pending
          - submitted
          - graded
          type: string
      type: object
    QuizCreate:
      example:
        dueDate: 2000-01-23T04:56:07.000+00:00
        questions:
        - question: question
          answer: answer
          options:
          - options
          - options
          id: id
        - question: question
          answer: answer
          options:
          - options
          - options
          id: id
        description: description
        title: title
      properties:
        title:
          type: string
        description:
          type: string
        questions:
          items:
            $ref: '#/components/schemas/QuizQuestion'
          type: array
        dueDate:
          format: date-time
          type: string
      required:
      - dueDate
      - questions
      - title
      type: object
    QuizUpdate:
      example:
        dueDate: 2000-01-23T04:56:07.000+00:00
        questions:
        - question: question
          answer: answer
          options:
          - options
          - options
          id: id
        - question: question
          answer: answer
          options:
          - options
          - options
          id: id
        description: description
        title: title
        status: pending
      properties:
        title:
          type: string
        description:
          type: string
        questions:
          items:
            $ref: '#/components/schemas/QuizQuestion'
          type: array
        dueDate:
          format: date-time
          type: string
        status:
          enum:
          - pending
          - submitted
          - graded
          type: string
      type: object
    QuizQuestion:
      example:
        question: question
        answer: answer
        options:
        - options
        - options
        id: id
      properties:
        id:
          type: string
        question:
          type: string
        options:
          items:
            type: string
          type: array
        answer:
          type: string
      type: object
    QuizSubmission:
      example:
        answers:
        - questionId: questionId
          answer: answer
        - questionId: questionId
          answer: answer
      properties:
        answers:
          items:
            $ref: '#/components/schemas/QuizSubmission_answers_inner'
          type: array
      type: object
    UserButtonInfo:
      example:
        firstName: firstName
        lastName: lastName
        avatarUrl: https://openapi-generator.tech
        id: id
        email: email
        username: username
      properties:
        id:
          type: string
        username:
          type: string
        email:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        avatarUrl:
          format: uri
          type: string
      type: object
    _courses__courseId__assignments__assignmentId__submit_post_request:
      properties:
        file:
          format: binary
          type: string
      type: object
    QuizSubmission_answers_inner:
      example:
        questionId: questionId
        answer: answer
      properties:
        questionId:
          type: string
        answer:
          type: string
      type: object
  securitySchemes:
    bearerAuth:
      bearerFormat: JWT
      scheme: bearer
      type: http
