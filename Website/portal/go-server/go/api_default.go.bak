/*
 * Student Portal API
 *
 * API specification for a student portal system supporting authentication, course management, announcements, and student profiles.
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package openapi

import (
	"net/http"
	"strconv"

	"github.com/GIT_USER_ID/GIT_REPO_ID/internal/auth"
	"github.com/GIT_USER_ID/GIT_REPO_ID/internal/db"
	"github.com/GIT_USER_ID/GIT_REPO_ID/internal/model"

	"github.com/gin-gonic/gin"
)

type DefaultAPI struct {
}

// Get /announcements
// List announcements
func (api *DefaultAPI) AnnouncementsGet(c *gin.Context) {
	announcements, err := db.ListAnnouncements()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch announcements"})
		return
	}
	c.<PERSON>SO<PERSON>(http.StatusOK, announcements)
}

// Get /courses/:courseId/assignments/:assignmentId
// Get assignment details
func (api *DefaultAPI) AssignmentIdGet(c *gin.Context) {
	courseId := c.Param("courseId")
	if courseId == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Course ID is required"})
		return
	}
	assignmentId := c.Param("assignmentId")
	if assignmentId == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Assignment ID is required"})
		return
	}
	assignment, err := db.GetAssignmentByID(assignmentId)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Assignment not found"})
		return
	}
	// Optionally check assignment.CourseID == courseId
	c.JSON(http.StatusOK, assignment)
}

// Put /courses/:courseId/assignments/:assignmentId
// Update assignment
func (api *DefaultAPI) AssignmentIdPut(c *gin.Context) {
	assignmentId := c.Param("assignmentId")
	if assignmentId == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Assignment ID is required"})
		return
	}
	var update map[string]interface{}
	if err := c.BindJSON(&update); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}
	if err := db.UpdateAssignment(assignmentId, update); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update assignment"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Assignment updated"})
}

// Post /courses/:courseId/assignments/:assignmentId/submit
// Submit homework
func (api *DefaultAPI) AssignmentIdSubmitPost(c *gin.Context) {
	courseId := c.Param("courseId")
	if courseId == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Course ID is required"})
		return
	}
	id := c.Param("assignmentId")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Assignment ID is required"})
		return
	}
	var submission model.Submission
	if err := c.BindJSON(&submission); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}
	if err := db.SubmitAssignment(id, &submission); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to submit assignment"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Assignment submitted successfully"})
}

// Get /courses/:courseId/assignments
// List assignments for a course
func (api *DefaultAPI) AssignmentsGet(c *gin.Context) {
	courseId := c.Param("courseId")
	if courseId == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Course ID is required"})
		return
	}
	assignments, err := db.ListAssignmentsByCourse(courseId)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch assignments"})
		return
	}
	c.JSON(http.StatusOK, assignments)
}

// Post /courses/:courseId/assignments
// Create assignment for a course
func (api *DefaultAPI) AssignmentsPost(c *gin.Context) {
	courseId := c.Param("courseId")
	if courseId == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Course ID is required"})
		return
	}
	var assignment model.Assignment
	if err := c.BindJSON(&assignment); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}
	assignment.CourseID = courseId
	if err := db.CreateAssignment(&assignment); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create assignment"})
		return
	}
	c.JSON(http.StatusCreated, assignment)
}

// Get /courses/:courseId/materials
// List course materials
func (api *DefaultAPI) MaterialsGet(c *gin.Context) {
	// Your handler implementation
	c.JSON(200, gin.H{"status": "OK"})
}

// Post /courses/:courseId/quizzes/:quizId/submit
// Submit quiz
func (api *DefaultAPI) CoursesCourseIdQuizzesQuizIdSubmitPost(c *gin.Context) {
	// Your handler implementation
	c.JSON(200, gin.H{"status": "OK"})
}

// Get /dashboard
// Get student dashboard
func (api *DefaultAPI) DashboardGet(c *gin.Context) {
	studentID, err := auth.GetUserIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}
	dashboard, err := db.GetDashboard(studentID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch dashboard"})
		return
	}
	c.JSON(http.StatusOK, dashboard)
}

// Post /logout
// Logout
func (api *DefaultAPI) LogoutPost(c *gin.Context) {
	// For JWT, logout is handled client-side by deleting the token
	c.JSON(http.StatusOK, gin.H{"message": "Logged out"})
}

// Get /notification
// List notifications
func (api *DefaultAPI) NotificationGet(c *gin.Context) {
	studentID, err := auth.GetUserIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}
	notifications, err := db.ListNotifications(studentID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch notifications"})
		return
	}
	c.JSON(http.StatusOK, notifications)
}

// Get /students/:studentId
// Get current student profile
func (api *DefaultAPI) StudentsStudentIdGet(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("studentId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid student ID"})
		return
	}
	student, err := db.GetStudentByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Student not found"})
		return
	}
	c.JSON(http.StatusOK, student)
}

// Put /students/:studentId
// Update current student profile
func (api *DefaultAPI) StudentsStudentIdPut(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("studentId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid student ID"})
		return
	}
	var req struct {
		Email string `json:"email"`
	}
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}
	if err := db.UpdateStudentEmail(uint(id), req.Email); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update student"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Student updated"})
}

// Get /user
// Get current user info
func (api *DefaultAPI) UserGet(c *gin.Context) {
	// Your handler implementation
	c.JSON(200, gin.H{"status": "OK"})
}

// --- ANNOUNCEMENTS CRUD ---
// Get /announcements/:id
func (api *DefaultAPI) AnnouncementsIdGet(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid announcement ID"})
		return
	}
	ann, err := db.GetAnnouncementByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Announcement not found"})
		return
	}
	c.JSON(http.StatusOK, ann)
}

// Post /announcements
func (api *DefaultAPI) AnnouncementsPost(c *gin.Context) {
	var ann model.Announcement
	if err := c.BindJSON(&ann); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}
	if err := db.CreateAnnouncement(&ann); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create announcement"})
		return
	}
	c.JSON(http.StatusOK, ann)
}

// Put /announcements/:id
func (api *DefaultAPI) AnnouncementsIdPut(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid announcement ID"})
		return
	}
	var update map[string]interface{}
	if err := c.BindJSON(&update); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}
	if err := db.UpdateAnnouncement(uint(id), update); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update announcement"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Announcement updated"})
}

// Delete /announcements/:id
func (api *DefaultAPI) AnnouncementsIdDelete(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid announcement ID"})
		return
	}
	if err := db.DeleteAnnouncement(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete announcement"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Announcement deleted"})
}

// --- STUDENT CRUD ---
// Delete /students/:studentId
func (api *DefaultAPI) StudentsStudentIdDelete(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("studentId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid student ID"})
		return
	}
	if err := db.DeleteStudent(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete student"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Student deleted"})
}

// --- NOTIFICATION CRUD ---
// Get /notification/:id
func (api *DefaultAPI) NotificationIdGet(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid notification ID"})
		return
	}
	notif, err := db.GetNotificationByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Notification not found"})
		return
	}
	c.JSON(http.StatusOK, notif)
}

// Post /notification
func (api *DefaultAPI) NotificationPost(c *gin.Context) {
	var notif model.Notification
	if err := c.BindJSON(&notif); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}
	if err := db.CreateNotification(&notif); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create notification"})
		return
	}
	c.JSON(http.StatusOK, notif)
}

// Put /notification/:id
func (api *DefaultAPI) NotificationIdPut(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid notification ID"})
		return
	}
	var update map[string]interface{}
	if err := c.BindJSON(&update); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}
	if err := db.UpdateNotification(uint(id), update); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update notification"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Notification updated"})
}

// Delete /notification/:id
func (api *DefaultAPI) NotificationIdDelete(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid notification ID"})
		return
	}
	if err := db.DeleteNotification(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete notification"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Notification deleted"})
}

// --- COURSES CRUD ---
// List all courses
func (api *DefaultAPI) CoursesGet(c *gin.Context) {
	courses, err := db.ListCourses()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch courses"})
		return
	}
	c.JSON(http.StatusOK, courses)
}

// Get course details
func (api *DefaultAPI) CoursesCourseIdGet(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("courseId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}
	course, err := db.GetCourseByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Course not found"})
		return
	}
	c.JSON(http.StatusOK, course)
}

// --- QUIZZES CRUD (per course) ---
// List quizzes for a course
func (api *DefaultAPI) CoursesCourseIdQuizzesGet(c *gin.Context) {
	courseId := c.Param("courseId")
	if courseId == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Course ID is required"})
		return
	}
	quizzes, err := db.ListQuizzesByCourse(courseId)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch quizzes"})
		return
	}
	c.JSON(http.StatusOK, quizzes)
}

// Create quiz for a course
func (api *DefaultAPI) CoursesCourseIdQuizzesPost(c *gin.Context) {
	courseId := c.Param("courseId")
	if courseId == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Course ID is required"})
		return
	}
	var quiz model.Quiz
	if err := c.BindJSON(&quiz); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}
	quiz.CourseID = courseId
	if err := db.CreateQuiz(&quiz); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create quiz"})
		return
	}
	c.JSON(http.StatusOK, quiz)
}

// Get quiz details
func (api *DefaultAPI) CoursesCourseIdQuizzesQuizIdGet(c *gin.Context) {
	quizId := c.Param("quizId")
	if quizId == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Quiz ID is required"})
		return
	}
	quiz, err := db.GetQuizByID(quizId)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz not found"})
		return
	}
	c.JSON(http.StatusOK, quiz)
}

// Update quiz
func (api *DefaultAPI) CoursesCourseIdQuizzesQuizIdPut(c *gin.Context) {
	quizId := c.Param("quizId")
	if quizId == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Quiz ID is required"})
		return
	}
	var update map[string]interface{}
	if err := c.BindJSON(&update); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}
	if err := db.UpdateQuiz(quizId, update); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update quiz"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Quiz updated"})
}

// Delete quiz
func (api *DefaultAPI) CoursesCourseIdQuizzesQuizIdDelete(c *gin.Context) {
	quizId := c.Param("quizId")
	if quizId == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Quiz ID is required"})
		return
	}
	if err := db.DeleteQuiz(quizId); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete quiz"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Quiz deleted"})
}

// Get /me/announcements
// List announcements for the authenticated user
func (api *DefaultAPI) MeAnnouncementsGet(c *gin.Context) {
	studentID, ok := auth.GetUserIDFromContext(c)
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}
	announcements, err := db.ListAnnouncementsForStudent(studentID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch announcements"})
		return
	}
	c.JSON(http.StatusOK, announcements)
}

// Get /me/notifications
// List notifications for the authenticated user
func (api *DefaultAPI) MeNotificationsGet(c *gin.Context) {
	studentID, ok := auth.GetUserIDFromContext(c)
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}
	notifications, err := db.ListNotifications(studentID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch notifications"})
		return
	}
	c.JSON(http.StatusOK, notifications)
}
