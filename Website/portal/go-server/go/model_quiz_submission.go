/*
 * Student Portal API
 *
 * API specification for a student portal system supporting authentication, course management, announcements, and student profiles.
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package openapi

type QuizSubmission struct {
	Id string `json:"id,omitempty"`

	QuizId string `json:"quizId,omitempty"`

	Answers []QuizSubmissionAnswersInner `json:"answers,omitempty"`
}
