/*
 * Student Portal API
 *
 * API specification for a student portal system supporting authentication, course management, announcements, and student profiles.
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package openapi

import (
	"time"
)

type Notification struct {

	Id string `json:"id,omitempty"`

	Message string `json:"message,omitempty"`

	Date time.Time `json:"date,omitempty"`
}
