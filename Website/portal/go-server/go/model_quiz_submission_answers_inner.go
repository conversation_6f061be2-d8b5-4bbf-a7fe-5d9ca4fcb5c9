/*
 * Student Portal API
 *
 * API specification for a student portal system supporting authentication, course management, announcements, and student profiles.
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package openapi

type QuizSubmissionAnswersInner struct {

	QuestionId string `json:"questionId,omitempty"`

	Answer string `json:"answer,omitempty"`
}
