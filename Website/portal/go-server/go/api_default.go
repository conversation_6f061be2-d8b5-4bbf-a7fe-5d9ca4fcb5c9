/*
 * Student Portal API
 *
 * API specification for a student portal system supporting authentication, course management, announcements, and student profiles.
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package openapi

import (
	"fmt"
	"portal/internal/auth"
	"portal/internal/db"
	"portal/internal/model"

	"github.com/gin-gonic/gin"
)

type DefaultAPI struct {
}

// Get /courses/:courseId/assignments/:assignmentId
// Get assignment details
func (api *DefaultAPI) CoursesCourseIdAssignmentsAssignmentIdGet(c *gin.Context) {
	courseId := c.Param("courseId")
	if courseId == "" {
		c.JSON(400, gin.H{"error": "Course ID is required"})
		return
	}
	assignmentId := c.Param("assignmentId")
	if assignmentId == "" {
		c.JSON(400, gin.H{"error": "Assignment ID is required"})
		return
	}
	assignment, err := db.GetAssignmentByID(assignmentId, courseId)
	if err != nil {
		c.<PERSON>(404, gin.H{"error": "Assignment not found"})
		return
	}
	// Optionally check assignment.CourseID == courseId
	c.JSON(200, assignment)
}

// Put /courses/:courseId/assignments/:assignmentId
// Update assignment
func (api *DefaultAPI) CoursesCourseIdAssignmentsAssignmentIdPut(c *gin.Context) {
	assignmentId := c.Param("assignmentId")
	if assignmentId == "" {
		c.JSON(400, gin.H{"error": "Assignment ID is required"})
		return
	}
	var update model.AssignmentUpdate
	if err := c.BindJSON(&update); err != nil {
		c.JSON(400, gin.H{"error": "Invalid input"})
		return
	}
	if err := db.UpdateAssignment(assignmentId, &update); err != nil {
		c.JSON(500, gin.H{"error": "Failed to update assignment"})
		return
	}
	c.JSON(200, gin.H{"message": "Assignment updated"})
}

// Post /courses/:courseId/assignments/:assignmentId/submit
// Submit homework
func (api *DefaultAPI) CoursesCourseIdAssignmentsAssignmentIdSubmitPost(c *gin.Context) {
	courseId := c.Param("courseId")
	if courseId == "" {
		c.JSON(400, gin.H{"error": "Course ID is required"})
		return
	}
	id := c.Param("assignmentId")
	if id == "" {
		c.JSON(400, gin.H{"error": "Assignment ID is required"})
		return
	}
	var submission Submission
	if err := c.BindJSON(&submission); err != nil {
		c.JSON(400, gin.H{"error": "Invalid input"})
		return
	}

	// Convert to internal model
	internalSubmission := model.Submission{
		AssignmentID: id,
		StudentID:    submission.StudentId,
		SubmittedAt:  submission.SubmittedAt,
		Status:       "submitted",
	}

	if err := db.CreateSubmission(&internalSubmission); err != nil {
		c.JSON(500, gin.H{"error": "Failed to submit assignment"})
		return
	}
	c.JSON(200, gin.H{"message": "Assignment submitted successfully"})
}

// Get /courses/:courseId/assignments
// List assignments
func (api *DefaultAPI) CoursesCourseIdAssignmentsGet(c *gin.Context) {
	courseId := c.Param("courseId")
	if courseId == "" {
		c.JSON(400, gin.H{"error": "Course ID is required"})
		return
	}
	assignments, err := db.ListAssignmentsByCourse(courseId)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to fetch assignments"})
		return
	}
	c.JSON(200, assignments)
}

// Post /courses/:courseId/assignments
// Create assignment
func (api *DefaultAPI) CoursesCourseIdAssignmentsPost(c *gin.Context) {
	courseId := c.Param("courseId")
	if courseId == "" {
		c.JSON(400, gin.H{"error": "Course ID is required"})
		return
	}
	var assignment model.AssignmentCreate
	if err := c.BindJSON(&assignment); err != nil {
		c.JSON(400, gin.H{"error": "Invalid input"})
		return
	}
	assignment.CourseID = courseId
	if err := db.CreateAssignment(&assignment); err != nil {
		c.JSON(500, gin.H{"error": "Failed to create assignment"})
		return
	}
	c.JSON(201, assignment)
}

// Get /courses/:courseId/quizzes
// List quizzes
func (api *DefaultAPI) CoursesCourseIdQuizzesGet(c *gin.Context) {
	courseId := c.Param("courseId")
	if courseId == "" {
		c.JSON(400, gin.H{"error": "Course ID is required"})
		return
	}
	quizzes, err := db.ListQuizzesByCourse(courseId)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to fetch quizzes"})
		return
	}
	c.JSON(200, quizzes)
}

// Post /courses/:courseId/quizzes
// Create quiz
func (api *DefaultAPI) CoursesCourseIdQuizzesPost(c *gin.Context) {
	courseId := c.Param("courseId")
	if courseId == "" {
		c.JSON(400, gin.H{"error": "Course ID is required"})
		return
	}
	var quiz model.Quiz
	if err := c.BindJSON(&quiz); err != nil {
		c.JSON(400, gin.H{"error": "Invalid input"})
		return
	}

	// Convert to internal model
	internalQuiz := model.QuizCreate{
		Title:       quiz.Title,
		Description: quiz.Description,
		CourseID:    courseId,
	}

	if err := db.CreateQuiz(&internalQuiz); err != nil {
		c.JSON(500, gin.H{"error": "Failed to create quiz"})
		return
	}
	c.JSON(201, quiz)
}

// Get /courses/:courseId/quizzes/:quizId
// Get quiz details
func (api *DefaultAPI) CoursesCourseIdQuizzesQuizIdGet(c *gin.Context) {
	quizId := c.Param("quizId")
	if quizId == "" {
		c.JSON(400, gin.H{"error": "Quiz ID is required"})
		return
	}
	quiz, err := db.GetQuizByID(quizId)
	if err != nil {
		c.JSON(404, gin.H{"error": "Quiz not found"})
		return
	}
	c.JSON(200, quiz)
}

// Put /courses/:courseId/quizzes/:quizId
// Update quiz
func (api *DefaultAPI) CoursesCourseIdQuizzesQuizIdPut(c *gin.Context) {
	quizId := c.Param("quizId")
	if quizId == "" {
		c.JSON(400, gin.H{"error": "Quiz ID is required"})
		return
	}
	var update model.QuizUpdate
	if err := c.BindJSON(&update); err != nil {
		c.JSON(400, gin.H{"error": "Invalid input"})
		return
	}
	if err := db.UpdateQuiz(quizId, &update); err != nil {
		c.JSON(500, gin.H{"error": "Failed to update quiz"})
		return
	}
	c.JSON(200, gin.H{"message": "Quiz updated"})
}

// Post /courses/:courseId/quizzes/:quizId/submit
// Submit quiz
func (api *DefaultAPI) CoursesCourseIdQuizzesQuizIdSubmitPost(c *gin.Context) {
	courseId := c.Param("courseId")
	if courseId == "" {
		c.JSON(400, gin.H{"error": "Course ID is required"})
		return
	}
	quizId := c.Param("quizId")
	if quizId == "" {
		c.JSON(400, gin.H{"error": "Quiz ID is required"})
		return
	}
	var submission QuizSubmission
	if err := c.BindJSON(&submission); err != nil {
		c.JSON(400, gin.H{"error": "Invalid input"})
		return
	}
	submission.QuizId = quizId

	// Get student ID from auth context
	userID, ok := auth.GetUserIDFromContext(c)
	if !ok {
		c.JSON(401, gin.H{"error": "Unauthorized"})
		return
	}

	// Convert to internal model
	internalSubmission := model.QuizSubmission{
		QuizID:    submission.QuizId,
		StudentID: fmt.Sprintf("%d", userID),
	}

	if err := db.CreateQuizSubmission(&internalSubmission); err != nil {
		c.JSON(500, gin.H{"error": "Failed to submit quiz"})
		return
	}
	c.JSON(200, gin.H{"message": "Quiz submitted successfully"})
}

// Get /courses/:courseId/materials
// List course materials
func (api *DefaultAPI) CoursesCourseIdMaterialsGet(c *gin.Context) {
	courseId := c.Param("courseId")
	if courseId == "" {
		c.JSON(400, gin.H{"error": "Course ID is required"})
		return
	}
	materials, err := db.ListMaterialsByCourse(courseId)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to fetch materials"})
		return
	}
	c.JSON(200, materials)
}

// Get /courses
// List all courses
func (api *DefaultAPI) CoursesGet(c *gin.Context) {
	courses, err := db.ListCourses()
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to fetch courses"})
		return
	}
	c.JSON(200, courses)
}

// Get /courses/:courseId
// Get course details
func (api *DefaultAPI) CoursesCourseIdGet(c *gin.Context) {
	id := c.Param("courseId")
	if id == "" {
		c.JSON(400, gin.H{"error": "Course ID is required"})
		return
	}
	course, err := db.GetCourseByID(id)
	if err != nil {
		c.JSON(404, gin.H{"error": "Course not found"})
		return
	}
	c.JSON(200, course)
}

// Get /dashboard
// Get student dashboard
func (api *DefaultAPI) DashboardGet(c *gin.Context) {
	studentID, ok := auth.GetUserIDFromContext(c)
	if !ok {
		c.JSON(401, gin.H{"error": "Unauthorized"})
		return
	}
	dashboard, err := db.GetDashboardData(fmt.Sprintf("%d", studentID))
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to fetch dashboard data"})
		return
	}
	c.JSON(200, dashboard)
}

// Post /logout
// Logout
func (api *DefaultAPI) LogoutPost(c *gin.Context) {
	// Clear any session data if needed
	c.JSON(200, gin.H{"message": "Logged out successfully"})
}

// Get /me/announcements
// List my announcements
func (api *DefaultAPI) MeAnnouncementsGet(c *gin.Context) {
	studentID, ok := auth.GetUserIDFromContext(c)
	if !ok {
		c.JSON(401, gin.H{"error": "Unauthorized"})
		return
	}
	announcements, err := db.ListAnnouncementsForStudent(fmt.Sprintf("%d", studentID))
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to fetch announcements"})
		return
	}
	c.JSON(200, announcements)
}

// Get /me/notifications
// List my notifications
func (api *DefaultAPI) MeNotificationsGet(c *gin.Context) {
	studentID, ok := auth.GetUserIDFromContext(c)
	if !ok {
		c.JSON(401, gin.H{"error": "Unauthorized"})
		return
	}
	notifications, err := db.ListNotifications(fmt.Sprintf("%d", studentID))
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to fetch notifications"})
		return
	}
	c.JSON(200, notifications)
}

// Get /notification
// List notifications (DEPRECATED)
// Deprecated
func (api *DefaultAPI) NotificationGet(c *gin.Context) {
	// Your handler implementation
	c.JSON(200, gin.H{"status": "OK"})
}

// Get /students/:studentId
// Get current student profile
func (api *DefaultAPI) StudentsStudentIdGet(c *gin.Context) {
	studentId := c.Param("studentId")
	if studentId == "" {
		c.JSON(400, gin.H{"error": "Student ID is required"})
		return
	}
	student, err := db.GetStudentByID(studentId)
	if err != nil {
		c.JSON(404, gin.H{"error": "Student not found"})
		return
	}
	c.JSON(200, student)
}

// Put /students/:studentId
// Update current student profile
func (api *DefaultAPI) StudentsStudentIdPut(c *gin.Context) {
	studentId := c.Param("studentId")
	if studentId == "" {
		c.JSON(400, gin.H{"error": "Student ID is required"})
		return
	}
	var update StudentUpdate
	if err := c.BindJSON(&update); err != nil {
		c.JSON(400, gin.H{"error": "Invalid input"})
		return
	}

	// Convert to internal model
	internalUpdate := model.StudentUpdate{
		Email:     update.Email,
		FirstName: update.FirstName,
		LastName:  update.LastName,
		Password:  update.Password,
	}

	if err := db.UpdateStudent(studentId, &internalUpdate); err != nil {
		c.JSON(500, gin.H{"error": "Failed to update student"})
		return
	}
	c.JSON(200, gin.H{"message": "Student updated successfully"})
}

// Get /user
// Get current user info
func (api *DefaultAPI) UserGet(c *gin.Context) {
	userID, ok := auth.GetUserIDFromContext(c)
	if !ok {
		c.JSON(401, gin.H{"error": "Unauthorized"})
		return
	}
	user, err := db.GetUserByID(fmt.Sprintf("%d", userID))
	if err != nil {
		c.JSON(404, gin.H{"error": "User not found"})
		return
	}
	c.JSON(200, user)
}

// POST /courses/:courseId/enroll
// Enroll current student in a course
func (api *DefaultAPI) CoursesCourseIdEnrollPost(c *gin.Context) {
	studentID, ok := auth.GetUserIDFromContext(c)
	if !ok {
		c.JSON(401, gin.H{"error": "Unauthorized"})
		return
	}
	studentIDStr := fmt.Sprintf("%v", studentID)
	courseID := c.Param("courseId")
	if courseID == "" {
		c.JSON(400, gin.H{"error": "Course ID is required"})
		return
	}
	if db.StudentEnrolledInCourse(studentIDStr, courseID) {
		c.JSON(403, gin.H{"error": "Already enrolled"})
		return
	}
	_, err := db.CreateEnrollmentLegacy(studentIDStr, courseID)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to enroll"})
		return
	}
	c.JSON(201, gin.H{"message": "Enrolled successfully"})
}

// DELETE /courses/:courseId/enroll
// Unenroll current student from a course
func (api *DefaultAPI) CoursesCourseIdEnrollDelete(c *gin.Context) {
	studentID, ok := auth.GetUserIDFromContext(c)
	if !ok {
		c.JSON(401, gin.H{"error": "Unauthorized"})
		return
	}
	studentIDStr := fmt.Sprintf("%v", studentID)
	courseID := c.Param("courseId")
	if courseID == "" {
		c.JSON(400, gin.H{"error": "Course ID is required"})
		return
	}
	if !db.StudentEnrolledInCourse(studentIDStr, courseID) {
		c.JSON(403, gin.H{"error": "Not enrolled in this course"})
		return
	}
	err := db.DeleteEnrollmentByStudentAndCourse(studentIDStr, courseID)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to unenroll"})
		return
	}
	c.JSON(200, gin.H{"message": "Unenrolled successfully"})
}

// POST /courses/:courseId/enroll/:studentId
// Enroll a student in a course (admin/teacher only)
func (api *DefaultAPI) CoursesCourseIdEnrollStudentIdPost(c *gin.Context) {
	// TODO: Check if user is admin/teacher/staff
	studentID := c.Param("studentId")
	courseID := c.Param("courseId")
	if studentID == "" || courseID == "" {
		c.JSON(400, gin.H{"error": "Student ID and Course ID are required"})
		return
	}
	if db.StudentEnrolledInCourse(studentID, courseID) {
		c.JSON(403, gin.H{"error": "Student already enrolled"})
		return
	}
	_, err := db.CreateEnrollmentLegacy(studentID, courseID)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to enroll student"})
		return
	}
	c.JSON(201, gin.H{"message": "Student enrolled successfully"})
}

// DELETE /courses/:courseId/enroll/:studentId
// Unenroll a student from a course (admin/teacher only)
func (api *DefaultAPI) CoursesCourseIdEnrollStudentIdDelete(c *gin.Context) {
	// TODO: Check if user is admin/teacher/staff
	studentID := c.Param("studentId")
	courseID := c.Param("courseId")
	if studentID == "" || courseID == "" {
		c.JSON(400, gin.H{"error": "Student ID and Course ID are required"})
		return
	}
	if !db.StudentEnrolledInCourse(studentID, courseID) {
		c.JSON(403, gin.H{"error": "Student not enrolled in this course"})
		return
	}
	err := db.DeleteEnrollmentByStudentAndCourse(studentID, courseID)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to unenroll student"})
		return
	}
	c.JSON(200, gin.H{"message": "Student unenrolled successfully"})
}
