/*
 * Student Portal API
 *
 * API specification for a student portal system supporting authentication, course management, announcements, and student profiles.
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package openapi

type QuizQuestion struct {

	Id string `json:"id,omitempty"`

	Question string `json:"question,omitempty"`

	Options []string `json:"options,omitempty"`

	Answer string `json:"answer,omitempty"`
}
