/*
 * Student Portal API
 *
 * API specification for a student portal system supporting authentication, course management, announcements, and student profiles.
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package openapi

import (
	"net/http"

	"portal/internal/auth"

	"github.com/gin-gonic/gin"
)

// Route is the information for every URI.
type Route struct {
	// Name is the name of this Route.
	Name string
	// Method is the string for the HTTP method. ex) GET, POST etc..
	Method string
	// Pattern is the pattern of the URI.
	Pattern string
	// HandlerFunc is the handler function of this route.
	HandlerFunc gin.HandlerFunc
}

// NewRouter returns a new router.
func NewRouter(handleFunctions ApiHandleFunctions) *gin.Engine {
	return NewRouterWithGinEngine(gin.Default(), handleFunctions)
}

// NewRouter add routes to existing gin engine.
func NewRouterWithGinEngine(router *gin.Engine, handleFunctions ApiHandleFunctions) *gin.Engine {
	for _, route := range getRoutes(handleFunctions) {
		if route.HandlerFunc == nil {
			route.HandlerFunc = DefaultHandleFunc
		}
		pattern := route.Pattern

		// For testing: Skip auth middleware for certain endpoints
		skipAuth := pattern == "/courses" ||
			pattern == "/courses/:courseId" ||
			pattern == "/courses/:courseId/assignments" ||
			pattern == "/courses/:courseId/quizzes" ||
			pattern == "/courses/:courseId/materials" ||
			pattern == "/me/announcements" ||
			pattern == "/me/notifications"

		switch route.Method {
		case http.MethodGet:
			if skipAuth {
				router.GET(pattern, route.HandlerFunc)
			} else {
				router.GET(pattern, auth.AuthMiddleware(), route.HandlerFunc)
			}
		case http.MethodPost:
			router.POST(pattern, auth.AuthMiddleware(), route.HandlerFunc)
		case http.MethodPut:
			router.PUT(pattern, auth.AuthMiddleware(), route.HandlerFunc)
		case http.MethodPatch:
			router.PATCH(pattern, auth.AuthMiddleware(), route.HandlerFunc)
		case http.MethodDelete:
			router.DELETE(pattern, auth.AuthMiddleware(), route.HandlerFunc)
		}
	}
	return router
}

// Default handler for not yet implemented routes
func DefaultHandleFunc(c *gin.Context) {
	c.String(http.StatusNotImplemented, "501 not implemented")
}

type ApiHandleFunctions struct {

	// Routes for the DefaultAPI part of the API
	DefaultAPI DefaultAPI
}

func getRoutes(handleFunctions ApiHandleFunctions) []Route {
	return []Route{
		{
			"MeAnnouncementsGet",
			http.MethodGet,
			"/me/announcements",
			handleFunctions.DefaultAPI.MeAnnouncementsGet,
		},
		{
			"MeNotificationsGet",
			http.MethodGet,
			"/me/notifications",
			handleFunctions.DefaultAPI.MeNotificationsGet,
		},
		/*// Deprecated endpoints
		{
			"AnnouncementsGet",
			http.MethodGet,
			"/announcements",
			handleFunctions.DefaultAPI.AnnouncementsGet,
		},
		{
			"NotificationGet",
			http.MethodGet,
			"/notification",
			handleFunctions.DefaultAPI.NotificationGet,
		},*/
		{
			"CoursesCourseIdAssignmentsGet",
			http.MethodGet,
			"/courses/:courseId/assignments",
			handleFunctions.DefaultAPI.CoursesCourseIdAssignmentsGet,
		},
		{
			"CoursesCourseIdAssignmentsPost",
			http.MethodPost,
			"/courses/:courseId/assignments",
			handleFunctions.DefaultAPI.CoursesCourseIdAssignmentsPost,
		},
		{
			"CoursesCourseIdAssignmentsAssignmentIdGet",
			http.MethodGet,
			"/courses/:courseId/assignments/:assignmentId",
			handleFunctions.DefaultAPI.CoursesCourseIdAssignmentsAssignmentIdGet,
		},
		{
			"CoursesCourseIdAssignmentsAssignmentIdPut",
			http.MethodPut,
			"/courses/:courseId/assignments/:assignmentId",
			handleFunctions.DefaultAPI.CoursesCourseIdAssignmentsAssignmentIdPut,
		},
		{
			"CoursesCourseIdAssignmentsAssignmentIdSubmitPost",
			http.MethodPost,
			"/courses/:courseId/assignments/:assignmentId/submit",
			handleFunctions.DefaultAPI.CoursesCourseIdAssignmentsAssignmentIdSubmitPost,
		},
		{
			"CoursesCourseIdGet",
			http.MethodGet,
			"/courses/:courseId",
			handleFunctions.DefaultAPI.CoursesCourseIdGet,
		},
		{
			"MaterialsGet",
			http.MethodGet,
			"/courses/:courseId/materials",
			handleFunctions.DefaultAPI.CoursesCourseIdMaterialsGet,
		},
		{
			"CoursesCourseIdQuizzesGet",
			http.MethodGet,
			"/courses/:courseId/quizzes",
			handleFunctions.DefaultAPI.CoursesCourseIdQuizzesGet,
		},
		{
			"CoursesCourseIdQuizzesPost",
			http.MethodPost,
			"/courses/:courseId/quizzes",
			handleFunctions.DefaultAPI.CoursesCourseIdQuizzesPost,
		},
		{
			"CoursesCourseIdQuizzesQuizIdGet",
			http.MethodGet,
			"/courses/:courseId/quizzes/:quizId",
			handleFunctions.DefaultAPI.CoursesCourseIdQuizzesQuizIdGet,
		},
		{
			"CoursesCourseIdQuizzesQuizIdPut",
			http.MethodPut,
			"/courses/:courseId/quizzes/:quizId",
			handleFunctions.DefaultAPI.CoursesCourseIdQuizzesQuizIdPut,
		},
		{
			"CoursesCourseIdQuizzesQuizIdSubmitPost",
			http.MethodPost,
			"/courses/:courseId/quizzes/:quizId/submit",
			handleFunctions.DefaultAPI.CoursesCourseIdQuizzesQuizIdSubmitPost,
		},
		{
			"CoursesGet",
			http.MethodGet,
			"/courses",
			handleFunctions.DefaultAPI.CoursesGet,
		},
		{
			"DashboardGet",
			http.MethodGet,
			"/dashboard",
			handleFunctions.DefaultAPI.DashboardGet,
		},
		{
			"LogoutPost",
			http.MethodPost,
			"/logout",
			handleFunctions.DefaultAPI.LogoutPost,
		},
		{
			"StudentsStudentIdGet",
			http.MethodGet,
			"/students/:studentId",
			handleFunctions.DefaultAPI.StudentsStudentIdGet,
		},
		{
			"StudentsStudentIdPut",
			http.MethodPut,
			"/students/:studentId",
			handleFunctions.DefaultAPI.StudentsStudentIdPut,
		},
		{
			"UserGet",
			http.MethodGet,
			"/user",
			handleFunctions.DefaultAPI.UserGet,
		},
		{
			"CoursesCourseIdEnrollPost",
			http.MethodPost,
			"/courses/:courseId/enroll",
			handleFunctions.DefaultAPI.CoursesCourseIdEnrollPost,
		},
		{
			"CoursesCourseIdEnrollDelete",
			http.MethodDelete,
			"/courses/:courseId/enroll",
			handleFunctions.DefaultAPI.CoursesCourseIdEnrollDelete,
		},
		{
			"CoursesCourseIdEnrollStudentIdPost",
			http.MethodPost,
			"/courses/:courseId/enroll/:studentId",
			handleFunctions.DefaultAPI.CoursesCourseIdEnrollStudentIdPost,
		},
		{
			"CoursesCourseIdEnrollStudentIdDelete",
			http.MethodDelete,
			"/courses/:courseId/enroll/:studentId",
			handleFunctions.DefaultAPI.CoursesCourseIdEnrollStudentIdDelete,
		},
	}
}
