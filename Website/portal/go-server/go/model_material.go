/*
 * Student Portal API
 *
 * API specification for a student portal system supporting authentication, course management, announcements, and student profiles.
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package openapi

type Material struct {

	Id string `json:"id,omitempty"`

	Title string `json:"title,omitempty"`

	Url string `json:"url,omitempty"`

	Type string `json:"type,omitempty"`
}
