/*
 * Student Portal API
 *
 * API specification for a student portal system supporting authentication, course management, announcements, and student profiles.
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package openapi

import (
	"time"
)

type Submission struct {

	Id string `json:"id,omitempty"`

	AssignmentId string `json:"assignmentId,omitempty"`

	StudentId string `json:"studentId,omitempty"`

	Content string `json:"content,omitempty"`

	SubmittedAt time.Time `json:"submittedAt,omitempty"`

	Grade float32 `json:"grade,omitempty"`

	Feedback string `json:"feedback,omitempty"`
}
