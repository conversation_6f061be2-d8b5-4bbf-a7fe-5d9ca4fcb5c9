/*
 * Student Portal API
 *
 * API specification for a student portal system supporting authentication, course management, announcements, and student profiles.
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package openapi

type StudentUpdate struct {

	Email string `json:"email,omitempty"`

	FirstName string `json:"firstName,omitempty"`

	LastName string `json:"lastName,omitempty"`

	Password string `json:"password,omitempty"`
}
