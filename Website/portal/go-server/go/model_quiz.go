/*
 * Student Portal API
 *
 * API specification for a student portal system supporting authentication, course management, announcements, and student profiles.
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package openapi

import (
	"time"
)

type Quiz struct {

	Id string `json:"id,omitempty"`

	Title string `json:"title,omitempty"`

	Description string `json:"description,omitempty"`

	Questions []QuizQuestion `json:"questions,omitempty"`

	DueDate time.Time `json:"dueDate,omitempty"`

	Status string `json:"status,omitempty"`
}
