/*
 * Student Portal API
 *
 * API specification for a student portal system supporting authentication, course management, announcements, and student profiles.
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package openapi

type Dashboard struct {

	Student Student `json:"student,omitempty"`

	Courses []Course `json:"courses,omitempty"`

	Announcements []Announcement `json:"announcements,omitempty"`
}
