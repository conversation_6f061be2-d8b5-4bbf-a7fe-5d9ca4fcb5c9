/*
 * Student Portal API
 *
 * API specification for a student portal system supporting authentication, course management, announcements, and student profiles.
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package openapi

import (
	"time"
)

type Enrollment struct {

	Id string `json:"id,omitempty"`

	StudentId string `json:"studentId,omitempty"`

	CourseId string `json:"courseId,omitempty"`

	EnrolledAt time.Time `json:"enrolledAt,omitempty"`

	Status string `json:"status,omitempty"`
}
