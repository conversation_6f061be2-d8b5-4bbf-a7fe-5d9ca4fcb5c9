// env.example.js - Example environment configurations
// Copy this file to env.js and customize for your setup

// =============================================================================
// DEVELOPMENT CONFIGURATION EXAMPLES
// =============================================================================

// Example 1: Standard Development Setup (Default)
// This is what happens automatically if you don't set anything
/*
window.ENV_LOGIN_PORT = '8080';
window.ENV_PORTAL_PORT = '8082';
window.ENV_DEBUG = 'true';
*/

// Example 2: Custom Development Ports
// Use this if your backends are running on different ports
/*
window.ENV_LOGIN_PORT = '9080';
window.ENV_PORTAL_PORT = '9082';
window.ENV_DEBUG = 'true';
*/

// Example 3: Remote Development Server
// Use this if connecting to backends on another machine
/*
window.ENV_LOGIN_URL = 'http://*************:8080';
window.ENV_PORTAL_URL = 'http://*************:8082';
window.ENV_DEBUG = 'true';
*/

// Example 4: Docker Development Setup
// Use this if your backends are running in Docker
/*
window.ENV_LOGIN_URL = 'http://localhost:8080';
window.ENV_PORTAL_URL = 'http://localhost:8082';
window.ENV_DEBUG = 'true';
*/

// =============================================================================
// PRODUCTION CONFIGURATION EXAMPLES
// =============================================================================

// Example 5: Production URLs
// Use this for production deployment
/*
window.ENV_LOGIN_URL = 'https://stemblock-login-gljgs.ondigitalocean.app';
window.ENV_PORTAL_URL = 'https://stemblock-portal-api.ondigitalocean.app';
window.ENV_DEBUG = 'false';
*/

// Example 6: Staging Environment
// Use this for staging/testing environment
/*
window.ENV_LOGIN_URL = 'https://staging-login.stemblock.ca';
window.ENV_PORTAL_URL = 'https://staging-portal.stemblock.ca';
window.ENV_DEBUG = 'true';
*/

// =============================================================================
// TESTING SCENARIOS
// =============================================================================

// Example 7: Testing with Different Backends
// Mix and match login and portal backends
/*
window.ENV_LOGIN_URL = 'https://stemblock-login-gljgs.ondigitalocean.app';  // Production login
window.ENV_PORTAL_URL = 'http://localhost:8082';                           // Local portal
window.ENV_DEBUG = 'true';
*/

// Example 8: Load Testing Setup
// Point to load testing environment
/*
window.ENV_LOGIN_URL = 'http://load-test-login.internal:8080';
window.ENV_PORTAL_URL = 'http://load-test-portal.internal:8082';
window.ENV_DEBUG = 'false';
*/

// =============================================================================
// INSTRUCTIONS
// =============================================================================

/*
To use any of these configurations:

1. Copy this file to env.js:
   cp env.example.js env.js

2. Uncomment the configuration you want to use

3. Modify the URLs/ports as needed for your setup

4. Reload the test.html page

5. Check the "Current Configuration" section to verify your settings

The configuration will be automatically detected and applied when you load test.html.
*/
