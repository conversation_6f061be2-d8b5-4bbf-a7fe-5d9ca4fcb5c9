package model

import (
	"time"
)

// User represents a portal user (student, coach, admin, etc.)
type User struct {
	ID            string    `json:"id,omitempty" gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	Username      string    `json:"username" gorm:"uniqueIndex;not null"`
	Email         string    `json:"email" gorm:"uniqueIndex;not null"`
	Password_hash string    `json:"-" gorm:"not null"`    // hashed password
	Role          string    `json:"role" gorm:"not null"` // e.g. "student", "coach", "admin"
	FirstName     string    `json:"firstName,omitempty"`
	LastName      string    `json:"lastName,omitempty"`
	CreatedAt     time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt     time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
}

// Coach model, also references UserID
// You can expand this as needed for coach-specific fields
type Coach struct {
	Id        string    `json:"id,omitempty" gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	UserID    string    `json:"userId" gorm:"type:uuid;not null;index"`
	CreatedAt time.Time `json:"createdAt" gorm:"autoCreateTime"`
}

type Student struct {
	Id              string   `json:"id,omitempty" gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	UserID          string   `json:"userId" gorm:"type:uuid;not null;index"`
	EnrolledCourses []Course `json:"enrolledCourses,omitempty" gorm:"-"` // ignored by GORM, use Enrollment for DB
}

type Announcement struct {
	Id        string             `json:"id,omitempty" gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	Title     string             `json:"title,omitempty"`
	Content   string             `json:"content,omitempty"`
	Date      time.Time          `json:"date,omitempty"`
	CourseID  string             `json:"course_id"`
	Files     []AnnouncementFile `json:"files,omitempty" gorm:"foreignKey:AnnouncementID"`
	CreatedAt time.Time          `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt *time.Time         `json:"updatedAt,omitempty" gorm:"autoUpdateTime:nano"`
}

type AnnouncementCreate struct {
	Title    string `json:"title" binding:"required"`
	Content  string `json:"content" binding:"required"`
	CourseID string `json:"courseId" binding:"required"`
}

type AnnouncementUpdate struct {
	Title    *string `json:"title,omitempty"`
	Content  *string `json:"content,omitempty"`
	CourseID *string `json:"courseId,omitempty"`
	// Other fields (e.g., Id, Date) will be empty or unchanged
}

type Notification struct {
	Id        string    `json:"id,omitempty" gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	Message   string    `json:"message,omitempty"`
	Date      time.Time `json:"date,omitempty"`
	StudentID string    `json:"student_id"` // Link to student
}

type NotificationCreate struct {
	Message   string `json:"message" binding:"required"`
	StudentID string `json:"studentId" binding:"required"`
}

type NotificationUpdate struct {
	Message   *string `json:"message,omitempty"`
	StudentID *string `json:"studentId,omitempty"`
	// Other fields (e.g., Id, Date) will be empty or unchanged
}

type Course struct {
	Id          string `json:"id,omitempty" gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	Name        string `json:"name,omitempty"`
	Description string `json:"description,omitempty"`
	Instructor  string `json:"instructor,omitempty"`
}

type Assignment struct {
	Id          string           `json:"id,omitempty" gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	Title       string           `json:"title,omitempty"`
	Description string           `json:"description,omitempty"`
	DueDate     time.Time        `json:"dueDate,omitempty"`
	Status      string           `json:"status,omitempty"`
	CourseID    string           `json:"course_id"`
	Files       []AssignmentFile `json:"files,omitempty" gorm:"foreignKey:AssignmentID"`
	CreatedAt   time.Time        `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   *time.Time       `json:"updatedAt,omitempty" gorm:"autoUpdateTime:nano"`
}

type AssignmentCreate struct {
	Title       string           `json:"title" binding:"required"`
	Description string           `json:"description" binding:"required"`
	DueDate     time.Time        `json:"dueDate" binding:"required"`
	CourseID    string           `json:"courseId" binding:"required"`
	Files       []AssignmentFile `json:"files,omitempty" gorm:"foreignKey:AssignmentID"`
}

type AssignmentUpdate struct {
	Title       *string          `json:"title,omitempty"`
	Description *string          `json:"description,omitempty"`
	DueDate     *time.Time       `json:"dueDate,omitempty"`
	Status      *string          `json:"status,omitempty"`
	CourseID    *string          `json:"courseId,omitempty"`
	Files       []AssignmentFile `json:"files,omitempty" gorm:"foreignKey:AssignmentID"`
}

type Submission struct {
	Id           string           `json:"id,omitempty" gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	AssignmentID string           `json:"assignmentId"`
	StudentID    string           `json:"studentId"`
	SubmittedAt  time.Time        `json:"submittedAt"`
	Status       string           `json:"status,omitempty"`
	Files        []SubmissionFile `json:"files,omitempty" gorm:"foreignKey:SubmissionID"`
	CreatedAt    time.Time        `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt    *time.Time       `json:"updatedAt,omitempty" gorm:"autoUpdateTime:nano"`
}

type Quiz struct {
	Id          string         `json:"id,omitempty" gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	Title       string         `json:"title,omitempty"`
	Description string         `json:"description,omitempty"`
	Questions   []QuizQuestion `json:"questions,omitempty" gorm:"-"`
	DueDate     time.Time      `json:"dueDate,omitempty"`
	Status      string         `json:"status,omitempty"`
	CourseID    string         `json:"course_id"`
	Files       []QuizFile     `json:"files,omitempty" gorm:"foreignKey:QuizID"`
	CreatedAt   time.Time      `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   *time.Time     `json:"updatedAt,omitempty" gorm:"autoUpdateTime:nano"`
}

type QuizCreate struct {
	Title       string         `json:"title" binding:"required"`
	Description string         `json:"description" binding:"required"`
	Questions   []QuizQuestion `json:"questions" binding:"required"`
	DueDate     time.Time      `json:"dueDate" binding:"required"`
	CourseID    string         `json:"courseId" binding:"required"`
	Files       []QuizFile     `json:"files,omitempty" gorm:"foreignKey:QuizID"`
}

type QuizUpdate struct {
	Title       *string         `json:"title,omitempty"`
	Description *string         `json:"description,omitempty"`
	Questions   *[]QuizQuestion `json:"questions,omitempty"`
	DueDate     *time.Time      `json:"dueDate,omitempty"`
	Status      *string         `json:"status,omitempty"`
	CourseID    *string         `json:"courseId,omitempty"`
	Files       []QuizFile      `json:"files,omitempty" gorm:"foreignKey:QuizID"`
	// Other fields (e.g., Id) will be empty or unchanged
}

type QuizQuestion struct {
	// Define fields as in model_quiz_question.go
}

// File represents a file uploaded to the portal (for assignments, announcements, quizzes, etc.)
type File struct {
	ID         string    `json:"id,omitempty" gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	FileName   string    `json:"fileName"`
	FilePath   string    `json:"filePath"`
	FileSize   int64     `json:"fileSize"`
	UploadedAt time.Time `json:"uploadedAt"`
}

// AssignmentFile associates example/starter files with an assignment
type AssignmentFile struct {
	ID           string `json:"id,omitempty" gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	AssignmentID string `json:"assignmentId"`
	FileID       string `json:"fileId"`
	File         File   `json:"file" gorm:"foreignKey:FileID"`
}

// AnnouncementFile associates files with an announcement
type AnnouncementFile struct {
	ID             string `json:"id,omitempty" gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	AnnouncementID string `json:"announcementId"`
	FileID         string `json:"fileId"`
	File           File   `json:"file" gorm:"foreignKey:FileID"`
}

// QuizFile associates files with a quiz
type QuizFile struct {
	ID     string `json:"id,omitempty" gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	QuizID string `json:"quizId"`
	FileID string `json:"fileId"`
	File   File   `json:"file" gorm:"foreignKey:FileID"`
}

// SubmissionFile associates uploaded files with a student's assignment submission
type SubmissionFile struct {
	ID           string `json:"id,omitempty" gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	SubmissionID string `json:"submissionId"`
	FileID       string `json:"fileId"`
	File         File   `json:"file" gorm:"foreignKey:FileID"`
}

// Material represents a class material (e.g., lecture notes, slides, handouts, etc.)
type Material struct {
	ID          string     `json:"id,omitempty" gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	Title       string     `json:"title"`
	Description string     `json:"description"`
	CourseID    string     `json:"courseId" gorm:"index"`
	UploadedBy  string     `json:"uploadedBy"` // e.g., teacher or admin user id
	UploadedAt  time.Time  `json:"uploadedAt"`
	FileID      string     `json:"fileId"`
	File        File       `json:"file" gorm:"foreignKey:FileID"`
	CreatedAt   time.Time  `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   *time.Time `json:"updatedAt,omitempty" gorm:"autoUpdateTime:nano"`
}

// MaterialUpdate is used for PATCH/PUT updates to Material
// All fields are pointers to allow partial updates

type MaterialUpdate struct {
	Title       *string `json:"title,omitempty"`
	Description *string `json:"description,omitempty"`
	CourseID    *string `json:"courseId,omitempty"`
	UploadedBy  *string `json:"uploadedBy,omitempty"`
	FileID      *string `json:"fileId,omitempty"`
}

// Enrollment represents a student enrolled in a course (join table)
type Enrollment struct {
	ID        string    `json:"id,omitempty" gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	StudentID string    `json:"studentId" gorm:"type:uuid;not null;index"`
	CourseID  string    `json:"courseId" gorm:"type:uuid;not null;index"`
	CreatedAt time.Time `json:"createdAt" gorm:"autoCreateTime"`
	//Status    string    `json:"status,omitempty"` // e.g., "active", "completed", "dropped","trial"
	//Paid		bool      `json:"paid,omitempty"` // true if the student has paid for the course
}

// QuizSubmission represents a student's quiz submission
type QuizSubmission struct {
	ID        string                 `json:"id,omitempty" gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	QuizID    string                 `json:"quizId" gorm:"type:uuid;not null;index"`
	StudentID string                 `json:"studentId" gorm:"type:uuid;not null;index"`
	Answers   []QuizSubmissionAnswer `json:"answers,omitempty" gorm:"foreignKey:SubmissionID"`
	CreatedAt time.Time              `json:"createdAt" gorm:"autoCreateTime"`
}

// QuizSubmissionAnswer represents an answer to a quiz question
type QuizSubmissionAnswer struct {
	ID           string `json:"id,omitempty" gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	SubmissionID string `json:"submissionId" gorm:"type:uuid;not null;index"`
	QuestionID   string `json:"questionId" gorm:"type:uuid;not null"`
	Answer       string `json:"answer"`
}

// CourseDetail represents detailed course information
type CourseDetail struct {
	Id          string `json:"id,omitempty"`
	Name        string `json:"name,omitempty"`
	Description string `json:"description,omitempty"`
	Instructor  string `json:"instructor,omitempty"`
	Schedule    string `json:"schedule,omitempty"`
}

// StudentUpdate represents student update data
type StudentUpdate struct {
	Email     string `json:"email,omitempty"`
	FirstName string `json:"firstName,omitempty"`
	LastName  string `json:"lastName,omitempty"`
	Password  string `json:"password,omitempty"`
}

// Dashboard represents dashboard data
type Dashboard struct {
	RecentAnnouncements []Announcement `json:"recentAnnouncements,omitempty"`
	UpcomingAssignments []Assignment   `json:"upcomingAssignments,omitempty"`
}

// Grade represents a grade for an assignment or quiz
type Grade struct {
	ID           string     `json:"id,omitempty" gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	StudentID    string     `json:"studentId" gorm:"type:uuid;not null;index"`
	AssignmentID *string    `json:"assignmentId,omitempty" gorm:"type:uuid;index"`
	QuizID       *string    `json:"quizId,omitempty" gorm:"type:uuid;index"`
	CourseID     string     `json:"courseId" gorm:"type:uuid;not null;index"`
	Score        float64    `json:"score"`
	MaxScore     float64    `json:"maxScore"`
	Percentage   float64    `json:"percentage"`
	LetterGrade  string     `json:"letterGrade,omitempty"`
	Comments     string     `json:"comments,omitempty"`
	GradedAt     *time.Time `json:"gradedAt,omitempty"`
	GradedBy     string     `json:"gradedBy" gorm:"type:uuid;not null"` // Teacher/Coach ID
	CreatedAt    time.Time  `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt    time.Time  `json:"updatedAt" gorm:"autoUpdateTime"`
}

// GradeCreate represents data for creating a new grade
type GradeCreate struct {
	StudentID    string  `json:"studentId" binding:"required"`
	AssignmentID string  `json:"assignmentId,omitempty"`
	QuizID       string  `json:"quizId,omitempty"`
	CourseID     string  `json:"courseId" binding:"required"`
	Score        float64 `json:"score" binding:"required"`
	MaxScore     float64 `json:"maxScore" binding:"required"`
	Comments     string  `json:"comments,omitempty"`
}

// GradeUpdate represents data for updating a grade
type GradeUpdate struct {
	Score       *float64 `json:"score,omitempty"`
	MaxScore    *float64 `json:"maxScore,omitempty"`
	Comments    *string  `json:"comments,omitempty"`
	LetterGrade *string  `json:"letterGrade,omitempty"`
}

// GradeBook represents a student's grades for a course
type GradeBook struct {
	StudentID    string    `json:"studentId"`
	CourseID     string    `json:"courseId"`
	CourseName   string    `json:"courseName"`
	OverallGrade float64   `json:"overallGrade"`
	LetterGrade  string    `json:"letterGrade"`
	Assignments  []Grade   `json:"assignments"`
	Quizzes      []Grade   `json:"quizzes"`
	LastUpdated  time.Time `json:"lastUpdated"`
}

// Attendance represents student attendance for a class
type Attendance struct {
	ID         string    `json:"id,omitempty" gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	StudentID  string    `json:"studentId" gorm:"type:uuid;not null;index"`
	CourseID   string    `json:"courseId" gorm:"type:uuid;not null;index"`
	Date       time.Time `json:"date" gorm:"not null;index"`
	Status     string    `json:"status" gorm:"not null"` // "present", "absent", "late", "excused"
	Notes      string    `json:"notes,omitempty"`
	RecordedBy string    `json:"recordedBy" gorm:"type:uuid;not null"` // Teacher/Coach ID
	CreatedAt  time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt  time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
}

// AttendanceCreate represents data for creating attendance record
type AttendanceCreate struct {
	StudentID string    `json:"studentId" binding:"required"`
	CourseID  string    `json:"courseId" binding:"required"`
	Date      time.Time `json:"date" binding:"required"`
	Status    string    `json:"status" binding:"required"`
	Notes     string    `json:"notes,omitempty"`
}

// AttendanceUpdate represents data for updating attendance
type AttendanceUpdate struct {
	Status *string `json:"status,omitempty"`
	Notes  *string `json:"notes,omitempty"`
}
