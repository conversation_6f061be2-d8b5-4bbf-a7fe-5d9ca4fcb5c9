package converter

import (
	"portal/go-server/go"
	"portal/internal/model"
)

// Convert OpenAPI models to internal models

func ToInternalAssignment(api openapi.Assignment) model.Assignment {
	return model.Assignment{
		Id:          api.Id,
		Title:       api.Title,
		Description: api.Description,
		DueDate:     api.DueDate,
		Status:      api.Status,
	}
}

func ToAPIAssignment(internal model.Assignment) openapi.Assignment {
	return openapi.Assignment{
		Id:          internal.Id,
		Title:       internal.Title,
		Description: internal.Description,
		DueDate:     internal.DueDate,
		Status:      internal.Status,
	}
}

func ToInternalSubmission(api openapi.Submission) model.Submission {
	return model.Submission{
		Id:           api.Id,
		AssignmentID: api.AssignmentId,
		StudentID:    api.StudentId,
		Content:      api.Content,
		SubmittedAt:  api.SubmittedAt,
		Grade:        api.Grade,
		Feedback:     api.Feedback,
	}
}

func ToAPISubmission(internal model.Submission) openapi.Submission {
	return openapi.Submission{
		Id:           internal.Id,
		AssignmentId: internal.AssignmentID,
		StudentId:    internal.StudentID,
		Content:      internal.Content,
		SubmittedAt:  internal.SubmittedAt,
		Grade:        internal.Grade,
		Feedback:     internal.Feedback,
	}
}

func ToInternalUser(api openapi.User) model.User {
	return model.User{
		ID:        api.Id,
		Username:  api.Username,
		Email:     api.Email,
		FirstName: api.FirstName,
		LastName:  api.LastName,
		Role:      api.Role,
	}
}

func ToAPIUser(internal model.User) openapi.User {
	return openapi.User{
		Id:        internal.ID,
		Username:  internal.Username,
		Email:     internal.Email,
		FirstName: internal.FirstName,
		LastName:  internal.LastName,
		Role:      internal.Role,
	}
}

func ToInternalStudent(api openapi.Student) model.Student {
	return model.Student{
		Id:     api.Id,
		UserID: api.UserId,
	}
}

func ToAPIStudent(internal model.Student) openapi.Student {
	return openapi.Student{
		Id:     internal.Id,
		UserId: internal.UserID,
	}
}

func ToInternalStudentUpdate(api openapi.StudentUpdate) model.StudentUpdate {
	return model.StudentUpdate{
		Email:     api.Email,
		FirstName: api.FirstName,
		LastName:  api.LastName,
		Password:  api.Password,
	}
}

func ToInternalQuizSubmission(api openapi.QuizSubmission) model.QuizSubmission {
	return model.QuizSubmission{
		Id:      api.Id,
		QuizID:  api.QuizId,
		Answers: convertQuizAnswers(api.Answers),
	}
}

func ToAPIQuizSubmission(internal model.QuizSubmission) openapi.QuizSubmission {
	return openapi.QuizSubmission{
		Id:      internal.Id,
		QuizId:  internal.QuizID,
		Answers: convertAPIQuizAnswers(internal.Answers),
	}
}

func convertQuizAnswers(apiAnswers []openapi.QuizSubmissionAnswersInner) []model.QuizSubmissionAnswer {
	var answers []model.QuizSubmissionAnswer
	for _, apiAnswer := range apiAnswers {
		answers = append(answers, model.QuizSubmissionAnswer{
			QuestionID: apiAnswer.QuestionId,
			Answer:     apiAnswer.Answer,
		})
	}
	return answers
}

func convertAPIQuizAnswers(internalAnswers []model.QuizSubmissionAnswer) []openapi.QuizSubmissionAnswersInner {
	var answers []openapi.QuizSubmissionAnswersInner
	for _, internalAnswer := range internalAnswers {
		answers = append(answers, openapi.QuizSubmissionAnswersInner{
			QuestionId: internalAnswer.QuestionID,
			Answer:     internalAnswer.Answer,
		})
	}
	return answers
}

// Convert slices
func ToAPIAssignments(internals []model.Assignment) []openapi.Assignment {
	var apis []openapi.Assignment
	for _, internal := range internals {
		apis = append(apis, ToAPIAssignment(internal))
	}
	return apis
}

func ToAPIQuizzes(internals []model.Quiz) []openapi.Quiz {
	var apis []openapi.Quiz
	for _, internal := range internals {
		apis = append(apis, ToAPIQuiz(internal))
	}
	return apis
}

func ToAPIQuiz(internal model.Quiz) openapi.Quiz {
	return openapi.Quiz{
		Id:          internal.Id,
		Title:       internal.Title,
		Description: internal.Description,
		Questions:   convertAPIQuizQuestions(internal.Questions),
	}
}

func convertAPIQuizQuestions(internalQuestions []model.QuizQuestion) []openapi.QuizQuestion {
	var questions []openapi.QuizQuestion
	for _, internal := range internalQuestions {
		questions = append(questions, openapi.QuizQuestion{
			Id:       internal.Id,
			Question: internal.Question,
			Type:     internal.Type,
			Options:  internal.Options,
		})
	}
	return questions
}

func ToAPICourses(internals []model.Course) []openapi.Course {
	var apis []openapi.Course
	for _, internal := range internals {
		apis = append(apis, openapi.Course{
			Id:          internal.Id,
			Name:        internal.Name,
			Description: internal.Description,
		})
	}
	return apis
}

func ToAPICourseDetail(internal model.CourseDetail) openapi.CourseDetail {
	return openapi.CourseDetail{
		Id:          internal.Id,
		Name:        internal.Name,
		Description: internal.Description,
		Instructor:  internal.Instructor,
		Schedule:    internal.Schedule,
	}
}

func ToAPIMaterials(internals []model.Material) []openapi.Material {
	var apis []openapi.Material
	for _, internal := range internals {
		apis = append(apis, openapi.Material{
			Id:    internal.Id,
			Title: internal.Title,
			Type:  internal.Type,
			Url:   internal.Url,
		})
	}
	return apis
}

func ToAPIAnnouncements(internals []model.Announcement) []openapi.Announcement {
	var apis []openapi.Announcement
	for _, internal := range internals {
		apis = append(apis, openapi.Announcement{
			Id:      internal.Id,
			Title:   internal.Title,
			Content: internal.Content,
			Date:    internal.Date,
		})
	}
	return apis
}

func ToAPINotifications(internals []model.Notification) []openapi.Notification {
	var apis []openapi.Notification
	for _, internal := range internals {
		apis = append(apis, openapi.Notification{
			Id:      internal.Id,
			Message: internal.Message,
			Date:    internal.Date,
			Read:    internal.Read,
		})
	}
	return apis
}

func ToAPIDashboard(internal model.Dashboard) openapi.Dashboard {
	return openapi.Dashboard{
		RecentAnnouncements: ToAPIAnnouncements(internal.RecentAnnouncements),
		UpcomingAssignments: ToAPIAssignments(internal.UpcomingAssignments),
	}
}
