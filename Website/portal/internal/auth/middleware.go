package auth

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

// @Summary		Access user dashboard
// @Description	Returns personalized dashboard message. Requires valid JWT cookie.
// @Tags			dashboard
// @Produce		json
// @Success		200	{object}	map[string]string	"Welcome message"
// @Failure		401	{object}	map[string]string	"Missing or invalid token"
// @Router			/dashboard [get]
// @Security		ApiKeyAuth
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		var tokenStr string

		// Try to get token from Authorization header first
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader != "" && len(authHeader) > 7 && authHeader[:7] == "Bearer " {
			tokenStr = authHeader[7:]
		} else {
			// Fall back to cookie
			var err error
			tokenStr, err = c.<PERSON>("token")
			if err != nil {
				c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Missing auth token"})
				return
			}
		}

		token, err := ValidateToken(tokenStr)
		if err != nil || !token.Valid {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid or expired token"})
			return
		}

		claims, ok := token.Claims.(jwt.MapClaims)
		if !ok {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid token claims"})
			return
		}

		// Extract username from token (login server provides username, not id)
		usernameVal, usernameOk := claims["username"]
		if !usernameOk {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Token missing username"})
			return
		}
		username, ok := usernameVal.(string)
		if !ok {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Token username has invalid type"})
			return
		}

		// Set both username and a mock user ID for compatibility
		c.Set("username", username)
		c.Set("id", uint(1)) // Mock ID for now - in production, you'd look up the user ID from username

		c.Next()
	}
}

// GetUserIDFromContext extracts the authenticated user ID from Gin context
func GetUserIDFromContext(c *gin.Context) (uint, bool) {
	id, exists := c.Get("id")
	if !exists {
		return 0, false
	}
	idStr, ok := id.(uint)
	return idStr, ok
}
