package handlers

import (
	"net/http"
	"portal/internal/db"
	"portal/internal/model"

	"github.com/gin-gonic/gin"
)

// ===== USER CRUD =====

func CreateUser(c *gin.Context) {
	var user model.User
	if err := c.<PERSON>(&user); err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.CreateUser(&user); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
		return
	}

	c.<PERSON>(http.StatusCreated, gin.H{"id": user.ID, "message": "User created successfully"})
}

func GetUsers(c *gin.Context) {
	// Search functionality
	search := c.Query("search")
	role := c.Query("role")

	users, err := db.SearchUsers(search, role)
	if err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to fetch users"})
		return
	}

	c.<PERSON>(http.StatusOK, users)
}

func GetUser(c *gin.Context) {
	id := c.Param("id")
	user, err := db.GetUserByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	c.JSON(http.StatusOK, user)
}

func UpdateUser(c *gin.Context) {
	id := c.Param("id")
	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.UpdateUser(id, updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User updated successfully"})
}

func DeleteUser(c *gin.Context) {
	id := c.Param("id")
	if err := db.DeleteUser(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete user"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User deleted successfully"})
}

// ===== STUDENT CRUD =====

func CreateStudent(c *gin.Context) {
	var student model.Student
	if err := c.ShouldBindJSON(&student); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.CreateStudent(&student); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create student"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"id": student.Id, "message": "Student created successfully"})
}

func GetStudents(c *gin.Context) {
	search := c.Query("search")
	students, err := db.SearchStudents(search)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch students"})
		return
	}

	c.JSON(http.StatusOK, students)
}

func GetStudent(c *gin.Context) {
	id := c.Param("id")
	student, err := db.GetStudentByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Student not found"})
		return
	}

	c.JSON(http.StatusOK, student)
}

func UpdateStudent(c *gin.Context) {
	id := c.Param("id")
	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.UpdateStudentCRUD(id, updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update student"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Student updated successfully"})
}

func DeleteStudent(c *gin.Context) {
	id := c.Param("id")
	if err := db.DeleteStudent(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete student"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Student deleted successfully"})
}

// ===== COACH CRUD =====

func CreateCoach(c *gin.Context) {
	var coach model.Coach
	if err := c.ShouldBindJSON(&coach); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.CreateCoach(&coach); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create coach"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"id": coach.Id, "message": "Coach created successfully"})
}

func GetCoaches(c *gin.Context) {
	search := c.Query("search")
	coaches, err := db.SearchCoaches(search)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch coaches"})
		return
	}

	c.JSON(http.StatusOK, coaches)
}

func GetCoach(c *gin.Context) {
	id := c.Param("id")
	coach, err := db.GetCoachByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Coach not found"})
		return
	}

	c.JSON(http.StatusOK, coach)
}

func UpdateCoach(c *gin.Context) {
	id := c.Param("id")
	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.UpdateCoach(id, updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update coach"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Coach updated successfully"})
}

func DeleteCoach(c *gin.Context) {
	id := c.Param("id")
	if err := db.DeleteCoach(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete coach"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Coach deleted successfully"})
}

// ===== COURSE CRUD =====

func GetCourses(c *gin.Context) {
	search := c.Query("search")
	instructor := c.Query("instructor")

	courses, err := db.SearchCourses(search, instructor)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch courses"})
		return
	}

	c.JSON(http.StatusOK, courses)
}

func GetCourse(c *gin.Context) {
	id := c.Param("id")
	course, err := db.GetCourseByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Course not found"})
		return
	}

	c.JSON(http.StatusOK, course)
}

func CreateCourse(c *gin.Context) {
	var course model.Course
	if err := c.ShouldBindJSON(&course); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.CreateCourse(&course); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create course"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"id": course.Id, "message": "Course created successfully"})
}

func UpdateCourse(c *gin.Context) {
	id := c.Param("id")
	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.UpdateCourse(id, updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update course"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Course updated successfully"})
}

func DeleteCourse(c *gin.Context) {
	id := c.Param("id")
	if err := db.DeleteCourse(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete course"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Course deleted successfully"})
}

// ===== ASSIGNMENT CRUD =====

func CreateAssignment(c *gin.Context) {
	var assignment model.Assignment
	if err := c.ShouldBindJSON(&assignment); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.CreateAssignmentCRUD(&assignment); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create assignment"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"id": assignment.Id, "message": "Assignment created successfully"})
}

func GetAssignments(c *gin.Context) {
	search := c.Query("search")
	courseId := c.Query("courseId")
	status := c.Query("status")

	assignments, err := db.SearchAssignments(search, courseId, status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch assignments"})
		return
	}

	c.JSON(http.StatusOK, assignments)
}

func GetAssignment(c *gin.Context) {
	id := c.Param("id")
	assignment, err := db.GetAssignmentByID(id, "")
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Assignment not found"})
		return
	}

	c.JSON(http.StatusOK, assignment)
}

func UpdateAssignment(c *gin.Context) {
	id := c.Param("id")
	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.UpdateAssignmentCRUD(id, updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update assignment"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Assignment updated successfully"})
}

func DeleteAssignment(c *gin.Context) {
	id := c.Param("id")
	if err := db.DeleteAssignment(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete assignment"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Assignment deleted successfully"})
}

// ===== QUIZ CRUD =====

func CreateQuiz(c *gin.Context) {
	var quiz model.Quiz
	if err := c.ShouldBindJSON(&quiz); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.CreateQuizCRUD(&quiz); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create quiz"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"id": quiz.Id, "message": "Quiz created successfully"})
}

func GetQuizzes(c *gin.Context) {
	search := c.Query("search")
	courseId := c.Query("courseId")
	status := c.Query("status")

	quizzes, err := db.SearchQuizzes(search, courseId, status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch quizzes"})
		return
	}

	c.JSON(http.StatusOK, quizzes)
}

func GetQuiz(c *gin.Context) {
	id := c.Param("id")
	quiz, err := db.GetQuizByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz not found"})
		return
	}

	c.JSON(http.StatusOK, quiz)
}

func UpdateQuiz(c *gin.Context) {
	id := c.Param("id")
	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.UpdateQuizCRUD(id, updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update quiz"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Quiz updated successfully"})
}

func DeleteQuiz(c *gin.Context) {
	id := c.Param("id")
	if err := db.DeleteQuiz(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete quiz"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Quiz deleted successfully"})
}

// ===== ANNOUNCEMENT CRUD =====

func CreateAnnouncement(c *gin.Context) {
	var announcement model.Announcement
	if err := c.ShouldBindJSON(&announcement); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.CreateAnnouncement(&announcement); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create announcement"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"id": announcement.Id, "message": "Announcement created successfully"})
}

func GetAnnouncements(c *gin.Context) {
	search := c.Query("search")
	courseId := c.Query("courseId")

	announcements, err := db.SearchAnnouncements(search, courseId)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch announcements"})
		return
	}

	c.JSON(http.StatusOK, announcements)
}

func GetAnnouncement(c *gin.Context) {
	id := c.Param("id")
	announcement, err := db.GetAnnouncementByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Announcement not found"})
		return
	}

	c.JSON(http.StatusOK, announcement)
}

func UpdateAnnouncement(c *gin.Context) {
	id := c.Param("id")
	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.UpdateAnnouncement(id, updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update announcement"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Announcement updated successfully"})
}

func DeleteAnnouncement(c *gin.Context) {
	id := c.Param("id")
	if err := db.DeleteAnnouncement(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete announcement"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Announcement deleted successfully"})
}

// ===== NOTIFICATION CRUD =====

func CreateNotification(c *gin.Context) {
	var notification model.Notification
	if err := c.ShouldBindJSON(&notification); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.CreateNotification(&notification); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create notification"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"id": notification.Id, "message": "Notification created successfully"})
}

func GetNotifications(c *gin.Context) {
	search := c.Query("search")
	studentId := c.Query("studentId")

	notifications, err := db.SearchNotifications(search, studentId)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch notifications"})
		return
	}

	c.JSON(http.StatusOK, notifications)
}

func GetNotification(c *gin.Context) {
	id := c.Param("id")
	notification, err := db.GetNotificationByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Notification not found"})
		return
	}

	c.JSON(http.StatusOK, notification)
}

func UpdateNotification(c *gin.Context) {
	id := c.Param("id")
	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.UpdateNotification(id, updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update notification"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Notification updated successfully"})
}

func DeleteNotification(c *gin.Context) {
	id := c.Param("id")
	if err := db.DeleteNotification(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete notification"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Notification deleted successfully"})
}

// ===== SUBMISSION CRUD =====

func CreateSubmission(c *gin.Context) {
	var submission model.Submission
	if err := c.ShouldBindJSON(&submission); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.CreateSubmission(&submission); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create submission"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"id": submission.Id, "message": "Submission created successfully"})
}

func GetSubmissions(c *gin.Context) {
	search := c.Query("search")
	studentId := c.Query("studentId")
	assignmentId := c.Query("assignmentId")

	submissions, err := db.SearchSubmissions(search, studentId, assignmentId)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch submissions"})
		return
	}

	c.JSON(http.StatusOK, submissions)
}

func GetSubmission(c *gin.Context) {
	id := c.Param("id")
	submission, err := db.GetSubmissionByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Submission not found"})
		return
	}

	c.JSON(http.StatusOK, submission)
}

func UpdateSubmission(c *gin.Context) {
	id := c.Param("id")
	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.UpdateSubmission(id, updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update submission"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Submission updated successfully"})
}

func DeleteSubmission(c *gin.Context) {
	id := c.Param("id")
	if err := db.DeleteSubmission(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete submission"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Submission deleted successfully"})
}

// ===== MATERIAL CRUD =====

func CreateMaterial(c *gin.Context) {
	var material model.Material
	if err := c.ShouldBindJSON(&material); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.CreateMaterial(&material); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create material"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"id": material.ID, "message": "Material created successfully"})
}

func GetMaterials(c *gin.Context) {
	search := c.Query("search")
	courseId := c.Query("courseId")

	materials, err := db.SearchMaterials(search, courseId)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch materials"})
		return
	}

	c.JSON(http.StatusOK, materials)
}

func GetMaterial(c *gin.Context) {
	id := c.Param("id")
	material, err := db.GetMaterialByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Material not found"})
		return
	}

	c.JSON(http.StatusOK, material)
}

func UpdateMaterial(c *gin.Context) {
	id := c.Param("id")
	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.UpdateMaterial(id, updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update material"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Material updated successfully"})
}

func DeleteMaterial(c *gin.Context) {
	id := c.Param("id")
	if err := db.DeleteMaterial(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete material"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Material deleted successfully"})
}

// ===== ENROLLMENT CRUD =====

func CreateEnrollment(c *gin.Context) {
	var enrollment model.Enrollment
	if err := c.ShouldBindJSON(&enrollment); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.CreateEnrollment(&enrollment); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create enrollment"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"id": enrollment.ID, "message": "Enrollment created successfully"})
}

func GetEnrollments(c *gin.Context) {
	studentId := c.Query("studentId")
	courseId := c.Query("courseId")

	enrollments, err := db.SearchEnrollments(studentId, courseId)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch enrollments"})
		return
	}

	c.JSON(http.StatusOK, enrollments)
}

func GetEnrollment(c *gin.Context) {
	id := c.Param("id")
	enrollment, err := db.GetEnrollmentByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Enrollment not found"})
		return
	}

	c.JSON(http.StatusOK, enrollment)
}

func DeleteEnrollment(c *gin.Context) {
	id := c.Param("id")
	if err := db.DeleteEnrollment(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete enrollment"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Enrollment deleted successfully"})
}

// ===== GRADE CRUD =====

func CreateGrade(c *gin.Context) {
	var grade model.Grade
	if err := c.ShouldBindJSON(&grade); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.CreateGrade(&grade); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create grade"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"id": grade.ID, "message": "Grade created successfully"})
}

func GetGrades(c *gin.Context) {
	studentId := c.Query("studentId")
	courseId := c.Query("courseId")

	grades, err := db.SearchGrades(studentId, courseId)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch grades"})
		return
	}

	c.JSON(http.StatusOK, grades)
}

func GetGrade(c *gin.Context) {
	id := c.Param("id")
	grade, err := db.GetGradeByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Grade not found"})
		return
	}

	c.JSON(http.StatusOK, grade)
}

func UpdateGrade(c *gin.Context) {
	id := c.Param("id")
	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.UpdateGrade(id, updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update grade"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Grade updated successfully"})
}

func DeleteGrade(c *gin.Context) {
	id := c.Param("id")
	if err := db.DeleteGrade(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete grade"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Grade deleted successfully"})
}

// ===== ATTENDANCE CRUD =====

func CreateAttendance(c *gin.Context) {
	var attendance model.Attendance
	if err := c.ShouldBindJSON(&attendance); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.CreateAttendance(&attendance); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create attendance"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"id": attendance.ID, "message": "Attendance created successfully"})
}

func GetAttendances(c *gin.Context) {
	studentId := c.Query("studentId")
	courseId := c.Query("courseId")
	date := c.Query("date")

	attendances, err := db.SearchAttendances(studentId, courseId, date)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch attendances"})
		return
	}

	c.JSON(http.StatusOK, attendances)
}

func GetAttendance(c *gin.Context) {
	id := c.Param("id")
	attendance, err := db.GetAttendanceByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Attendance not found"})
		return
	}

	c.JSON(http.StatusOK, attendance)
}

func UpdateAttendance(c *gin.Context) {
	id := c.Param("id")
	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := db.UpdateAttendance(id, updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update attendance"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Attendance updated successfully"})
}

func DeleteAttendance(c *gin.Context) {
	id := c.Param("id")
	if err := db.DeleteAttendance(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete attendance"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Attendance deleted successfully"})
}
