package handlers

import (
	"portal/internal/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// HandleCreateCourse handles course creation requests
func HandleCreateCourse(c *gin.Context, db *gorm.DB) {
	var courseData struct {
		Name        string `json:"name" binding:"required"`
		Description string `json:"description"`
		Instructor  string `json:"instructor"`
		Schedule    string `json:"schedule"`
	}

	if err := c.ShouldBindJSON(&courseData); err != nil {
		c.JSON(400, gin.H{"error": "Invalid input: " + err.Error()})
		return
	}

	// Create course record
	course := model.Course{
		Id:          "", // Will be auto-generated by database
		Name:        courseData.Name,
		Description: courseData.Description,
		Instructor:  courseData.Instructor,
	}

	if err := db.Create(&course).Error; err != nil {
		c.JSON(500, gin.H{"error": "Failed to create course"})
		return
	}

	c.<PERSON><PERSON>(201, gin.H{
		"id":          course.Id, // Use the generated ID
		"name":        courseData.Name,
		"description": courseData.Description,
		"message":     "Course created successfully",
	})
}

// HandleUpdateCourse handles course update requests
func HandleUpdateCourse(c *gin.Context, db *gorm.DB) {
	courseID := c.Param("courseId")
	if courseID == "" {
		c.JSON(400, gin.H{"error": "Course ID is required"})
		return
	}

	var updateData struct {
		Name        *string `json:"name,omitempty"`
		Description *string `json:"description,omitempty"`
	}

	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(400, gin.H{"error": "Invalid input: " + err.Error()})
		return
	}

	// Check if course exists
	var course model.Course
	if err := db.First(&course, "id = ?", courseID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(404, gin.H{"error": "Course not found"})
		} else {
			c.JSON(500, gin.H{"error": "Database error"})
		}
		return
	}

	// Update fields if provided
	updates := make(map[string]interface{})
	if updateData.Name != nil {
		updates["name"] = *updateData.Name
	}
	if updateData.Description != nil {
		updates["description"] = *updateData.Description
	}

	if len(updates) == 0 {
		c.JSON(400, gin.H{"error": "No fields to update"})
		return
	}

	if err := db.Model(&course).Updates(updates).Error; err != nil {
		c.JSON(500, gin.H{"error": "Failed to update course"})
		return
	}

	c.JSON(200, gin.H{"message": "Course updated successfully"})
}

// HandleDeleteCourse handles course deletion requests
func HandleDeleteCourse(c *gin.Context, db *gorm.DB) {
	courseID := c.Param("courseId")
	if courseID == "" {
		c.JSON(400, gin.H{"error": "Course ID is required"})
		return
	}

	// Check if course exists
	var course model.Course
	if err := db.First(&course, "id = ?", courseID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(404, gin.H{"error": "Course not found"})
		} else {
			c.JSON(500, gin.H{"error": "Database error"})
		}
		return
	}

	// TODO: Check if user has permission to delete this course
	// For now, allow any authenticated user

	// Delete course (this will cascade to related records if properly configured)
	if err := db.Delete(&course).Error; err != nil {
		c.JSON(500, gin.H{"error": "Failed to delete course"})
		return
	}

	c.JSON(200, gin.H{"message": "Course deleted successfully"})
}
