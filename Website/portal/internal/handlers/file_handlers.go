package handlers

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"

	"portal/internal/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// HandleFileUpload handles file upload requests
func HandleFileUpload(c *gin.Context, db *gorm.DB) {
	// Parse multipart form
	err := c.Request.ParseMultipartForm(10 << 20) // 10 MB max
	if err != nil {
		c.JSON(400, gin.H{"error": "File too large or invalid form"})
		return
	}

	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(400, gin.H{"error": "No file provided"})
		return
	}
	defer file.Close()

	// Validate file type
	allowedTypes := map[string]bool{
		"image/jpeg":      true,
		"image/png":       true,
		"image/gif":       true,
		"application/pdf": true,
		"text/plain":      true,
	}

	contentType := header.Header.Get("Content-Type")
	if contentType == "" {
		// Try to detect from file extension
		ext := filepath.Ext(header.Filename)
		switch ext {
		case ".jpg", ".jpeg":
			contentType = "image/jpeg"
		case ".png":
			contentType = "image/png"
		case ".gif":
			contentType = "image/gif"
		case ".pdf":
			contentType = "application/pdf"
		case ".txt":
			contentType = "text/plain"
		default:
			c.JSON(400, gin.H{"error": "File type not allowed"})
			return
		}
	}

	if !allowedTypes[contentType] {
		c.JSON(400, gin.H{"error": "File type not allowed"})
		return
	}

	// Validate file size (10MB max)
	if header.Size > 10<<20 {
		c.JSON(400, gin.H{"error": "File too large (max 10MB)"})
		return
	}

	// Generate unique filename for storage
	tempID := fmt.Sprintf("%d", time.Now().UnixNano())
	ext := filepath.Ext(header.Filename)
	storedName := tempID + ext

	// Create upload directory if it doesn't exist
	uploadDir := "uploads"
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		c.JSON(500, gin.H{"error": "Failed to create upload directory"})
		return
	}

	// Save file to disk
	filePath := filepath.Join(uploadDir, storedName)
	dst, err := os.Create(filePath)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to create file"})
		return
	}
	defer dst.Close()

	if _, err := io.Copy(dst, file); err != nil {
		c.JSON(500, gin.H{"error": "Failed to save file"})
		return
	}

	// Save file metadata to database
	fileRecord := model.File{
		ID:         "", // Will be auto-generated by database
		FileName:   header.Filename,
		FilePath:   filePath,
		FileSize:   header.Size,
		UploadedAt: time.Now(),
	}

	if err := db.Create(&fileRecord).Error; err != nil {
		// Clean up file if database save fails
		os.Remove(filePath)
		c.JSON(500, gin.H{"error": "Failed to save file metadata"})
		return
	}

	c.JSON(201, gin.H{
		"id":       fileRecord.ID, // Use the generated ID
		"filename": header.Filename,
		"size":     header.Size,
		"message":  "File uploaded successfully",
	})
}

// HandleFileDownload handles file download requests
func HandleFileDownload(c *gin.Context, db *gorm.DB) {
	fileID := c.Param("fileId")
	if fileID == "" {
		c.JSON(400, gin.H{"error": "File ID is required"})
		return
	}

	var fileRecord model.File
	if err := db.First(&fileRecord, "id = ?", fileID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(404, gin.H{"error": "File not found"})
		} else {
			c.JSON(500, gin.H{"error": "Database error"})
		}
		return
	}

	// Check if file exists on disk
	if _, err := os.Stat(fileRecord.FilePath); os.IsNotExist(err) {
		c.JSON(404, gin.H{"error": "File not found on disk"})
		return
	}

	// Set headers for file download
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileRecord.FileName))
	c.Header("Content-Type", "application/octet-stream")
	c.Header("Content-Length", fmt.Sprintf("%d", fileRecord.FileSize))

	// Serve the file
	c.File(fileRecord.FilePath)
}

// HandleFileInfo handles file info requests
func HandleFileInfo(c *gin.Context, db *gorm.DB) {
	fileID := c.Param("fileId")
	if fileID == "" {
		c.JSON(400, gin.H{"error": "File ID is required"})
		return
	}

	var fileRecord model.File
	if err := db.First(&fileRecord, "id = ?", fileID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(404, gin.H{"error": "File not found"})
		} else {
			c.JSON(500, gin.H{"error": "Database error"})
		}
		return
	}

	c.JSON(200, fileRecord)
}

// HandleFileDelete handles file deletion requests
func HandleFileDelete(c *gin.Context, db *gorm.DB) {
	fileID := c.Param("fileId")
	if fileID == "" {
		c.JSON(400, gin.H{"error": "File ID is required"})
		return
	}

	var fileRecord model.File
	if err := db.First(&fileRecord, "id = ?", fileID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(404, gin.H{"error": "File not found"})
		} else {
			c.JSON(500, gin.H{"error": "Database error"})
		}
		return
	}

	// Delete file from disk
	if err := os.Remove(fileRecord.FilePath); err != nil && !os.IsNotExist(err) {
		c.JSON(500, gin.H{"error": "Failed to delete file from disk"})
		return
	}

	// Delete file record from database
	if err := db.Delete(&fileRecord).Error; err != nil {
		c.JSON(500, gin.H{"error": "Failed to delete file record"})
		return
	}

	c.JSON(200, gin.H{"message": "File deleted successfully"})
}
