package db

import (
	"portal/internal/model"
	"portal/internal/persistence"

	"gorm.io/gorm"
)

var database *gorm.DB

// SetDB sets the global database instance
func SetDB(db *gorm.DB) {
	database = db
}

// GetDB returns the global database instance
func GetDB() *gorm.DB {
	return database
}

// Assignment functions
func GetAssignmentByID(assignmentID, courseID string) (model.Assignment, error) {
	return persistence.GetAssignmentByID(database, assignmentID)
}

// Removed - using new CRUD version below

// Removed - using new CRUD version below

func ListAssignmentsByCourse(courseID string) ([]model.Assignment, error) {
	return persistence.ListAssignmentsByCourse(database, courseID)
}

// Removed - using new CRUD versions below

// Quiz functions
func ListQuizzesByCourse(courseID string) ([]model.Quiz, error) {
	return persistence.ListQuizzesByCourse(database, courseID)
}

func GetQuizByID(quizID string) (model.Quiz, error) {
	return persistence.GetQuizByID(database, quizID)
}

// Removed CreateQuiz and UpdateQuiz - using new CRUD versions below

func CreateQuizSubmission(submission *model.QuizSubmission) error {
	return persistence.CreateQuizSubmission(database, submission)
}

// Course functions
func ListCourses() ([]model.Course, error) {
	return persistence.ListCourses(database)
}

func GetCourseByID(courseID string) (model.CourseDetail, error) {
	return persistence.GetCourseByID(database, courseID)
}

func CreateCourse(course *model.Course) error {
	return persistence.CreateCourse(database, course)
}

func ListMaterialsByCourse(courseID string) ([]model.Material, error) {
	return persistence.ListMaterialsByCourse(database, courseID)
}

// Announcement functions
func ListAnnouncementsForStudent(studentID string) ([]model.Announcement, error) {
	return persistence.ListAnnouncementsForStudent(database, studentID)
}

// Notification functions
func ListNotifications(studentID string) ([]model.Notification, error) {
	return persistence.ListNotifications(database, studentID)
}

// Enrollment functions
func StudentEnrolledInCourse(studentID, courseID string) bool {
	return persistence.StudentEnrolledInCourse(studentID, courseID)
}

func CreateEnrollmentLegacy(studentID, courseID string) (model.Enrollment, error) {
	enrollment := model.Enrollment{
		StudentID: studentID,
		CourseID:  courseID,
	}
	err := persistence.CreateEnrollment(database, &enrollment)
	return enrollment, err
}

func DeleteEnrollmentByStudentAndCourse(studentID, courseID string) error {
	return persistence.DeleteEnrollmentByStudentAndCourse(database, studentID, courseID)
}

// Student functions
func GetStudentByID(studentID string) (model.Student, error) {
	return persistence.GetStudentByID(database, studentID)
}

// Removed - using new CRUD version below

// User functions
func GetUserByID(userID string) (model.User, error) {
	return persistence.GetUserByID(database, userID)
}

// Dashboard functions
func GetDashboardData(studentID string) (model.Dashboard, error) {
	return persistence.GetDashboardData(database, studentID)
}

// ===== USER CRUD =====
func CreateUser(user *model.User) error {
	return persistence.CreateUser(database, user)
}

func SearchUsers(search, role string) ([]model.User, error) {
	return persistence.SearchUsers(database, search, role)
}

func UpdateUser(id string, updates map[string]interface{}) error {
	return persistence.UpdateUser(database, id, updates)
}

func DeleteUser(id string) error {
	return persistence.DeleteUser(database, id)
}

// ===== STUDENT CRUD =====
func CreateStudent(student *model.Student) error {
	return persistence.CreateStudent(database, student)
}

func SearchStudents(search string) ([]model.Student, error) {
	return persistence.SearchStudents(database, search)
}

func UpdateStudentCRUD(id string, updates map[string]interface{}) error {
	return persistence.UpdateStudent(database, id, updates)
}

func DeleteStudent(id string) error {
	return persistence.DeleteStudent(database, id)
}

// ===== COACH CRUD =====
func CreateCoach(coach *model.Coach) error {
	return persistence.CreateCoach(database, coach)
}

func SearchCoaches(search string) ([]model.Coach, error) {
	return persistence.SearchCoaches(database, search)
}

func GetCoachByID(id string) (model.Coach, error) {
	return persistence.GetCoachByID(database, id)
}

func UpdateCoach(id string, updates map[string]interface{}) error {
	return persistence.UpdateCoach(database, id, updates)
}

func DeleteCoach(id string) error {
	return persistence.DeleteCoach(database, id)
}

// ===== COURSE CRUD =====
func SearchCourses(search, instructor string) ([]model.Course, error) {
	return persistence.SearchCourses(database, search, instructor)
}

func UpdateCourse(id string, updates map[string]interface{}) error {
	return persistence.UpdateCourse(database, id, updates)
}

func DeleteCourse(id string) error {
	return persistence.DeleteCourse(database, id)
}

// ===== ASSIGNMENT CRUD =====
func CreateAssignmentCRUD(assignment *model.Assignment) error {
	return persistence.CreateAssignment(database, assignment)
}

func SearchAssignments(search, courseId, status string) ([]model.Assignment, error) {
	return persistence.SearchAssignments(database, search, courseId, status)
}

func UpdateAssignmentCRUD(id string, updates map[string]interface{}) error {
	return persistence.UpdateAssignment(database, id, updates)
}

func DeleteAssignment(id string) error {
	return persistence.DeleteAssignment(database, id)
}

// ===== QUIZ CRUD =====
func CreateQuizCRUD(quiz *model.Quiz) error {
	return persistence.CreateQuiz(database, quiz)
}

func SearchQuizzes(search, courseId, status string) ([]model.Quiz, error) {
	return persistence.SearchQuizzes(database, search, courseId, status)
}

func UpdateQuizCRUD(id string, updates map[string]interface{}) error {
	return persistence.UpdateQuiz(database, id, updates)
}

func DeleteQuiz(id string) error {
	return persistence.DeleteQuiz(database, id)
}

// ===== ANNOUNCEMENT CRUD =====
func CreateAnnouncement(announcement *model.Announcement) error {
	return persistence.CreateAnnouncement(database, announcement)
}

func SearchAnnouncements(search, courseId string) ([]model.Announcement, error) {
	return persistence.SearchAnnouncements(database, search, courseId)
}

func GetAnnouncementByID(id string) (model.Announcement, error) {
	return persistence.GetAnnouncementByID(database, id)
}

func UpdateAnnouncement(id string, updates map[string]interface{}) error {
	return persistence.UpdateAnnouncement(database, id, updates)
}

func DeleteAnnouncement(id string) error {
	return persistence.DeleteAnnouncement(database, id)
}

// ===== NOTIFICATION CRUD =====
func CreateNotification(notification *model.Notification) error {
	return persistence.CreateNotification(database, notification)
}

func SearchNotifications(search, studentId string) ([]model.Notification, error) {
	return persistence.SearchNotifications(database, search, studentId)
}

func GetNotificationByID(id string) (model.Notification, error) {
	return persistence.GetNotificationByID(database, id)
}

func UpdateNotification(id string, updates map[string]interface{}) error {
	return persistence.UpdateNotification(database, id, updates)
}

func DeleteNotification(id string) error {
	return persistence.DeleteNotification(database, id)
}

// ===== LEGACY API COMPATIBILITY WRAPPERS =====

// UpdateAssignment wrapper for legacy API - converts AssignmentUpdate to map
func UpdateAssignment(id string, update *model.AssignmentUpdate) error {
	return persistence.UpdateAssignmentWithFiles(database, id, update)
}

// CreateAssignment wrapper for legacy API - converts AssignmentCreate to Assignment
func CreateAssignment(assignment *model.AssignmentCreate) error {
	return persistence.CreateAssignmentWithFiles(database, assignment)
}

// CreateQuiz wrapper for legacy API - converts QuizCreate to Quiz
func CreateQuiz(quiz *model.QuizCreate) error {
	return persistence.CreateQuizWithFiles(database, quiz)
}

// UpdateQuiz wrapper for legacy API - converts QuizUpdate to map
func UpdateQuiz(id string, update *model.QuizUpdate) error {
	return persistence.UpdateQuizWithFiles(database, id, update)
}

// UpdateStudent wrapper for legacy API - converts StudentUpdate to map
func UpdateStudent(studentID string, update *model.StudentUpdate) error {
	updateMap := make(map[string]interface{})
	if update.Email != "" {
		updateMap["email"] = update.Email
	}
	if update.FirstName != "" {
		updateMap["first_name"] = update.FirstName
	}
	if update.LastName != "" {
		updateMap["last_name"] = update.LastName
	}
	return persistence.UpdateStudent(database, studentID, updateMap)
}

// ===== SUBMISSION CRUD =====
func CreateSubmission(submission *model.Submission) error {
	return persistence.CreateSubmission(database, submission)
}

func SearchSubmissions(search, studentId, assignmentId string) ([]model.Submission, error) {
	return persistence.SearchSubmissions(database, search, studentId, assignmentId)
}

func GetSubmissionByID(id string) (model.Submission, error) {
	return persistence.GetSubmissionByID(database, id)
}

func UpdateSubmission(id string, updates map[string]interface{}) error {
	return persistence.UpdateSubmission(database, id, updates)
}

func DeleteSubmission(id string) error {
	return persistence.DeleteSubmission(database, id)
}

// ===== MATERIAL CRUD =====
func CreateMaterial(material *model.Material) error {
	return persistence.CreateMaterial(database, material)
}

func SearchMaterials(search, courseId string) ([]model.Material, error) {
	return persistence.SearchMaterials(database, search, courseId)
}

func GetMaterialByID(id string) (model.Material, error) {
	return persistence.GetMaterialByID(database, id)
}

func UpdateMaterial(id string, updates map[string]interface{}) error {
	return persistence.UpdateMaterial(database, id, updates)
}

func DeleteMaterial(id string) error {
	return persistence.DeleteMaterial(database, id)
}

// ===== ENROLLMENT CRUD =====
func CreateEnrollment(enrollment *model.Enrollment) error {
	return persistence.CreateEnrollment(database, enrollment)
}

func SearchEnrollments(studentId, courseId string) ([]model.Enrollment, error) {
	return persistence.SearchEnrollments(database, studentId, courseId)
}

func GetEnrollmentByID(id string) (model.Enrollment, error) {
	return persistence.GetEnrollmentByID(database, id)
}

func DeleteEnrollment(id string) error {
	return persistence.DeleteEnrollment(database, id)
}

// ===== GRADE CRUD =====
func CreateGrade(grade *model.Grade) error {
	return persistence.CreateGrade(database, grade)
}

func SearchGrades(studentId, courseId string) ([]model.Grade, error) {
	return persistence.SearchGrades(database, studentId, courseId)
}

func GetGradeByID(id string) (model.Grade, error) {
	return persistence.GetGradeByID(database, id)
}

func UpdateGrade(id string, updates map[string]interface{}) error {
	return persistence.UpdateGrade(database, id, updates)
}

func DeleteGrade(id string) error {
	return persistence.DeleteGrade(database, id)
}

// ===== ATTENDANCE CRUD =====
func CreateAttendance(attendance *model.Attendance) error {
	return persistence.CreateAttendance(database, attendance)
}

func SearchAttendances(studentId, courseId, date string) ([]model.Attendance, error) {
	return persistence.SearchAttendances(database, studentId, courseId, date)
}

func GetAttendanceByID(id string) (model.Attendance, error) {
	return persistence.GetAttendanceByID(database, id)
}

func UpdateAttendance(id string, updates map[string]interface{}) error {
	return persistence.UpdateAttendance(database, id, updates)
}

func DeleteAttendance(id string) error {
	return persistence.DeleteAttendance(database, id)
}
