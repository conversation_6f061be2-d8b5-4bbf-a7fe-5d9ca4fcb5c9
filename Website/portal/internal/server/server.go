package server

import (
	"log"
	"os"

	"portal/internal/db"
	"portal/internal/model"
	"portal/internal/persistence"
	"portal/internal/routes"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Server represents the HTTP server
type Server struct {
	router *gin.Engine
	port   string
}

// New creates a new server instance
func New() *Server {
	// Initialize database
	dsn := getEnvOrDefault("DATABASE_URL", "host=localhost user=postgres password=P5269874 dbname=new port=5432 sslmode=disable")
	database := persistence.InitDB(dsn)
	db.SetDB(database)
	log.Printf("Database connected")

	// Add sample data for testing
	addSampleData(database)

	// Get port from environment
	port := getEnvOrDefault("PORT", "8081")
	log.Printf("Server starting on port %s", port)

	// Create router with all routes
	router := routes.SetupRoutes(database)

	return &Server{
		router: router,
		port:   port,
	}
}

// Start starts the HTTP server
func (s *Server) Start() error {
	log.Printf("Server starting on port %s", s.port)
	return s.router.Run(":" + s.port)
}

// getEnvOrDefault returns environment variable value or default
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// addSampleData adds sample data to the database for testing
func addSampleData(database *gorm.DB) {
	// Add sample courses using GORM models (let database generate UUIDs)
	courses := []model.Course{
		{Name: "Introduction to Programming", Description: "Learn the basics of programming"},
		{Name: "Web Development", Description: "Build modern web applications"},
		{Name: "Data Science", Description: "Analyze data and build models"},
	}

	for _, course := range courses {
		// Check if course with this name already exists
		var existingCourse model.Course
		if err := database.Where("name = ?", course.Name).First(&existingCourse).Error; err == gorm.ErrRecordNotFound {
			// Course doesn't exist, create it
			if err := database.Create(&course).Error; err != nil {
				log.Printf("Error creating course %s: %v", course.Name, err)
			} else {
				log.Printf("Created course: %s (ID: %s)", course.Name, course.Id)
			}
		}
	}

	log.Printf("Sample data added")
}
