package persistence

import (
	"portal/internal/model"

	"gorm.io/gorm"
)

// CreateGrade creates a new grade record
func CreateGrade(db *gorm.DB, grade *model.Grade) error {
	return db.Create(grade).Error
}

// GetGradeByID retrieves a grade by its ID
func GetGradeByID(db *gorm.DB, id string) (model.Grade, error) {
	var grade model.Grade
	err := db.First(&grade, "id = ?", id).Error
	return grade, err
}

// SearchGrades searches for grades by student or course
func SearchGrades(db *gorm.DB, studentId, courseId string) ([]model.Grade, error) {
	var grades []model.Grade
	query := db.Model(&model.Grade{})
	
	if studentId != "" {
		query = query.Where("student_id = ?", studentId)
	}
	
	if courseId != "" {
		query = query.Where("course_id = ?", courseId)
	}
	
	err := query.Find(&grades).Error
	return grades, err
}

// UpdateGrade updates grade fields by ID
func UpdateGrade(db *gorm.DB, id string, updates map[string]interface{}) error {
	return db.Model(&model.Grade{}).Where("id = ?", id).Updates(updates).Error
}

// DeleteGrade deletes a grade by its ID
func DeleteGrade(db *gorm.DB, id string) error {
	return db.Delete(&model.Grade{}, "id = ?", id).Error
}

// ListGradesByStudent returns all grades for a student
func ListGradesByStudent(db *gorm.DB, studentID string) ([]model.Grade, error) {
	var grades []model.Grade
	err := db.Where("student_id = ?", studentID).Find(&grades).Error
	return grades, err
}

// ListGradesByCourse returns all grades for a course
func ListGradesByCourse(db *gorm.DB, courseID string) ([]model.Grade, error) {
	var grades []model.Grade
	err := db.Where("course_id = ?", courseID).Find(&grades).Error
	return grades, err
}
