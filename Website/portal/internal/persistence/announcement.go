package persistence

import (
	"portal/internal/model"
	"time"

	"gorm.io/gorm"
)

// --- Announcement CRUD ---
// ListAnnouncements retrieves all announcements, preloading files.
func ListAnnouncements(db *gorm.DB) ([]model.Announcement, error) {
	var announcements []model.Announcement
	err := db.Preload("Files.File").Find(&announcements).Error
	return announcements, err
}

// GetAnnouncementByID retrieves an announcement by its ID, preloading files.
func GetAnnouncementByID(db *gorm.DB, id string) (model.Announcement, error) {
	var announcement model.Announcement
	err := db.Preload("Files.File").Where("id = ?", id).First(&announcement).Error
	return announcement, err
}

// CreateAnnouncementFiles creates AnnouncementFile records and their File records for an announcement.
func CreateAnnouncementFiles(db *gorm.DB, announcementID string, files []model.AnnouncementFile) error {
	for _, af := range files {
		if af.File.ID == "" {
			err := CreateFile(db, &af.File)
			if err != nil {
				return err
			}
			af.FileID = af.File.ID
		}
		af.AnnouncementID = announcementID
		err := db.Create(&af).Error
		if err != nil {
			return err
		}
	}
	return nil
}

// DeleteAnnouncementFiles deletes all AnnouncementFile records for an announcement.
func DeleteAnnouncementFiles(db *gorm.DB, announcementID string) error {
	return db.Where("announcement_id = ?", announcementID).Delete(&model.AnnouncementFile{}).Error
}

// CreateAnnouncementWithFiles creates a new announcement and its associated files.
func CreateAnnouncementWithFiles(db *gorm.DB, input *model.Announcement) error {
	input.CreatedAt = time.Now()
	input.UpdatedAt = nil
	err := db.Create(input).Error
	if err != nil {
		return err
	}
	if len(input.Files) > 0 {
		if err := CreateAnnouncementFiles(db, input.Id, input.Files); err != nil {
			return err
		}
	}
	return nil
}

// UpdateAnnouncementWithFiles updates an existing announcement and its files.
func UpdateAnnouncementWithFiles(db *gorm.DB, id string, input *model.Announcement) error {
	now := time.Now()
	input.UpdatedAt = &now
	err := db.Model(&model.Announcement{}).Where("id = ?", id).Updates(input).Error
	if err != nil {
		return err
	}
	if input.Files != nil {
		if err := DeleteAnnouncementFiles(db, id); err != nil {
			return err
		}
		if err := CreateAnnouncementFiles(db, id, input.Files); err != nil {
			return err
		}
	}
	return nil
}

// DeleteAnnouncement deletes an announcement and its associated files.
func DeleteAnnouncement(db *gorm.DB, id string) error {
	if err := DeleteAnnouncementFiles(db, id); err != nil {
		return err
	}
	return db.Delete(&model.Announcement{}, "id = ?", id).Error
}

// ListAnnouncementsByStudent returns all announcements for courses a student is enrolled in.
func ListAnnouncementsByStudent(db *gorm.DB, studentID string) ([]model.Announcement, error) {
	var announcements []model.Announcement
	err := db.Table("announcements").
		Select("announcements.*").
		Joins("join enrollments on enrollments.course_id = announcements.course_id").
		Where("enrollments.student_id = ?", studentID).
		Preload("Files.File").
		Find(&announcements).Error
	return announcements, err
}

// SearchAnnouncements searches for announcements by title, content, or course
func SearchAnnouncements(db *gorm.DB, search, courseId string) ([]model.Announcement, error) {
	var announcements []model.Announcement
	query := db.Model(&model.Announcement{}).Preload("Files.File")

	if search != "" {
		query = query.Where("title ILIKE ? OR content ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	if courseId != "" {
		query = query.Where("course_id = ?", courseId)
	}

	err := query.Find(&announcements).Error
	return announcements, err
}

// CreateAnnouncement overload for CRUD - accepts model.Announcement directly
func CreateAnnouncement(db *gorm.DB, announcement *model.Announcement) error {
	return db.Create(announcement).Error
}

// UpdateAnnouncement overload for CRUD - accepts map[string]interface{}
func UpdateAnnouncement(db *gorm.DB, id string, updates map[string]interface{}) error {
	return db.Model(&model.Announcement{}).Where("id = ?", id).Updates(updates).Error
}
