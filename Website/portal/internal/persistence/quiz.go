package persistence

import (
	"portal/internal/model"
	"time"

	"gorm.io/gorm"
)

// --- Quiz CRUD ---
func ListQuizzes(db *gorm.DB) ([]model.Quiz, error) {
	var quizzes []model.Quiz
	err := db.Preload("Files.File").Find(&quizzes).Error
	return quizzes, err
}

func GetQuizByID(db *gorm.DB, id string) (model.Quiz, error) {
	var quiz model.Quiz
	err := db.Preload("Files.File").First(&quiz, "id = ?", id).Error
	return quiz, err
}

// CreateQuizFiles creates QuizFile records and their File records for a quiz.
func CreateQuizFiles(db *gorm.DB, quizID string, files []model.QuizFile) error {
	for _, qf := range files {
		if qf.File.ID == "" {
			err := CreateFile(db, &qf.File)
			if err != nil {
				return err
			}
			qf.FileID = qf.File.ID
		}
		qf.QuizID = quizID
		err := db.Create(&qf).Error
		if err != nil {
			return err
		}
	}
	return nil
}

// DeleteQuizFiles deletes all QuizFile records for a quiz.
func DeleteQuizFiles(db *gorm.DB, quizID string) error {
	return db.Where("quiz_id = ?", quizID).Delete(&model.QuizFile{}).Error
}

func CreateQuizWithFiles(db *gorm.DB, input *model.QuizCreate) error {
	quiz := model.Quiz{
		Title:       input.Title,
		Description: input.Description,
		Questions:   input.Questions,
		DueDate:     input.DueDate,
		CourseID:    input.CourseID,
		CreatedAt:   time.Now(),
		UpdatedAt:   nil,
	}
	err := db.Create(&quiz).Error
	if err != nil {
		return err
	}
	if len(input.Files) > 0 {
		if err := CreateQuizFiles(db, quiz.Id, input.Files); err != nil {
			return err
		}
	}
	return nil
}

func UpdateQuizWithFiles(db *gorm.DB, id string, input *model.QuizUpdate) error {
	update := map[string]interface{}{}
	if input.Title != nil {
		update["title"] = *input.Title
	}
	if input.Description != nil {
		update["description"] = *input.Description
	}
	if input.Questions != nil {
		update["questions"] = *input.Questions
	}
	if input.DueDate != nil {
		update["due_date"] = *input.DueDate
	}
	if input.Status != nil {
		update["status"] = *input.Status
	}
	if input.CourseID != nil {
		update["course_id"] = *input.CourseID
	}
	update["updated_at"] = time.Now()
	err := db.Model(&model.Quiz{}).Where("id = ?", id).Updates(update).Error
	if err != nil {
		return err
	}
	if input.Files != nil {
		if err := DeleteQuizFiles(db, id); err != nil {
			return err
		}
		if err := CreateQuizFiles(db, id, input.Files); err != nil {
			return err
		}
	}
	return nil
}

func DeleteQuizWithFiles(db *gorm.DB, id string) error {
	if err := DeleteQuizFiles(db, id); err != nil {
		return err
	}
	return db.Delete(&model.Quiz{}, "id = ?", id).Error
}

// CreateQuizSubmission creates a new quiz submission
func CreateQuizSubmission(db *gorm.DB, submission *model.QuizSubmission) error {
	return db.Create(submission).Error
}

// List quizzes for a course
func ListQuizzesByCourse(db *gorm.DB, courseId string) ([]model.Quiz, error) {
	var quizzes []model.Quiz
	err := db.Preload("Files.File").Where("course_id = ?", courseId).Find(&quizzes).Error
	return quizzes, err
}

// List quizzes for a student's courses
func ListQuizzesForStudent(db *gorm.DB, studentID string) ([]model.Quiz, error) {
	var quizzes []model.Quiz
	var courseIDs []string
	err := db.Table("enrollments").Where("student_id = ?", studentID).Pluck("course_id", &courseIDs).Error
	if err != nil {
		return nil, err
	}
	err = db.Preload("Files.File").Where("course_id IN ?", courseIDs).Find(&quizzes).Error
	return quizzes, err
}

// SearchQuizzes searches for quizzes by title, description, course, or status
func SearchQuizzes(db *gorm.DB, search, courseId, status string) ([]model.Quiz, error) {
	var quizzes []model.Quiz
	query := db.Model(&model.Quiz{}).Preload("Files.File")

	if search != "" {
		query = query.Where("title ILIKE ? OR description ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	if courseId != "" {
		query = query.Where("course_id = ?", courseId)
	}

	if status != "" {
		query = query.Where("status = ?", status)
	}

	err := query.Find(&quizzes).Error
	return quizzes, err
}

// CreateQuiz overload for CRUD - accepts model.Quiz directly
func CreateQuiz(db *gorm.DB, quiz *model.Quiz) error {
	return db.Create(quiz).Error
}

// UpdateQuiz overload for CRUD - accepts map[string]interface{}
func UpdateQuiz(db *gorm.DB, id string, updates map[string]interface{}) error {
	return db.Model(&model.Quiz{}).Where("id = ?", id).Updates(updates).Error
}

// DeleteQuiz deletes a quiz and its associated files
func DeleteQuiz(db *gorm.DB, id string) error {
	if err := DeleteQuizFiles(db, id); err != nil {
		return err
	}
	return db.Delete(&model.Quiz{}, "id = ?", id).Error
}
