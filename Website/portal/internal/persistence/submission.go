package persistence

import (
	"portal/internal/model"
	"time"

	"gorm.io/gorm"
)

// --- Submission CRUD ---

// ListSubmissions retrieves all submissions, preloading files.
func ListSubmissions(db *gorm.DB) ([]model.Submission, error) {
	var submissions []model.Submission
	err := db.Preload("Files.File").Find(&submissions).Error
	return submissions, err
}

// GetSubmissionByID retrieves a submission by its ID, preloading files.
func GetSubmissionByID(db *gorm.DB, id string) (model.Submission, error) {
	var submission model.Submission
	err := db.Preload("Files.File").First(&submission, "id = ?", id).Error
	return submission, err
}

// CreateSubmission creates a new submission and its associated files.
func CreateSubmission(db *gorm.DB, input *model.Submission) error {
	input.CreatedAt = time.Now()
	input.UpdatedAt = nil
	err := db.Create(input).Error
	if err != nil {
		return err
	}
	if len(input.Files) > 0 {
		if err := CreateSubmissionFiles(db, input.Id, input.Files); err != nil {
			return err
		}
	}
	return nil
}

// UpdateSubmissionWithFiles updates an existing submission and its files.
func UpdateSubmissionWithFiles(db *gorm.DB, id string, input *model.Submission) error {
	now := time.Now()
	input.UpdatedAt = &now
	err := db.Model(&model.Submission{}).Where("id = ?", id).Updates(input).Error
	if err != nil {
		return err
	}
	if input.Files != nil {
		if err := DeleteSubmissionFiles(db, id); err != nil {
			return err
		}
		if err := CreateSubmissionFiles(db, id, input.Files); err != nil {
			return err
		}
	}
	return nil
}

// DeleteSubmissionWithFiles deletes a submission and its associated files.
func DeleteSubmissionWithFiles(db *gorm.DB, id string) error {
	if err := DeleteSubmissionFiles(db, id); err != nil {
		return err
	}
	return db.Delete(&model.Submission{}, "id = ?", id).Error
}

// CreateSubmissionFiles creates SubmissionFile records and their File records for a submission.
func CreateSubmissionFiles(db *gorm.DB, submissionID string, files []model.SubmissionFile) error {
	for _, sf := range files {
		if sf.File.ID == "" {
			err := CreateFile(db, &sf.File)
			if err != nil {
				return err
			}
			sf.FileID = sf.File.ID
		}
		sf.SubmissionID = submissionID
		err := db.Create(&sf).Error
		if err != nil {
			return err
		}
	}
	return nil
}

// DeleteSubmissionFiles deletes all SubmissionFile records for a submission.
func DeleteSubmissionFiles(db *gorm.DB, submissionID string) error {
	return db.Where("submission_id = ?", submissionID).Delete(&model.SubmissionFile{}).Error
}

// SearchSubmissions searches for submissions by various criteria
func SearchSubmissions(db *gorm.DB, search, studentId, assignmentId string) ([]model.Submission, error) {
	var submissions []model.Submission
	query := db.Model(&model.Submission{}).Preload("Files.File")

	if search != "" {
		query = query.Where("status ILIKE ?", "%"+search+"%")
	}

	if studentId != "" {
		query = query.Where("student_id = ?", studentId)
	}

	if assignmentId != "" {
		query = query.Where("assignment_id = ?", assignmentId)
	}

	err := query.Find(&submissions).Error
	return submissions, err
}

// UpdateSubmission overload for CRUD - accepts map[string]interface{}
func UpdateSubmission(db *gorm.DB, id string, updates map[string]interface{}) error {
	return db.Model(&model.Submission{}).Where("id = ?", id).Updates(updates).Error
}

// DeleteSubmission deletes a submission and its associated files
func DeleteSubmission(db *gorm.DB, id string) error {
	if err := DeleteSubmissionFiles(db, id); err != nil {
		return err
	}
	return db.Delete(&model.Submission{}, "id = ?", id).Error
}
