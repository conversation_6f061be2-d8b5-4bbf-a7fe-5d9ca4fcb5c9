package persistence

import (
	"log"
	"portal/internal/model"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var db *gorm.DB

func SetDB(database *gorm.DB) {
	db = database
}

func InitDB(dsn string) *gorm.DB {
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	// Migrate the database schema
	if err := Migrate(db); err != nil {
		log.Fatal("Failed to run migrations:", err)
	}
	return db
}

func Migrate(db *gorm.DB) error {
	// Enable uuid-ossp extension for uuid_generate_v4()
	db.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";")

	// AutoMigrate all models
	return db.AutoMigrate(
		&model.User{},
		&model.Student{},
		&model.Coach{},
		&model.Course{},
		&model.Enrollment{},
		&model.Announcement{},
		&model.Notification{},
		&model.Assignment{},
		&model.Quiz{},
		&model.Submission{},
		&model.QuizSubmission{},
		&model.Grade{},
		&model.Attendance{},
		&model.File{},
		&model.AssignmentFile{},
		&model.SubmissionFile{},
		&model.QuizFile{},
		&model.AnnouncementFile{},
		&model.Material{},
	)
}
