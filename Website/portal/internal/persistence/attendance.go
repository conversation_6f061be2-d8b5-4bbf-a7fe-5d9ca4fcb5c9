package persistence

import (
	"portal/internal/model"
	"time"

	"gorm.io/gorm"
)

// CreateAttendance creates a new attendance record
func CreateAttendance(db *gorm.DB, attendance *model.Attendance) error {
	return db.Create(attendance).Error
}

// GetAttendanceByID retrieves an attendance record by its ID
func GetAttendanceByID(db *gorm.DB, id string) (model.Attendance, error) {
	var attendance model.Attendance
	err := db.First(&attendance, "id = ?", id).Error
	return attendance, err
}

// SearchAttendances searches for attendance records by various criteria
func SearchAttendances(db *gorm.DB, studentId, courseId, date string) ([]model.Attendance, error) {
	var attendances []model.Attendance
	query := db.Model(&model.Attendance{})
	
	if studentId != "" {
		query = query.Where("student_id = ?", studentId)
	}
	
	if courseId != "" {
		query = query.Where("course_id = ?", courseId)
	}
	
	if date != "" {
		// Parse date and search for that specific day
		if parsedDate, err := time.Parse("2006-01-02", date); err == nil {
			startOfDay := parsedDate.Truncate(24 * time.Hour)
			endOfDay := startOfDay.Add(24 * time.Hour)
			query = query.Where("date >= ? AND date < ?", startOfDay, endOfDay)
		}
	}
	
	err := query.Find(&attendances).Error
	return attendances, err
}

// UpdateAttendance updates attendance fields by ID
func UpdateAttendance(db *gorm.DB, id string, updates map[string]interface{}) error {
	return db.Model(&model.Attendance{}).Where("id = ?", id).Updates(updates).Error
}

// DeleteAttendance deletes an attendance record by its ID
func DeleteAttendance(db *gorm.DB, id string) error {
	return db.Delete(&model.Attendance{}, "id = ?", id).Error
}

// ListAttendancesByStudent returns all attendance records for a student
func ListAttendancesByStudent(db *gorm.DB, studentID string) ([]model.Attendance, error) {
	var attendances []model.Attendance
	err := db.Where("student_id = ?", studentID).Find(&attendances).Error
	return attendances, err
}

// ListAttendancesByCourse returns all attendance records for a course
func ListAttendancesByCourse(db *gorm.DB, courseID string) ([]model.Attendance, error) {
	var attendances []model.Attendance
	err := db.Where("course_id = ?", courseID).Find(&attendances).Error
	return attendances, err
}

// GetAttendanceByStudentAndDate gets attendance for a specific student on a specific date
func GetAttendanceByStudentAndDate(db *gorm.DB, studentID string, date time.Time) (model.Attendance, error) {
	var attendance model.Attendance
	startOfDay := date.Truncate(24 * time.Hour)
	endOfDay := startOfDay.Add(24 * time.Hour)
	err := db.Where("student_id = ? AND date >= ? AND date < ?", studentID, startOfDay, endOfDay).First(&attendance).Error
	return attendance, err
}
