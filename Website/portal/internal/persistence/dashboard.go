package persistence

import (
	"portal/internal/model"

	"gorm.io/gorm"
)

// GetDashboardData retrieves dashboard data for a student
func GetDashboardData(db *gorm.DB, studentID string) (model.Dashboard, error) {
	var dashboard model.Dashboard

	// For now, return mock data since the full schema isn't implemented yet
	// TODO: Implement proper queries when announcements, enrollments, and assignments tables exist

	// Mock recent announcements
	dashboard.RecentAnnouncements = []model.Announcement{
		{
			Id:      "1",
			Title:   "Welcome to the Portal!",
			Content: "This is a sample announcement to demonstrate the dashboard functionality.",
		},
		{
			Id:      "2",
			Title:   "System Maintenance",
			Content: "Scheduled maintenance will occur this weekend.",
		},
	}

	// Mock upcoming assignments
	dashboard.UpcomingAssignments = []model.Assignment{
		{
			Id:          "1",
			Title:       "Math Homework #1",
			Description: "Complete exercises 1-10 from chapter 3",
			CourseID:    "course-1",
		},
		{
			Id:          "2",
			Title:       "Science Lab Report",
			Description: "Submit your lab findings from last week's experiment",
			CourseID:    "course-2",
		},
	}

	return dashboard, nil
}

// ListAnnouncementsForStudent lists announcements for a specific student
func ListAnnouncementsForStudent(db *gorm.DB, studentID string) ([]model.Announcement, error) {
	var announcements []model.Announcement
	err := db.Table("announcements").
		Joins("JOIN enrollments ON announcements.course_id = enrollments.course_id").
		Where("enrollments.student_id = ?", studentID).
		Order("announcements.created_at DESC").
		Find(&announcements).Error
	return announcements, err
}

// ListNotifications lists notifications for a specific student
func ListNotifications(db *gorm.DB, studentID string) ([]model.Notification, error) {
	var notifications []model.Notification
	err := db.Where("student_id = ?", studentID).
		Order("created_at DESC").
		Find(&notifications).Error
	return notifications, err
}

// ListMaterialsByCourseDashboard lists materials for a specific course (dashboard version)
func ListMaterialsByCourseDashboard(db *gorm.DB, courseID string) ([]model.Material, error) {
	var materials []model.Material
	err := db.Where("course_id = ?", courseID).Find(&materials).Error
	return materials, err
}
