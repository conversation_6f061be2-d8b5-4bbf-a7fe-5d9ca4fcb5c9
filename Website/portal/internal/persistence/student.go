package persistence

import (
	"portal/internal/model"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// --- Student CRUD ---

func ListStudents(db *gorm.DB) ([]model.Student, error) {
	var students []model.Student
	err := db.Find(&students).Error
	return students, err
}

func GetStudentByUsername(db *gorm.DB, username string) (model.Student, error) {
	var student model.Student
	err := db.Where("username = ?", username).First(&student).Error
	return student, err
}

func GetStudentByID(db *gorm.DB, id string) (model.Student, error) {
	var student model.Student
	err := db.Where("id = ?", id).First(&student).Error
	return student, err
}

func CreateStudent(db *gorm.DB, student *model.Student) error {
	if student.Id == "" {
		student.Id = uuid.NewString()
	}
	return db.Create(student).Error
}

func UpdateStudent(db *gorm.DB, id string, update map[string]interface{}) error {
	return db.Model(&model.Student{}).Where("id = ?", id).Updates(update).Error
}

func DeleteStudent(db *gorm.DB, id string) error {
	return db.Delete(&model.Student{}, "id = ?", id).Error
}

func SearchStudents(db *gorm.DB, search string) ([]model.Student, error) {
	var students []model.Student
	query := db.Model(&model.Student{})

	if search != "" {
		// Join with users table to search by user fields
		query = query.Joins("JOIN users ON students.user_id = users.id").
			Where("users.username ILIKE ? OR users.email ILIKE ? OR users.first_name ILIKE ? OR users.last_name ILIKE ?",
				"%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	err := query.Find(&students).Error
	return students, err
}

// --- Dashboard Example ---
func GetDashboard(studentID uint) (map[string]interface{}, error) {
	// Example: return dummy dashboard data
	return map[string]interface{}{"courses": []string{"Math", "Science"}}, nil
}

// Returns true if the student is enrolled in the given course (by UUID string)
func StudentEnrolledInCourse(studentID string, courseID string) bool {
	var count int64
	db.Table("enrollments").Where("student_id = ? AND course_id = ?", studentID, courseID).Count(&count)
	return count > 0
}

// Returns true if the student has access to the assignment (by assignment UUID string)
func StudentHasAccessToAssignment(studentID string, assignmentID string) bool {
	var assignment model.Assignment
	if err := db.First(&assignment, "id = ?", assignmentID).Error; err != nil {
		return false
	}
	return StudentEnrolledInCourse(studentID, assignment.CourseID)
}

// Returns true if the student has access to the quiz (by quiz UUID string)
func StudentHasAccessToQuiz(studentID string, quizID string) bool {
	var quiz model.Quiz
	if err := db.First(&quiz, "id = ?", quizID).Error; err != nil {
		return false
	}
	return StudentEnrolledInCourse(studentID, quiz.CourseID)
}

// Returns true if the student has access to the material (by material UUID string)
func StudentHasAccessToMaterial(studentID string, materialID string) bool {
	var material model.Material
	if err := db.First(&material, "id = ?", materialID).Error; err != nil {
		return false
	}
	return StudentEnrolledInCourse(studentID, material.CourseID)
}
