package persistence

import (
	"portal/internal/model"

	"gorm.io/gorm"
)

// CreateFile inserts a new file record into the database.
func CreateFile(db *gorm.DB, file *model.File) error {
	return db.Create(file).Error
}

// GetFileByID retrieves a file by its ID.
func GetFileByID(db *gorm.DB, id string) (model.File, error) {
	var file model.File
	err := db.First(&file, "id = ?", id).Error
	return file, err
}

// DeleteFile deletes a file by its ID.
func DeleteFile(db *gorm.DB, id string) error {
	return db.Delete(&model.File{}, "id = ?", id).Error
}

// ListFiles retrieves all files (optional, for admin/debugging).
func ListFiles(db *gorm.DB) ([]model.File, error) {
	var files []model.File
	err := db.Find(&files).Error
	return files, err
}
