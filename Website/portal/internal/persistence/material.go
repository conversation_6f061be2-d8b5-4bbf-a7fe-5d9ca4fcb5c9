package persistence

import (
	"portal/internal/model"
	"time"

	"gorm.io/gorm"
)

// ListMaterials retrieves all materials, preloading the file.
func ListMaterials(db *gorm.DB) ([]model.Material, error) {
	var materials []model.Material
	err := db.Preload("File").Find(&materials).Error
	return materials, err
}

// GetMaterialByID retrieves a material by its ID, preloading the file.
func GetMaterialByID(db *gorm.DB, id string) (model.Material, error) {
	var material model.Material
	err := db.Preload("File").First(&material, "id = ?", id).Error
	return material, err
}

// CreateMaterial creates a new material and its associated file.
func CreateMaterial(db *gorm.DB, input *model.Material) error {
	input.CreatedAt = time.Now()
	input.UpdatedAt = nil
	if input.File.ID == "" {
		err := CreateFile(db, &input.File)
		if err != nil {
			return err
		}
		input.FileID = input.File.ID
	}
	return db.Create(input).Error
}

// UpdateMaterialWithFile updates an existing material and its file using MaterialUpdate.
func UpdateMaterialWithFile(db *gorm.DB, id string, input *model.MaterialUpdate) error {
	update := map[string]interface{}{}
	if input.Title != nil {
		update["title"] = *input.Title
	}
	if input.Description != nil {
		update["description"] = *input.Description
	}
	if input.CourseID != nil {
		update["course_id"] = *input.CourseID
	}
	if input.UploadedBy != nil {
		update["uploaded_by"] = *input.UploadedBy
	}
	if input.FileID != nil {
		update["file_id"] = *input.FileID
	}
	update["updated_at"] = time.Now()
	return db.Model(&model.Material{}).Where("id = ?", id).Updates(update).Error
}

// DeleteMaterial deletes a material and its associated file record (does not delete the file from disk).
func DeleteMaterial(db *gorm.DB, id string) error {
	var material model.Material
	err := db.First(&material, "id = ?", id).Error
	if err != nil {
		return err
	}
	if material.FileID != "" {
		_ = DeleteFile(db, material.FileID) // Optionally handle file deletion
	}
	return db.Delete(&model.Material{}, "id = ?", id).Error
}

// ListMaterialsByCourse returns all materials for a course.
func ListMaterialsByCourse(db *gorm.DB, courseID string) ([]model.Material, error) {
	var materials []model.Material
	err := db.Where("course_id = ?", courseID).Find(&materials).Error
	return materials, err
}

// SearchMaterials searches for materials by title, description, or course
func SearchMaterials(db *gorm.DB, search, courseId string) ([]model.Material, error) {
	var materials []model.Material
	query := db.Model(&model.Material{})

	if search != "" {
		query = query.Where("title ILIKE ? OR description ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	if courseId != "" {
		query = query.Where("course_id = ?", courseId)
	}

	err := query.Find(&materials).Error
	return materials, err
}

// UpdateMaterial overload for CRUD - accepts map[string]interface{}
func UpdateMaterial(db *gorm.DB, id string, updates map[string]interface{}) error {
	return db.Model(&model.Material{}).Where("id = ?", id).Updates(updates).Error
}
