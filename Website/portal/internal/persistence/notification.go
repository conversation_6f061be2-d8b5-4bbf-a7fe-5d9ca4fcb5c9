package persistence

import (
	"portal/internal/model"

	"gorm.io/gorm"
)

// --- Notification CRUD ---
func GetNotificationByID(db *gorm.DB, id string) (model.Notification, error) {
	var notif model.Notification
	err := db.First(&notif, "id = ?", id).Error
	return notif, err
}

func CreateNotificationFromInput(db *gorm.DB, input *model.NotificationCreate) error {
	notification := model.Notification{
		Message:   input.Message,
		StudentID: input.StudentID,
		// Other fields (e.g., Id, Date) will be empty or set by DB
	}
	return db.Create(&notification).Error
}

func UpdateNotificationFromInput(db *gorm.DB, id string, input *model.NotificationUpdate) error {
	update := map[string]interface{}{}
	if input.Message != nil {
		update["message"] = *input.Message
	}
	if input.StudentID != nil {
		update["student_id"] = *input.StudentID
	}
	// Other fields in Notification will remain unchanged
	return db.Model(&model.Notification{}).Where("id = ?", id).Updates(update).Error
}

func DeleteNotification(db *gorm.DB, id string) error {
	return db.Delete(&model.Notification{}, "id = ?", id).Error
}

// List notifications for a student (by student ID)
func ListNotificationsForStudent(db *gorm.DB, studentID string) ([]model.Notification, error) {
	var notifications []model.Notification
	err := db.Where("student_id = ?", studentID).Find(&notifications).Error
	return notifications, err
}

// SearchNotifications searches for notifications by message content or student
func SearchNotifications(db *gorm.DB, search, studentId string) ([]model.Notification, error) {
	var notifications []model.Notification
	query := db.Model(&model.Notification{})

	if search != "" {
		query = query.Where("message ILIKE ?", "%"+search+"%")
	}

	if studentId != "" {
		query = query.Where("student_id = ?", studentId)
	}

	err := query.Find(&notifications).Error
	return notifications, err
}

// CreateNotification overload for CRUD - accepts model.Notification directly
func CreateNotification(db *gorm.DB, notification *model.Notification) error {
	return db.Create(notification).Error
}

// UpdateNotification overload for CRUD - accepts map[string]interface{}
func UpdateNotification(db *gorm.DB, id string, updates map[string]interface{}) error {
	return db.Model(&model.Notification{}).Where("id = ?", id).Updates(updates).Error
}
