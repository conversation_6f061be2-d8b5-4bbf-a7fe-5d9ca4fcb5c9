package persistence

import (
	"portal/internal/model"

	"gorm.io/gorm"
)

// <PERSON><PERSON><PERSON><PERSON> inserts a new user record into the database.
func CreateUser(db *gorm.DB, user *model.User) error {
	return db.Create(user).Error
}

// GetUserByID retrieves a user by their ID.
func GetUserByID(db *gorm.DB, id string) (model.User, error) {
	var user model.User
	err := db.First(&user, "id = ?", id).Error
	return user, err
}

// GetUserByUsername retrieves a user by their username.
func GetUserByUsername(db *gorm.DB, username string) (model.User, error) {
	var user model.User
	err := db.Where("username = ?", username).First(&user).Error
	return user, err
}

// GetUserByEmail retrieves a user by their email.
func GetUserByEmail(db *gorm.DB, email string) (model.User, error) {
	var user model.User
	err := db.Where("email = ?", email).First(&user).Error
	return user, err
}

// UpdateUser updates user fields by ID.
func UpdateUser(db *gorm.DB, id string, update map[string]interface{}) error {
	return db.Model(&model.User{}).Where("id = ?", id).Updates(update).Error
}

// DeleteUser deletes a user by their ID.
func DeleteUser(db *gorm.DB, id string) error {
	return db.Delete(&model.User{}, "id = ?", id).Error
}

// SearchUsers searches for users by username, email, or role
func SearchUsers(db *gorm.DB, search, role string) ([]model.User, error) {
	var users []model.User
	query := db.Model(&model.User{})

	if search != "" {
		query = query.Where("username ILIKE ? OR email ILIKE ? OR first_name ILIKE ? OR last_name ILIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	if role != "" {
		query = query.Where("role = ?", role)
	}

	err := query.Find(&users).Error
	return users, err
}
