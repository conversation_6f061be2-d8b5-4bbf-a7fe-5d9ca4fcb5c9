package persistence

import (
	"portal/internal/model"
	"time"

	"gorm.io/gorm"
)

// CreateAssignmentFiles creates AssignmentFile records and their File records for an assignment.
func CreateAssignmentFiles(db *gorm.DB, assignmentID string, files []model.AssignmentFile) error {
	for _, af := range files {
		if af.File.ID == "" {
			err := CreateFile(db, &af.File)
			if err != nil {
				return err
			}
			af.FileID = af.File.ID
		}
		af.AssignmentID = assignmentID
		err := db.Create(&af).Error
		if err != nil {
			return err
		}
	}
	return nil
}

// DeleteAssignmentFiles deletes all AssignmentFile records for an assignment.
func DeleteAssignmentFiles(db *gorm.DB, assignmentID string) error {
	return db.Where("assignment_id = ?", assignmentID).Delete(&model.AssignmentFile{}).Error
}

// --- Assignment CRUD ---
// ListAssignments retrieves all assignments from the database, preloading files.
func ListAssignments(db *gorm.DB) ([]model.Assignment, error) {
	var assignments []model.Assignment
	err := db.Preload("Files.File").Find(&assignments).Error
	return assignments, err
}

// GetAssignmentByID retrieves an assignment by its ID, optionally filtering by course ID, preloading files.
func GetAssignmentByID(db *gorm.DB, id string, courseID ...string) (model.Assignment, error) {
	var assignment model.Assignment
	if len(courseID) > 0 && courseID[0] != "" {
		err := db.Preload("Files.File").Where("id = ? AND course_id = ?", id, courseID[0]).First(&assignment).Error
		return assignment, err
	}
	err := db.Preload("Files.File").Where("id = ?", id).First(&assignment).Error
	return assignment, err
}

// CreateAssignmentWithFiles creates a new assignment and its associated files.
func CreateAssignmentWithFiles(db *gorm.DB, input *model.AssignmentCreate) error {
	assignment := model.Assignment{
		Title:       input.Title,
		Description: input.Description,
		DueDate:     input.DueDate,
		CourseID:    input.CourseID,
		CreatedAt:   time.Now(),
		UpdatedAt:   nil,
	}
	err := db.Create(&assignment).Error
	if err != nil {
		return err
	}
	if len(input.Files) > 0 {
		if err := CreateAssignmentFiles(db, assignment.Id, input.Files); err != nil {
			return err
		}
	}
	return nil
}

// UpdateAssignmentWithFiles updates an existing assignment by ID, and updates files if provided.
func UpdateAssignmentWithFiles(db *gorm.DB, id string, input *model.AssignmentUpdate) error {
	update := map[string]interface{}{}
	if input.Title != nil {
		update["title"] = *input.Title
	}
	if input.Description != nil {
		update["description"] = *input.Description
	}
	if input.DueDate != nil {
		update["due_date"] = *input.DueDate
	}
	if input.Status != nil {
		update["status"] = *input.Status
	}
	if input.CourseID != nil {
		update["course_id"] = *input.CourseID
	}
	update["updated_at"] = time.Now()
	err := db.Model(&model.Assignment{}).Where("id = ?", id).Updates(update).Error
	if err != nil {
		return err
	}
	if input.Files != nil {
		if err := DeleteAssignmentFiles(db, id); err != nil {
			return err
		}
		if err := CreateAssignmentFiles(db, id, input.Files); err != nil {
			return err
		}
	}
	return nil
}

// DeleteAssignment deletes an assignment and its associated files (AssignmentFile records).
func DeleteAssignment(db *gorm.DB, id string) error {
	if err := DeleteAssignmentFiles(db, id); err != nil {
		return err
	}
	return db.Delete(&model.Assignment{}, "id = ?", id).Error
}

// List assignments for a course, preloading files
func ListAssignmentsByCourse(db *gorm.DB, courseId string) ([]model.Assignment, error) {
	var assignments []model.Assignment
	err := db.Preload("Files.File").Where("course_id = ?", courseId).Find(&assignments).Error
	return assignments, err
}

// List assignments for a student's courses, preloading files
func ListAssignmentsForStudent(db *gorm.DB, studentID string) ([]model.Assignment, error) {
	var assignments []model.Assignment
	var courseIDs []string
	err := db.Table("enrollments").Where("student_id = ?", studentID).Pluck("course_id", &courseIDs).Error
	if err != nil {
		return nil, err
	}
	err = db.Preload("Files.File").Where("course_id IN ?", courseIDs).Find(&assignments).Error
	return assignments, err
}

// SearchAssignments searches for assignments by title, description, course, or status
func SearchAssignments(db *gorm.DB, search, courseId, status string) ([]model.Assignment, error) {
	var assignments []model.Assignment
	query := db.Model(&model.Assignment{}).Preload("Files.File")

	if search != "" {
		query = query.Where("title ILIKE ? OR description ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	if courseId != "" {
		query = query.Where("course_id = ?", courseId)
	}

	if status != "" {
		query = query.Where("status = ?", status)
	}

	err := query.Find(&assignments).Error
	return assignments, err
}

// CreateAssignment overload for CRUD - accepts model.Assignment directly
func CreateAssignment(db *gorm.DB, assignment *model.Assignment) error {
	return db.Create(assignment).Error
}

// UpdateAssignment overload for CRUD - accepts map[string]interface{}
func UpdateAssignment(db *gorm.DB, id string, updates map[string]interface{}) error {
	return db.Model(&model.Assignment{}).Where("id = ?", id).Updates(updates).Error
}
