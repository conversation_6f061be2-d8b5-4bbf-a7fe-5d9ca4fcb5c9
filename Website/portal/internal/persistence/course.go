package persistence

import (
	"portal/internal/model"

	"gorm.io/gorm"
)

// CreateCourse inserts a new course record into the database.
func CreateCourse(db *gorm.DB, course *model.Course) error {
	return db.Create(course).Error
}

// GetCourseByID retrieves a course by its ID.
func GetCourseByID(db *gorm.DB, id string) (model.CourseDetail, error) {
	var course model.Course
	err := db.First(&course, "id = ?", id).Error
	if err != nil {
		return model.CourseDetail{}, err
	}

	// Convert to CourseDetail
	detail := model.CourseDetail{
		Id:          course.Id,
		Name:        course.Name,
		Description: course.Description,
		Instructor:  "TBD", // TODO: Add instructor field to Course model
		Schedule:    "TBD", // TODO: Add schedule field to Course model
	}
	return detail, nil
}

// UpdateCourse updates an existing course record.
func UpdateCourse(db *gorm.DB, id string, update map[string]interface{}) error {
	return db.Model(&model.Course{}).Where("id = ?", id).Updates(update).Error
}

// DeleteCourse deletes a course by its ID.
func DeleteCourse(db *gorm.DB, id string) error {
	return db.Delete(&model.Course{}, "id = ?", id).Error
}

// ListCourses retrieves all courses.
func ListCourses(db *gorm.DB) ([]model.Course, error) {
	var courses []model.Course
	err := db.Find(&courses).Error
	return courses, err
}

// ListCoursesByStudent returns all courses a student is enrolled in.
func ListCoursesByStudent(db *gorm.DB, studentID string) ([]model.Course, error) {
	var courses []model.Course
	err := db.Table("courses").
		Select("courses.*").
		Joins("join enrollments on enrollments.course_id = courses.id").
		Where("enrollments.student_id = ?", studentID).
		Find(&courses).Error
	return courses, err
}

// SearchCourses searches for courses by name, description, or instructor
func SearchCourses(db *gorm.DB, search, instructor string) ([]model.Course, error) {
	var courses []model.Course
	query := db.Model(&model.Course{})

	if search != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	if instructor != "" {
		query = query.Where("instructor ILIKE ?", "%"+instructor+"%")
	}

	err := query.Find(&courses).Error
	return courses, err
}
