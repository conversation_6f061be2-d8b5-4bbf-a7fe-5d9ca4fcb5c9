package persistence

import (
	"portal/internal/model"

	"gorm.io/gorm"
)

// CreateEnrollment creates a new enrollment record
func CreateEnrollment(db *gorm.DB, enrollment *model.Enrollment) error {
	return db.Create(enrollment).Error
}

// GetEnrollment fetches an enrollment by its ID
func GetEnrollmentByID(db *gorm.DB, id string) (model.Enrollment, error) {
	var enrollment model.Enrollment
	err := db.First(&enrollment, "id = ?", id).Error
	return enrollment, err
}

// ListEnrollmentsByStudent returns all enrollments for a student
func ListEnrollmentsByStudent(db *gorm.DB, studentID string) ([]model.Enrollment, error) {
	var enrollments []model.Enrollment
	err := db.Where("student_id = ?", studentID).Find(&enrollments).Error
	return enrollments, err
}

// ListEnrollmentsByCourse returns all enrollments for a course
func ListEnrollmentsByCourse(db *gorm.DB, courseID string) ([]model.Enrollment, error) {
	var enrollments []model.Enrollment
	err := db.Where("course_id = ?", courseID).Find(&enrollments).Error
	return enrollments, err
}

// DeleteEnrollment deletes an enrollment by its ID
func DeleteEnrollment(db *gorm.DB, id string) error {
	return db.Delete(&model.Enrollment{}, "id = ?", id).Error
}

// SearchEnrollments searches for enrollments by student or course
func SearchEnrollments(db *gorm.DB, studentId, courseId string) ([]model.Enrollment, error) {
	var enrollments []model.Enrollment
	query := db.Model(&model.Enrollment{})

	if studentId != "" {
		query = query.Where("student_id = ?", studentId)
	}

	if courseId != "" {
		query = query.Where("course_id = ?", courseId)
	}

	err := query.Find(&enrollments).Error
	return enrollments, err
}

// GetEnrollmentByID already exists above

// DeleteEnrollmentByStudentAndCourse deletes an enrollment by student and course
func DeleteEnrollmentByStudentAndCourse(db *gorm.DB, studentID, courseID string) error {
	return db.Where("student_id = ? AND course_id = ?", studentID, courseID).Delete(&model.Enrollment{}).Error
}
