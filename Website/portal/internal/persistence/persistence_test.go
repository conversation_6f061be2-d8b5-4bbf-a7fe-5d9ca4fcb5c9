package persistence

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"portal/internal/model"
)

func setupTestDB(t *testing.T) *gorm.DB {
	dsn := "host=localhost user=postgres password=******** dbname=User_test port=5432 sslmode=disable"
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	assert.NoError(t, err)
	db.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";")
	db.AutoMigrate(
		&model.User{},
		&model.Coach{},
		&model.Student{},
		&model.Announcement{},
		&model.Notification{},
		&model.Course{},
		&model.Assignment{},
		&model.AssignmentFile{},
		&model.AnnouncementFile{},
		&model.Quiz{},
		&model.QuizFile{},
		&model.Submission{},
		&model.SubmissionFile{},
		&model.File{},
		&model.Material{},
		&model.Enrollment{},
	)
	db.Exec("TRUNCATE TABLE users, posts, comments RESTART IDENTITY CASCADE;")
	return db
}

func TestUserCRUD(t *testing.T) {
	db := setupTestDB(t)
	uniqueUsername := "testuser_" + uuid.NewString()
	uniqueEmail := "test_" + uuid.NewString() + "@example.com"
	user := model.User{ID: uuid.NewString(), Username: uniqueUsername, Email: uniqueEmail, Password_hash: "hash", Role: "student"}
	err := db.Create(&user).Error
	assert.NoError(t, err)
	var found model.User
	err = db.First(&found, "username = ?", uniqueUsername).Error
	assert.NoError(t, err)
	assert.Equal(t, user.Email, found.Email)
}

func TestCoachCRUD(t *testing.T) {
	db := setupTestDB(t)
	userID := uuid.NewString()
	coach := model.Coach{Id: uuid.NewString(), UserID: userID}
	err := db.Create(&coach).Error
	assert.NoError(t, err)
	var found model.Coach
	err = db.First(&found, "user_id = ?", userID).Error
	assert.NoError(t, err)
	assert.Equal(t, coach.UserID, found.UserID)
}

func TestStudentCRUD(t *testing.T) {
	db := setupTestDB(t)
	userID := uuid.NewString()
	student := model.Student{Id: uuid.NewString(), UserID: userID}
	err := db.Create(&student).Error
	assert.NoError(t, err)
	var found model.Student
	err = db.First(&found, "user_id = ?", userID).Error
	assert.NoError(t, err)
	assert.Equal(t, student.UserID, found.UserID)
}

func TestAnnouncementCRUD(t *testing.T) {
	db := setupTestDB(t)
	courseID := uuid.NewString()
	ann := model.Announcement{Id: uuid.NewString(), Title: "Test", Content: "Hello", Date: time.Now(), CourseID: courseID}
	err := db.Create(&ann).Error
	assert.NoError(t, err)
	var found model.Announcement
	err = db.First(&found, "title = ?", "Test").Error
	assert.NoError(t, err)
	assert.Equal(t, ann.Content, found.Content)
}

func TestNotificationCRUD(t *testing.T) {
	db := setupTestDB(t)
	studentID := uuid.NewString()
	note := model.Notification{Id: uuid.NewString(), Message: "Hi", Date: time.Now(), StudentID: studentID}
	err := db.Create(&note).Error
	assert.NoError(t, err)
	var found model.Notification
	err = db.First(&found, "student_id = ?", studentID).Error
	assert.NoError(t, err)
	assert.Equal(t, note.Message, found.Message)
}

func TestCourseCRUD(t *testing.T) {
	db := setupTestDB(t)
	course := model.Course{Id: uuid.NewString(), Name: "Math", Description: "desc", Instructor: "inst"}
	err := db.Create(&course).Error
	assert.NoError(t, err)
	var found model.Course
	err = db.First(&found, "name = ?", "Math").Error
	assert.NoError(t, err)
	assert.Equal(t, course.Description, found.Description)
}

func TestAssignmentCRUD(t *testing.T) {
	db := setupTestDB(t)
	courseID := uuid.NewString()
	assign := model.Assignment{Id: uuid.NewString(), Title: "HW", Description: "desc", DueDate: time.Now(), Status: "open", CourseID: courseID}
	err := db.Create(&assign).Error
	assert.NoError(t, err)
	var found model.Assignment
	err = db.First(&found, "title = ?", "HW").Error
	assert.NoError(t, err)
	assert.Equal(t, assign.Description, found.Description)
}

func TestQuizCRUD(t *testing.T) {
	db := setupTestDB(t)
	courseID := uuid.NewString()
	quiz := model.Quiz{Id: uuid.NewString(), Title: "Quiz1", Description: "desc", DueDate: time.Now(), Status: "open", CourseID: courseID}
	err := db.Create(&quiz).Error
	assert.NoError(t, err)
	var found model.Quiz
	err = db.First(&found, "title = ?", "Quiz1").Error
	assert.NoError(t, err)
	assert.Equal(t, quiz.Description, found.Description)
}

func TestSubmissionCRUD(t *testing.T) {
	db := setupTestDB(t)
	assignmentID := uuid.NewString()
	studentID := uuid.NewString()
	sub := model.Submission{Id: uuid.NewString(), AssignmentID: assignmentID, StudentID: studentID, SubmittedAt: time.Now(), Status: "submitted"}
	err := db.Create(&sub).Error
	assert.NoError(t, err)
	var found model.Submission
	err = db.First(&found, "assignment_id = ?", assignmentID).Error
	assert.NoError(t, err)
	assert.Equal(t, sub.StudentID, found.StudentID)
}

func TestFileCRUD(t *testing.T) {
	db := setupTestDB(t)
	file := model.File{ID: uuid.NewString(), FileName: "file.txt", FilePath: "/tmp/file.txt", FileSize: 123}
	err := db.Create(&file).Error
	assert.NoError(t, err)
	var found model.File
	err = db.First(&found, "file_name = ?", "file.txt").Error
	assert.NoError(t, err)
	assert.Equal(t, file.FilePath, found.FilePath)
}

func TestMaterialCRUD(t *testing.T) {
	db := setupTestDB(t)
	courseID := uuid.NewString()
	fileID := uuid.NewString()
	// Insert a file first to satisfy the foreign key constraint
	file := model.File{ID: fileID, FileName: "file.txt", FilePath: "/tmp/file.txt", FileSize: 123}
	err := db.Create(&file).Error
	assert.NoError(t, err)
	mat := model.Material{ID: uuid.NewString(), Title: "Lecture", Description: "desc", CourseID: courseID, UploadedBy: uuid.NewString(), FileID: fileID}
	err = db.Create(&mat).Error
	assert.NoError(t, err)
	var found model.Material
	err = db.First(&found, "title = ?", "Lecture").Error
	assert.NoError(t, err)
	assert.Equal(t, mat.Description, found.Description)
}

func TestEnrollmentCRUD(t *testing.T) {
	db := setupTestDB(t)
	studentID := uuid.NewString()
	courseID := uuid.NewString()
	enroll := model.Enrollment{ID: uuid.NewString(), StudentID: studentID, CourseID: courseID}
	err := db.Create(&enroll).Error
	assert.NoError(t, err)
	var found model.Enrollment
	err = db.First(&found, "student_id = ?", studentID).Error
	assert.NoError(t, err)
	assert.Equal(t, enroll.CourseID, found.CourseID)
}

// --- User CRUD & Search ---
func TestUserPersistence(t *testing.T) {
	db := setupTestDB(t)
	// Create
	user := &model.User{ID: uuid.NewString(), Username: "testuser_" + uuid.NewString(), Email: "test_" + uuid.NewString() + "@example.com", Password_hash: "hash", Role: "student"}
	err := CreateUser(db, user)
	assert.NoError(t, err)

	// Get by ID
	found, err := GetUserByID(db, user.ID)
	assert.NoError(t, err)
	assert.Equal(t, user.Username, found.Username)

	// Get by Username
	found, err = GetUserByUsername(db, user.Username)
	assert.NoError(t, err)
	assert.Equal(t, user.Email, found.Email)

	// Get by Email
	found, err = GetUserByEmail(db, user.Email)
	assert.NoError(t, err)
	assert.Equal(t, user.Username, found.Username)

	// Update
	err = UpdateUser(db, user.ID, map[string]interface{}{"Role": "coach"})
	assert.NoError(t, err)
	found, err = GetUserByID(db, user.ID)
	assert.NoError(t, err)
	assert.Equal(t, "coach", found.Role)

	// Delete
	err = DeleteUser(db, user.ID)
	assert.NoError(t, err)
	_, err = GetUserByID(db, user.ID)
	assert.Error(t, err)
}

// --- Announcement CRUD & Search ---
func TestAnnouncementPersistence(t *testing.T) {
	db := setupTestDB(t)

	courseID := uuid.NewString()
	title := "TestAnn_" + uuid.NewString()
	ann := model.Announcement{Id: uuid.NewString(), Title: title, Content: "Hello", Date: time.Now(), CourseID: courseID}
	// Create
	err := CreateAnnouncement(db, &ann)
	assert.NoError(t, err)
	// List
	anns, err := ListAnnouncements(db)
	assert.NoError(t, err)
	assert.NotEmpty(t, anns)
	// Get by ID
	found, err := GetAnnouncementByID(db, ann.Id)
	assert.NoError(t, err)
	// Update
	found.Content = "Updated!"
	err = UpdateAnnouncement(db, ann.Id, &found)
	assert.NoError(t, err)
	found, err = GetAnnouncementByID(db, ann.Id)
	assert.NoError(t, err)
	assert.Equal(t, "Updated!", found.Content)
	// Delete
	err = DeleteAnnouncement(db, ann.Id)
	assert.NoError(t, err)
	_, err = GetAnnouncementByID(db, ann.Id)
	assert.Error(t, err)
}

// --- Assignment CRUD & Search ---
func TestAssignmentPersistence(t *testing.T) {
	db := setupTestDB(t)
	courseID := uuid.NewString()
	title := "HWTest_" + uuid.NewString()
	assign := model.Assignment{Id: uuid.NewString(), Title: title, Description: "desc", DueDate: time.Now(), Status: "open", CourseID: courseID}
	// Create
	input := &model.AssignmentCreate{Title: assign.Title, Description: assign.Description, DueDate: assign.DueDate, CourseID: assign.CourseID}
	err := CreateAssignment(db, input)
	assert.NoError(t, err)
	// List
	assigns, err := ListAssignments(db)
	assert.NoError(t, err)
	assert.NotEmpty(t, assigns)
	// Get by ID
	found, err := GetAssignmentByID(db, assigns[0].Id)
	assert.NoError(t, err)
	// Update
	update := &model.AssignmentUpdate{Description: ptrString("Updated!")}
	err = UpdateAssignment(db, assigns[0].Id, update)
	assert.NoError(t, err)
	found, err = GetAssignmentByID(db, assigns[0].Id)
	assert.NoError(t, err)
	assert.Equal(t, "Updated!", found.Description)
	// Delete
	err = DeleteAssignment(db, assigns[0].Id)
	assert.NoError(t, err)
	_, err = GetAssignmentByID(db, assigns[0].Id)
	assert.Error(t, err)
}

// --- Material CRUD & Search ---
func TestMaterialPersistence(t *testing.T) {
	db := setupTestDB(t)
	courseID := uuid.NewString()
	fileID := uuid.NewString()
	title := "LectureTest_" + uuid.NewString()
	file := model.File{ID: fileID, FileName: "file_" + uuid.NewString() + ".txt", FilePath: "/tmp/file.txt", FileSize: 123}
	CreateFile(db, &file)
	mat := model.Material{ID: uuid.NewString(), Title: title, Description: "desc", CourseID: courseID, UploadedBy: uuid.NewString(), FileID: fileID, File: file}
	// Create
	err := CreateMaterial(db, &mat)
	assert.NoError(t, err)
	// List
	mats, err := ListMaterials(db)
	assert.NoError(t, err)
	assert.NotEmpty(t, mats)
	// Get by ID
	found, err := GetMaterialByID(db, mat.ID)
	assert.NoError(t, err)
	// Update
	update := &model.MaterialUpdate{Description: ptrString("Updated!")}
	err = UpdateMaterial(db, mat.ID, update)
	assert.NoError(t, err)
	found, err = GetMaterialByID(db, mat.ID)
	assert.NoError(t, err)
	assert.Equal(t, "Updated!", found.Description)
	// Delete
	err = DeleteMaterial(db, mat.ID)
	assert.NoError(t, err)
	_, err = GetMaterialByID(db, mat.ID)
	assert.Error(t, err)
}

// --- Notification CRUD & Search ---
func TestNotificationPersistence(t *testing.T) {
	db := setupTestDB(t)
	studentID := uuid.NewString()
	msg := "HiTest_" + uuid.NewString()
	input := &model.NotificationCreate{Message: msg, StudentID: studentID}
	// Create
	err := CreateNotification(db, input)
	assert.NoError(t, err)
	// List by student
	notes, err := ListNotificationsForStudent(db, studentID)
	assert.NoError(t, err)
	assert.NotEmpty(t, notes)
	// Get by ID
	found, err := GetNotificationByID(db, notes[0].Id)
	assert.NoError(t, err)
	// Update
	update := &model.NotificationUpdate{Message: ptrString("Updated!")}
	err = UpdateNotification(db, notes[0].Id, update)
	assert.NoError(t, err)
	found, err = GetNotificationByID(db, notes[0].Id)
	assert.NoError(t, err)
	assert.Equal(t, "Updated!", found.Message)
	// Delete
	err = DeleteNotification(db, notes[0].Id)
	assert.NoError(t, err)
	_, err = GetNotificationByID(db, notes[0].Id)
	assert.Error(t, err)
}

// --- Helper ---
func ptrString(s string) *string { return &s }
