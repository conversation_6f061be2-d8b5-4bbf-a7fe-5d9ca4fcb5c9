package persistence

import (
	"portal/internal/model"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// CreateCoach inserts a new coach record into the database.
func CreateCoach(db *gorm.DB, coach *model.Coach) error {
	if coach.Id == "" {
		coach.Id = uuid.NewString()
	}
	return db.<PERSON>reate(coach).Error
}

// GetCoachByID retrieves a coach by their ID.
func GetCoachByID(db *gorm.DB, id string) (model.Coach, error) {
	var coach model.Coach
	err := db.First(&coach, "id = ?", id).Error
	return coach, err
}

// SearchCoaches searches for coaches by user information
func SearchCoaches(db *gorm.DB, search string) ([]model.Coach, error) {
	var coaches []model.Coach
	query := db.Model(&model.Coach{})
	
	if search != "" {
		// Join with users table to search by user fields
		query = query.Joins("JOIN users ON coaches.user_id = users.id").
			Where("users.username ILIKE ? OR users.email ILIKE ? OR users.first_name ILIKE ? OR users.last_name ILIKE ?", 
				"%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}
	
	err := query.Find(&coaches).Error
	return coaches, err
}

// UpdateCoach updates coach fields by ID.
func UpdateCoach(db *gorm.DB, id string, update map[string]interface{}) error {
	return db.Model(&model.Coach{}).Where("id = ?", id).Updates(update).Error
}

// DeleteCoach deletes a coach by their ID.
func DeleteCoach(db *gorm.DB, id string) error {
	return db.Delete(&model.Coach{}, "id = ?", id).Error
}
