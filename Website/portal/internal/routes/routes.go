package routes

import (
	"portal/internal/handlers"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SetupRoutes configures all application routes
func SetupRoutes(database *gorm.DB) *gin.Engine {
	router := gin.Default()

	// Add CORS middleware
	router.Use(func(c *gin.Context) {
		c.<PERSON><PERSON>("Access-Control-Allow-Origin", "*")
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// Setup API routes
	setupAPIRoutes(router, database)

	// Setup test routes
	setupTestRoutes(router, database)

	// Setup file routes
	setupFileRoutes(router, database)

	// Setup course routes
	setupCourseRoutes(router, database)

	// Serve static files
	router.StaticFile("/test.html", "./test.html")
	router.StaticFile("/env.js", "./env.js")

	return router
}

// setupAPIRoutes sets up CRUD API routes
func setupAPIRoutes(router *gin.Engine, database *gorm.DB) {
	api := router.Group("/api")

	// User CRUD
	api.POST("/users", handlers.CreateUser)
	api.GET("/users", handlers.GetUsers)
	api.GET("/users/:id", handlers.GetUser)
	api.PUT("/users/:id", handlers.UpdateUser)
	api.DELETE("/users/:id", handlers.DeleteUser)

	// Student CRUD
	api.POST("/students", handlers.CreateStudent)
	api.GET("/students", handlers.GetStudents)
	api.GET("/students/:id", handlers.GetStudent)
	api.PUT("/students/:id", handlers.UpdateStudent)
	api.DELETE("/students/:id", handlers.DeleteStudent)

	// Coach CRUD
	api.POST("/coaches", handlers.CreateCoach)
	api.GET("/coaches", handlers.GetCoaches)
	api.GET("/coaches/:id", handlers.GetCoach)
	api.PUT("/coaches/:id", handlers.UpdateCoach)
	api.DELETE("/coaches/:id", handlers.DeleteCoach)

	// Course CRUD (enhanced)
	api.GET("/courses", handlers.GetCourses)
	api.GET("/courses/:id", handlers.GetCourse)
	api.POST("/courses", handlers.CreateCourse)
	api.PUT("/courses/:id", handlers.UpdateCourse)
	api.DELETE("/courses/:id", handlers.DeleteCourse)

	// Assignment CRUD
	api.POST("/assignments", handlers.CreateAssignment)
	api.GET("/assignments", handlers.GetAssignments)
	api.GET("/assignments/:id", handlers.GetAssignment)
	api.PUT("/assignments/:id", handlers.UpdateAssignment)
	api.DELETE("/assignments/:id", handlers.DeleteAssignment)

	// Quiz CRUD
	api.POST("/quizzes", handlers.CreateQuiz)
	api.GET("/quizzes", handlers.GetQuizzes)
	api.GET("/quizzes/:id", handlers.GetQuiz)
	api.PUT("/quizzes/:id", handlers.UpdateQuiz)
	api.DELETE("/quizzes/:id", handlers.DeleteQuiz)

	// Announcement CRUD
	api.POST("/announcements", handlers.CreateAnnouncement)
	api.GET("/announcements", handlers.GetAnnouncements)
	api.GET("/announcements/:id", handlers.GetAnnouncement)
	api.PUT("/announcements/:id", handlers.UpdateAnnouncement)
	api.DELETE("/announcements/:id", handlers.DeleteAnnouncement)

	// Notification CRUD
	api.POST("/notifications", handlers.CreateNotification)
	api.GET("/notifications", handlers.GetNotifications)
	api.GET("/notifications/:id", handlers.GetNotification)
	api.PUT("/notifications/:id", handlers.UpdateNotification)
	api.DELETE("/notifications/:id", handlers.DeleteNotification)

	// Submission CRUD
	api.POST("/submissions", handlers.CreateSubmission)
	api.GET("/submissions", handlers.GetSubmissions)
	api.GET("/submissions/:id", handlers.GetSubmission)
	api.PUT("/submissions/:id", handlers.UpdateSubmission)
	api.DELETE("/submissions/:id", handlers.DeleteSubmission)

	// Material CRUD
	api.POST("/materials", handlers.CreateMaterial)
	api.GET("/materials", handlers.GetMaterials)
	api.GET("/materials/:id", handlers.GetMaterial)
	api.PUT("/materials/:id", handlers.UpdateMaterial)
	api.DELETE("/materials/:id", handlers.DeleteMaterial)

	// Enrollment CRUD
	api.POST("/enrollments", handlers.CreateEnrollment)
	api.GET("/enrollments", handlers.GetEnrollments)
	api.GET("/enrollments/:id", handlers.GetEnrollment)
	api.DELETE("/enrollments/:id", handlers.DeleteEnrollment)

	// Grade CRUD
	api.POST("/grades", handlers.CreateGrade)
	api.GET("/grades", handlers.GetGrades)
	api.GET("/grades/:id", handlers.GetGrade)
	api.PUT("/grades/:id", handlers.UpdateGrade)
	api.DELETE("/grades/:id", handlers.DeleteGrade)

	// Attendance CRUD
	api.POST("/attendances", handlers.CreateAttendance)
	api.GET("/attendances", handlers.GetAttendances)
	api.GET("/attendances/:id", handlers.GetAttendance)
	api.PUT("/attendances/:id", handlers.UpdateAttendance)
	api.DELETE("/attendances/:id", handlers.DeleteAttendance)
}

// setupTestRoutes sets up test endpoints without authentication
func setupTestRoutes(router *gin.Engine, database *gorm.DB) {
	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "Portal API is running",
			"endpoints": []string{
				"GET /courses",
				"GET /dashboard",
				"POST /logout",
				"GET /students/:studentId",
				"PUT /students/:studentId",
				"GET /user",
			},
		})
	})

	// Test endpoints
	router.GET("/test/courses", func(c *gin.Context) {
		c.JSON(200, []gin.H{
			{"id": "1", "name": "Introduction to Programming", "description": "Learn the basics of programming"},
			{"id": "2", "name": "Web Development", "description": "Build modern web applications"},
			{"id": "3", "name": "Data Science", "description": "Analyze data and build models"},
		})
	})

	router.GET("/test/dashboard", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"recentAnnouncements": []gin.H{
				{"id": "1", "title": "Welcome to the Portal", "content": "Welcome to our student portal!", "date": "2024-01-15"},
				{"id": "2", "title": "Assignment Due", "content": "Don't forget your assignment is due tomorrow", "date": "2024-01-16"},
			},
			"upcomingAssignments": []gin.H{
				{"id": "1", "title": "Math Homework", "description": "Complete exercises 1-10", "dueDate": "2024-01-20"},
				{"id": "2", "title": "Science Project", "description": "Build a volcano model", "dueDate": "2024-01-25"},
			},
		})
	})

	router.GET("/test/assignments/:courseId", func(c *gin.Context) {
		courseId := c.Param("courseId")
		c.JSON(200, []gin.H{
			{"id": "1", "title": "Assignment 1", "description": "First assignment for course " + courseId, "dueDate": "2024-01-20", "status": "pending"},
			{"id": "2", "title": "Assignment 2", "description": "Second assignment for course " + courseId, "dueDate": "2024-01-25", "status": "submitted"},
		})
	})

	router.GET("/test/quizzes/:courseId", func(c *gin.Context) {
		courseId := c.Param("courseId")
		c.JSON(200, []gin.H{
			{"id": "1", "title": "Quiz 1", "description": "First quiz for course " + courseId, "questions": []gin.H{
				{"id": "1", "question": "What is 2+2?", "type": "multiple_choice", "options": []string{"3", "4", "5", "6"}},
			}},
		})
	})

	router.GET("/test/materials/:courseId", func(c *gin.Context) {
		courseId := c.Param("courseId")
		c.JSON(200, []gin.H{
			{"id": "1", "title": "Lecture Notes for " + courseId, "type": "pdf", "url": "https://example.com/notes.pdf"},
			{"id": "2", "title": "Video Tutorial for " + courseId, "type": "video", "url": "https://example.com/tutorial.mp4"},
		})
	})
}

// setupFileRoutes sets up file upload/download routes
func setupFileRoutes(router *gin.Engine, database *gorm.DB) {
	router.POST("/files/upload", func(c *gin.Context) {
		handlers.HandleFileUpload(c, database)
	})
	router.GET("/files/:fileId/download", func(c *gin.Context) {
		handlers.HandleFileDownload(c, database)
	})
	router.GET("/files/:fileId/info", func(c *gin.Context) {
		handlers.HandleFileInfo(c, database)
	})
	router.DELETE("/files/:fileId", func(c *gin.Context) {
		handlers.HandleFileDelete(c, database)
	})
}

// setupCourseRoutes sets up course management routes
func setupCourseRoutes(router *gin.Engine, database *gorm.DB) {
	router.POST("/courses", func(c *gin.Context) {
		handlers.HandleCreateCourse(c, database)
	})
	router.PUT("/courses/:courseId", func(c *gin.Context) {
		handlers.HandleUpdateCourse(c, database)
	})
	router.DELETE("/courses/:courseId", func(c *gin.Context) {
		handlers.HandleDeleteCourse(c, database)
	})
}
