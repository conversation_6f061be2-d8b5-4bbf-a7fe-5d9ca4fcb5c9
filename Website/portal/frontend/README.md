# 🎓 Student Portal Frontend

A complete frontend application for the Student Portal backend system.

## 🚀 Quick Start

### 1. Start the Frontend Server

```bash
cd /Users/<USER>/goCode/Website/portal/frontend
python3 server.py
```

### 2. Open in Browser

Navigate to: **http://localhost:3000**

### 3. Login

Use any credentials (the frontend will work with your backend authentication system):
- Username: `testuser123`
- Password: `password123`

## 📱 Features

### ✅ **Implemented Features**

- **🏠 Dashboard**: Overview with announcements and upcoming assignments
- **📚 Course Management**: View and create courses
- **📁 File Upload**: Drag & drop file upload with download/delete
- **👤 Profile Management**: User profile editing
- **🔐 Authentication**: Login/logout with JWT tokens
- **📱 Responsive Design**: Works on desktop and mobile
- **🎨 Modern UI**: Clean, professional interface

### 🎯 **Core Functionality**

#### **Course Management**
- View all enrolled courses
- Create new courses (with form validation)
- Course details with instructor information
- Real-time course statistics

#### **File Management**
- Drag & drop file upload
- File type validation (PDF, images, documents)
- File size validation (10MB max)
- Download uploaded files
- Delete files with confirmation
- File metadata display

#### **Dashboard**
- Recent announcements display
- Upcoming assignments overview
- Quick statistics (courses, assignments, files)
- Real-time data updates

#### **User Interface**
- Modern gradient design
- Smooth animations and transitions
- Toast notifications for user feedback
- Loading states and error handling
- Mobile-responsive layout

## 🔧 **Backend Integration**

The frontend integrates with these backend endpoints:

### **Authentication**
- `POST /login` - User authentication
- JWT token storage and management

### **Courses**
- `GET /courses` - List all courses
- `POST /courses` - Create new course
- `PUT /courses/{id}` - Update course
- `DELETE /courses/{id}` - Delete course

### **Files**
- `POST /files/upload` - Upload files
- `GET /files/{id}/download` - Download files
- `GET /files/{id}/info` - Get file metadata
- `DELETE /files/{id}` - Delete files

### **Dashboard**
- `GET /dashboard` - Dashboard data

## 🎨 **UI Components**

### **Navigation**
- Fixed top navigation with gradient background
- Active page highlighting
- User information display
- Logout functionality

### **Cards & Layouts**
- Responsive grid layouts
- Hover effects and animations
- Clean card-based design
- Professional color scheme

### **Forms & Modals**
- Form validation and error handling
- Modal dialogs for actions
- File upload with drag & drop
- Toast notifications

### **Responsive Design**
- Mobile-first approach
- Flexible grid systems
- Touch-friendly interface
- Optimized for all screen sizes

## 📂 **File Structure**

```
frontend/
├── index.html          # Main HTML structure
├── styles.css          # Complete CSS styling
├── app.js             # JavaScript application logic
├── server.py          # Python HTTP server
└── README.md          # This file
```

## 🔧 **Configuration**

### **API Endpoints**
Edit `app.js` to change backend URLs:

```javascript
this.baseUrl = 'http://localhost:8081';  // Portal backend
this.loginUrl = 'http://localhost:8080'; // Login backend
```

### **Server Port**
Edit `server.py` to change frontend port:

```python
PORT = 3000  # Change this to your preferred port
```

## 🧪 **Testing the Frontend**

### **1. Course Management**
1. Click "Courses" in navigation
2. Click "Add Course" button
3. Fill in course details
4. Submit form
5. ✅ New course should appear in the grid

### **2. File Upload**
1. Click "Files" in navigation
2. Drag a file to the upload area OR click "Upload File"
3. Select a file (PDF, image, or document)
4. ✅ File should upload and appear in the list
5. Test download and delete functionality

### **3. Dashboard**
1. Click "Dashboard" in navigation
2. ✅ Should show course statistics and recent activity

## 🚨 **Troubleshooting**

### **Frontend Won't Load**
```bash
# Check if server is running
curl http://localhost:3000

# Restart server
cd /Users/<USER>/goCode/Website/portal/frontend
python3 server.py
```

### **API Errors**
- Ensure portal backend is running on port 8081
- Ensure login backend is running on port 8080
- Check browser console for error messages

### **CORS Issues**
- The Python server includes CORS headers
- Make sure both frontend and backend servers are running

### **Authentication Issues**
- Clear browser localStorage: `localStorage.clear()`
- Try logging in again
- Check that JWT tokens are being stored

## 🎯 **Next Steps**

### **Immediate Improvements**
1. **Add Assignment Management** - Create, view, submit assignments
2. **Add Grade Viewing** - Display student grades and progress
3. **Add Schedule/Calendar** - Class schedules and events
4. **Add Messaging** - Student-teacher communication

### **Advanced Features**
1. **Real-time Notifications** - WebSocket integration
2. **Offline Support** - Service worker implementation
3. **Advanced File Management** - Folders, sharing, previews
4. **Dark Mode** - Theme switching
5. **Mobile App** - Progressive Web App (PWA)

## 📱 **Mobile Support**

The frontend is fully responsive and works on:
- 📱 Mobile phones (iOS/Android)
- 📱 Tablets
- 💻 Desktop computers
- 🖥️ Large screens

## 🎨 **Customization**

### **Colors & Themes**
Edit `styles.css` to customize:
- Primary colors (gradients)
- Card styling
- Button designs
- Typography

### **Layout & Components**
Edit `index.html` and `styles.css` to:
- Add new pages
- Modify navigation
- Change grid layouts
- Add new components

---

**🎉 Your complete Student Portal frontend is ready to use!**

Open **http://localhost:3000** in your browser to get started.
