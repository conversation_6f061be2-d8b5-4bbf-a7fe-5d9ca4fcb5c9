<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Portal</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-graduation-cap"></i>
                <span>Student Portal</span>
            </div>
            <div class="nav-menu" id="nav-menu">
                <a href="#dashboard" class="nav-link active" data-page="dashboard">
                    <i class="fas fa-home"></i> Dashboard
                </a>
                <a href="#courses" class="nav-link" data-page="courses">
                    <i class="fas fa-book"></i> Courses
                </a>
                <a href="#assignments" class="nav-link" data-page="assignments">
                    <i class="fas fa-tasks"></i> Assignments
                </a>
                <a href="#files" class="nav-link" data-page="files">
                    <i class="fas fa-folder"></i> Files
                </a>
                <a href="#profile" class="nav-link" data-page="profile">
                    <i class="fas fa-user"></i> Profile
                </a>
            </div>
            <div class="nav-auth">
                <span id="user-name">Loading...</span>
                <button id="logout-btn" class="btn btn-outline">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Dashboard Page -->
        <div id="dashboard-page" class="page active">
            <div class="page-header">
                <h1><i class="fas fa-home"></i> Dashboard</h1>
                <p>Welcome to your student portal</p>
            </div>
            
            <div class="dashboard-grid">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-bell"></i> Recent Announcements</h3>
                    </div>
                    <div class="card-body" id="announcements-list">
                        <div class="loading">Loading announcements...</div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-clock"></i> Upcoming Assignments</h3>
                    </div>
                    <div class="card-body" id="upcoming-assignments">
                        <div class="loading">Loading assignments...</div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-chart-line"></i> Quick Stats</h3>
                    </div>
                    <div class="card-body">
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number" id="total-courses">-</div>
                                <div class="stat-label">Enrolled Courses</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="pending-assignments">-</div>
                                <div class="stat-label">Pending Assignments</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="uploaded-files">-</div>
                                <div class="stat-label">Uploaded Files</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Courses Page -->
        <div id="courses-page" class="page">
            <div class="page-header">
                <h1><i class="fas fa-book"></i> My Courses</h1>
                <button id="add-course-btn" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add Course
                </button>
            </div>
            
            <div class="courses-grid" id="courses-grid">
                <div class="loading">Loading courses...</div>
            </div>
        </div>

        <!-- Assignments Page -->
        <div id="assignments-page" class="page">
            <div class="page-header">
                <h1><i class="fas fa-tasks"></i> Assignments</h1>
                <div class="page-filters">
                    <select id="assignment-filter" class="form-select">
                        <option value="all">All Assignments</option>
                        <option value="pending">Pending</option>
                        <option value="completed">Completed</option>
                        <option value="overdue">Overdue</option>
                    </select>
                </div>
            </div>
            
            <div class="assignments-list" id="assignments-list">
                <div class="loading">Loading assignments...</div>
            </div>
        </div>

        <!-- Files Page -->
        <div id="files-page" class="page">
            <div class="page-header">
                <h1><i class="fas fa-folder"></i> My Files</h1>
                <button id="upload-file-btn" class="btn btn-primary">
                    <i class="fas fa-upload"></i> Upload File
                </button>
            </div>
            
            <div class="files-container">
                <div class="upload-area" id="upload-area">
                    <div class="upload-content">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <p>Drag and drop files here or click to browse</p>
                        <input type="file" id="file-input" multiple accept=".pdf,.doc,.docx,.txt,.jpg,.png,.gif">
                    </div>
                </div>
                
                <div class="files-list" id="files-list">
                    <div class="loading">Loading files...</div>
                </div>
            </div>
        </div>

        <!-- Profile Page -->
        <div id="profile-page" class="page">
            <div class="page-header">
                <h1><i class="fas fa-user"></i> My Profile</h1>
            </div>
            
            <div class="profile-container">
                <div class="card">
                    <div class="card-header">
                        <h3>Personal Information</h3>
                    </div>
                    <div class="card-body">
                        <form id="profile-form">
                            <div class="form-group">
                                <label for="first-name">First Name</label>
                                <input type="text" id="first-name" class="form-input" placeholder="Enter first name">
                            </div>
                            <div class="form-group">
                                <label for="last-name">Last Name</label>
                                <input type="text" id="last-name" class="form-input" placeholder="Enter last name">
                            </div>
                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" id="email" class="form-input" placeholder="Enter email">
                            </div>
                            <div class="form-group">
                                <label for="username">Username</label>
                                <input type="text" id="username" class="form-input" placeholder="Enter username">
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Changes
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Modals -->
    <!-- Add Course Modal -->
    <div id="add-course-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add New Course</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="add-course-form">
                    <div class="form-group">
                        <label for="course-name">Course Name</label>
                        <input type="text" id="course-name" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label for="course-description">Description</label>
                        <textarea id="course-description" class="form-textarea"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="course-instructor">Instructor</label>
                        <input type="text" id="course-instructor" class="form-input">
                    </div>
                    <div class="form-group">
                        <label for="course-schedule">Schedule</label>
                        <input type="text" id="course-schedule" class="form-input" placeholder="e.g., Mon/Wed/Fri 10:00-11:30">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('add-course-modal')">Cancel</button>
                <button type="submit" form="add-course-form" class="btn btn-primary">Create Course</button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading...</p>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container"></div>

    <script src="app.js"></script>
</body>
</html>
