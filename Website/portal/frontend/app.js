// Portal Frontend Application
class PortalApp {
    constructor() {
        this.baseUrl = 'http://localhost:8081';
        this.loginUrl = 'http://localhost:8080';
        this.currentUser = null;
        this.authToken = null;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkAuthentication();
        this.loadInitialData();
    }

    // Authentication
    checkAuthentication() {
        const token = localStorage.getItem('authToken');
        const user = localStorage.getItem('currentUser');
        
        if (token && user) {
            this.authToken = token;
            this.currentUser = JSON.parse(user);
            this.updateUserDisplay();
        } else {
            this.redirectToLogin();
        }
    }

    updateUserDisplay() {
        const userNameEl = document.getElementById('user-name');
        if (this.currentUser) {
            userNameEl.textContent = this.currentUser.username || 'Student';
        }
    }

    redirectToLogin() {
        // For now, show a simple login form
        this.showLoginModal();
    }

    showLoginModal() {
        const loginHtml = `
            <div class="modal active" id="login-modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Login to Portal</h3>
                    </div>
                    <div class="modal-body">
                        <form id="login-form">
                            <div class="form-group">
                                <label for="login-username">Username</label>
                                <input type="text" id="login-username" class="form-input" required>
                            </div>
                            <div class="form-group">
                                <label for="login-password">Password</label>
                                <input type="password" id="login-password" class="form-input" required>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" form="login-form" class="btn btn-primary">Login</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', loginHtml);
        
        document.getElementById('login-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });
    }

    async handleLogin() {
        const username = document.getElementById('login-username').value;
        const password = document.getElementById('login-password').value;
        
        try {
            this.showLoading(true);
            
            const response = await fetch(`${this.loginUrl}/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username, password })
            });
            
            if (response.ok) {
                const data = await response.json();
                this.authToken = data.token;
                this.currentUser = { username };
                
                localStorage.setItem('authToken', this.authToken);
                localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
                
                document.getElementById('login-modal').remove();
                this.updateUserDisplay();
                this.loadInitialData();
                this.showToast('Login successful!', 'success');
            } else {
                this.showToast('Login failed. Please check your credentials.', 'error');
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showToast('Login failed. Please try again.', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    logout() {
        localStorage.removeItem('authToken');
        localStorage.removeItem('currentUser');
        this.authToken = null;
        this.currentUser = null;
        this.redirectToLogin();
    }

    // API Calls
    async apiCall(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                ...(this.authToken && { 'Authorization': `Bearer ${this.authToken}` })
            }
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        if (finalOptions.body && typeof finalOptions.body === 'object') {
            finalOptions.body = JSON.stringify(finalOptions.body);
        }
        
        try {
            const response = await fetch(url, finalOptions);
            
            if (response.status === 401) {
                this.logout();
                return null;
            }
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API call failed:', error);
            this.showToast(`API Error: ${error.message}`, 'error');
            return null;
        }
    }

    // Event Listeners
    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.dataset.page;
                this.showPage(page);
            });
        });

        // Logout
        document.getElementById('logout-btn').addEventListener('click', () => {
            this.logout();
        });

        // Add Course
        document.getElementById('add-course-btn').addEventListener('click', () => {
            this.openModal('add-course-modal');
        });

        // Course Form
        document.getElementById('add-course-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleAddCourse();
        });

        // File Upload
        document.getElementById('upload-file-btn').addEventListener('click', () => {
            document.getElementById('file-input').click();
        });

        document.getElementById('file-input').addEventListener('change', (e) => {
            this.handleFileUpload(e.target.files);
        });

        // Drag and Drop
        const uploadArea = document.getElementById('upload-area');
        uploadArea.addEventListener('click', () => {
            document.getElementById('file-input').click();
        });

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            this.handleFileUpload(e.dataTransfer.files);
        });

        // Modal close buttons
        document.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const modal = e.target.closest('.modal');
                this.closeModal(modal.id);
            });
        });
    }

    // Page Navigation
    showPage(pageId) {
        // Hide all pages
        document.querySelectorAll('.page').forEach(page => {
            page.classList.remove('active');
        });

        // Show selected page
        document.getElementById(`${pageId}-page`).classList.add('active');

        // Update navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[data-page="${pageId}"]`).classList.add('active');

        // Load page data
        this.loadPageData(pageId);
    }

    async loadPageData(pageId) {
        switch (pageId) {
            case 'dashboard':
                await this.loadDashboard();
                break;
            case 'courses':
                await this.loadCourses();
                break;
            case 'assignments':
                await this.loadAssignments();
                break;
            case 'files':
                await this.loadFiles();
                break;
            case 'profile':
                await this.loadProfile();
                break;
        }
    }

    async loadInitialData() {
        await this.loadDashboard();
    }

    // Dashboard
    async loadDashboard() {
        try {
            const dashboardData = await this.apiCall('/dashboard');
            if (dashboardData) {
                this.renderDashboard(dashboardData);
            }
        } catch (error) {
            console.error('Failed to load dashboard:', error);
        }
    }

    renderDashboard(data) {
        // Render announcements
        const announcementsEl = document.getElementById('announcements-list');
        if (data.recentAnnouncements && data.recentAnnouncements.length > 0) {
            announcementsEl.innerHTML = data.recentAnnouncements.map(announcement => `
                <div class="announcement-item">
                    <h4>${announcement.title}</h4>
                    <p>${announcement.content}</p>
                    <small>${new Date(announcement.date).toLocaleDateString()}</small>
                </div>
            `).join('');
        } else {
            announcementsEl.innerHTML = '<p>No recent announcements</p>';
        }

        // Render upcoming assignments
        const assignmentsEl = document.getElementById('upcoming-assignments');
        if (data.upcomingAssignments && data.upcomingAssignments.length > 0) {
            assignmentsEl.innerHTML = data.upcomingAssignments.map(assignment => `
                <div class="assignment-item">
                    <h4>${assignment.title}</h4>
                    <p>Due: ${new Date(assignment.dueDate).toLocaleDateString()}</p>
                </div>
            `).join('');
        } else {
            assignmentsEl.innerHTML = '<p>No upcoming assignments</p>';
        }
    }

    // Courses
    async loadCourses() {
        try {
            const courses = await this.apiCall('/courses');
            if (courses) {
                this.renderCourses(courses);
            }
        } catch (error) {
            console.error('Failed to load courses:', error);
        }
    }

    renderCourses(courses) {
        const coursesGrid = document.getElementById('courses-grid');
        
        if (courses && courses.length > 0) {
            coursesGrid.innerHTML = courses.map(course => `
                <div class="course-card">
                    <div class="course-title">${course.name}</div>
                    <div class="course-description">${course.description || 'No description available'}</div>
                    <div class="course-meta">
                        <span class="course-instructor">
                            <i class="fas fa-user"></i> ${course.instructor || 'TBA'}
                        </span>
                        <div class="course-actions">
                            <button class="btn btn-primary btn-sm" onclick="app.viewCourse('${course.id}')">
                                <i class="fas fa-eye"></i> View
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        } else {
            coursesGrid.innerHTML = '<div class="loading">No courses found</div>';
        }
        
        // Update stats
        document.getElementById('total-courses').textContent = courses ? courses.length : 0;
    }

    async handleAddCourse() {
        const formData = {
            name: document.getElementById('course-name').value,
            description: document.getElementById('course-description').value,
            instructor: document.getElementById('course-instructor').value,
            schedule: document.getElementById('course-schedule').value
        };

        try {
            this.showLoading(true);
            const result = await this.apiCall('/courses', {
                method: 'POST',
                body: formData
            });

            if (result) {
                this.showToast('Course created successfully!', 'success');
                this.closeModal('add-course-modal');
                document.getElementById('add-course-form').reset();
                await this.loadCourses();
            }
        } catch (error) {
            this.showToast('Failed to create course', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // Utility Functions
    openModal(modalId) {
        document.getElementById(modalId).classList.add('active');
    }

    closeModal(modalId) {
        document.getElementById(modalId).classList.remove('active');
    }

    showLoading(show) {
        const overlay = document.getElementById('loading-overlay');
        if (show) {
            overlay.classList.add('active');
        } else {
            overlay.classList.remove('active');
        }
    }

    showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;
        
        document.getElementById('toast-container').appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 5000);
    }

    // Files
    async loadFiles() {
        // Since we don't have a list files endpoint, we'll show upload interface
        const filesList = document.getElementById('files-list');
        filesList.innerHTML = '<div class="loading">Upload files to see them here</div>';
    }

    async handleFileUpload(files) {
        if (!files || files.length === 0) return;

        for (const file of files) {
            try {
                this.showLoading(true);

                const formData = new FormData();
                formData.append('file', file);

                const response = await fetch(`${this.baseUrl}/files/upload`, {
                    method: 'POST',
                    headers: {
                        ...(this.authToken && { 'Authorization': `Bearer ${this.authToken}` })
                    },
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();
                    this.showToast(`File "${file.name}" uploaded successfully!`, 'success');
                    this.addFileToList(result);
                } else {
                    this.showToast(`Failed to upload "${file.name}"`, 'error');
                }
            } catch (error) {
                console.error('File upload error:', error);
                this.showToast(`Error uploading "${file.name}"`, 'error');
            }
        }

        this.showLoading(false);
        document.getElementById('file-input').value = '';
    }

    addFileToList(fileData) {
        const filesList = document.getElementById('files-list');

        // Remove loading message if it exists
        const loadingEl = filesList.querySelector('.loading');
        if (loadingEl) {
            loadingEl.remove();
        }

        const fileItem = document.createElement('div');
        fileItem.className = 'file-item';
        fileItem.innerHTML = `
            <div class="file-info">
                <div class="file-icon">
                    <i class="fas fa-file"></i>
                </div>
                <div class="file-details">
                    <h4>${fileData.filename}</h4>
                    <p>${this.formatFileSize(fileData.size)} • Uploaded just now</p>
                </div>
            </div>
            <div class="file-actions">
                <button class="btn btn-primary btn-sm" onclick="app.downloadFile('${fileData.id}')">
                    <i class="fas fa-download"></i> Download
                </button>
                <button class="btn btn-danger btn-sm" onclick="app.deleteFile('${fileData.id}', this)">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        `;

        filesList.appendChild(fileItem);
    }

    async downloadFile(fileId) {
        try {
            const response = await fetch(`${this.baseUrl}/files/${fileId}/download`, {
                headers: {
                    ...(this.authToken && { 'Authorization': `Bearer ${this.authToken}` })
                }
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = response.headers.get('Content-Disposition')?.split('filename=')[1] || 'download';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                this.showToast('File downloaded successfully!', 'success');
            } else {
                this.showToast('Failed to download file', 'error');
            }
        } catch (error) {
            console.error('Download error:', error);
            this.showToast('Error downloading file', 'error');
        }
    }

    async deleteFile(fileId, buttonElement) {
        if (!confirm('Are you sure you want to delete this file?')) return;

        try {
            const response = await fetch(`${this.baseUrl}/files/${fileId}`, {
                method: 'DELETE',
                headers: {
                    ...(this.authToken && { 'Authorization': `Bearer ${this.authToken}` })
                }
            });

            if (response.ok) {
                buttonElement.closest('.file-item').remove();
                this.showToast('File deleted successfully!', 'success');
            } else {
                this.showToast('Failed to delete file', 'error');
            }
        } catch (error) {
            console.error('Delete error:', error);
            this.showToast('Error deleting file', 'error');
        }
    }

    // Assignments
    async loadAssignments() {
        const assignmentsList = document.getElementById('assignments-list');
        assignmentsList.innerHTML = '<div class="loading">No assignments available</div>';
    }

    // Profile
    async loadProfile() {
        if (this.currentUser) {
            document.getElementById('username').value = this.currentUser.username || '';
            document.getElementById('email').value = this.currentUser.email || '';
            document.getElementById('first-name').value = this.currentUser.firstName || '';
            document.getElementById('last-name').value = this.currentUser.lastName || '';
        }
    }

    viewCourse(courseId) {
        this.showToast(`Course details for ${courseId} - Feature coming soon!`, 'info');
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// Global functions for inline event handlers
window.openModal = (modalId) => app.openModal(modalId);
window.closeModal = (modalId) => app.closeModal(modalId);

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new PortalApp();
});
