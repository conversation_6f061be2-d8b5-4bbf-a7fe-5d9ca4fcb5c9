# Build Go binaries
load("@rules_go//go:def.bzl", "go_binary", "go_library", "go_test")

go_binary(
    name = "portal_server",
    srcs = ["main.go"],
    deps = [":portal_lib"],
)

go_library(
    name = "portal_lib",
    srcs = glob(["**/*.go"]),
    importpath = "github.com/yourorg/portal",
    visibility = ["//visibility:public"],
)

go_test(
    name = "persistence_test",
    srcs = ["internal/persistence/persistence_test.go"],
    embed = [":portal_lib"],
    deps = [
        "@com_github_stretchr_testify//assert",
        "@gorm_io_gorm//:go_default_library",
        "@gorm_io_driver_sqlite//:go_default_library",
    ],
)
