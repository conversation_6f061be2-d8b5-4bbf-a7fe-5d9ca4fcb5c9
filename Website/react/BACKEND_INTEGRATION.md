# 🔗 Backend Integration Guide

This document explains in detail how the React frontend connects to your Go backend API.

## 🏗️ Architecture Overview

```
React Frontend (Port 3000)
        ↓ HTTP Requests
Go Backend API (https://stemblock-login-gljgs.ondigitalocean.app)
        ↓ Database Operations
PostgreSQL Database
```

## 🔧 API Configuration

### Base Configuration (`src/config/api.js`)

```javascript
export const API_CONFIG = {
  BASE_URL: 'https://stemblock-login-gljgs.ondigitalocean.app',
  FRONTEND_URL: 'https://www.stemblock.ca',
  
  ENDPOINTS: {
    LOGIN: '/login',
    REGISTER: '/register',
    FORGOT_PASSWORD: '/forgot-password',
    RESET_PASSWORD: '/reset-password',
    VERIFY_EMAIL: '/verify-email',
    DASHBOARD_AUTH: '/dashboard-auth',
    RESEND_VERIFICATION: '/resend-verification',
    CHECK_EMAIL: '/check-email',
  },

  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
};
```

### Axios Client Setup (`src/services/api.js`)

```javascript
const apiClient = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  withCredentials: true, // 🔑 CRITICAL: Enables JWT cookies
  headers: {
    'Content-Type': 'application/json',
  },
});
```

## 🔐 Authentication Flow

### 1. User Registration

**Frontend → Backend:**
```javascript
// src/pages/auth/Register.jsx
const response = await apiService.register({
  username: formData.username,
  email: formData.email,
  password: formData.password
});
```

**HTTP Request:**
```http
POST https://stemblock-login-gljgs.ondigitalocean.app/register
Content-Type: application/json
Credentials: include

{
  "username": "john_doe",
  "email": "<EMAIL>",
  "password": "SecurePass123!"
}
```

**Backend Response:**
```json
{
  "message": "Registration successful! Please check your email to verify your account.",
  "user_id": 123
}
```

### 2. User Login

**Frontend → Backend:**
```javascript
// src/contexts/AuthContext.js
const response = await apiService.login({
  username: email, // Backend expects username field
  password: password
});
```

**HTTP Request:**
```http
POST https://stemblock-login-gljgs.ondigitalocean.app/login
Content-Type: application/json
Credentials: include

{
  "username": "<EMAIL>",
  "password": "SecurePass123!"
}
```

**Backend Response:**
```json
{
  "message": "Login successful",
  "user": {
    "id": 123,
    "username": "john_doe",
    "email": "<EMAIL>",
    "role": "student"
  }
}
```

**🍪 JWT Cookie Set:**
```http
Set-Cookie: jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...; 
           HttpOnly; 
           Secure; 
           SameSite=None; 
           Path=/; 
           Max-Age=86400
```

### 3. Authentication Check

**Frontend → Backend:**
```javascript
// src/contexts/AuthContext.js
const response = await apiService.checkAuth();
```

**HTTP Request:**
```http
GET https://stemblock-login-gljgs.ondigitalocean.app/dashboard-auth
Credentials: include
Cookie: jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Backend Response (Authenticated):**
```json
{
  "user": {
    "id": 123,
    "username": "john_doe",
    "email": "<EMAIL>",
    "role": "student"
  }
}
```

**Backend Response (Not Authenticated):**
```http
HTTP/1.1 401 Unauthorized
{
  "error": "Unauthorized"
}
```

## 🔄 Request/Response Cycle

### 1. React Component Makes Request

```javascript
// src/pages/auth/Login.jsx
const Login = () => {
  const { login } = useAuth(); // React Context
  
  const onSubmit = async (formData) => {
    try {
      await login({
        username: formData.email,
        password: formData.password,
      });
      navigate('/dashboard');
    } catch (error) {
      toast.error('Login failed');
    }
  };
  
  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      {/* Form JSX */}
    </form>
  );
};
```

### 2. AuthContext Handles Request

```javascript
// src/contexts/AuthContext.js
const login = useCallback(async (credentials) => {
  try {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    // API call through service layer
    const response = await apiService.login(credentials);
    
    // Update React state
    queryClient.invalidateQueries('auth-status');
    
    toast.success('Login successful!');
    return response.data;
  } catch (error) {
    dispatch({ type: 'SET_ERROR', payload: error.message });
    toast.error(error.message);
    throw error;
  }
}, [queryClient]);
```

### 3. API Service Makes HTTP Request

```javascript
// src/services/api.js
export const apiService = {
  login: (credentials) => 
    apiClient.post(API_CONFIG.ENDPOINTS.LOGIN, credentials),
};

// Axios interceptor handles the actual HTTP request
apiClient.interceptors.request.use((config) => {
  // Request is sent with credentials: 'include'
  return config;
});
```

### 4. Backend Processes Request

```go
// Your Go backend (simplified)
func LoginHandler(w http.ResponseWriter, r *http.Request) {
    // Parse request body
    var loginReq LoginRequest
    json.NewDecoder(r.Body).Decode(&loginReq)
    
    // Validate credentials
    user, err := validateUser(loginReq.Username, loginReq.Password)
    if err != nil {
        http.Error(w, "Invalid credentials", 401)
        return
    }
    
    // Generate JWT token
    token, err := generateJWT(user.ID)
    if err != nil {
        http.Error(w, "Token generation failed", 500)
        return
    }
    
    // Set HTTP-only cookie
    http.SetCookie(w, &http.Cookie{
        Name:     "jwt",
        Value:    token,
        HttpOnly: true,
        Secure:   true,
        SameSite: http.SameSiteNoneMode,
        Path:     "/",
        MaxAge:   86400, // 24 hours
    })
    
    // Return user data
    json.NewEncoder(w).Encode(LoginResponse{
        Message: "Login successful",
        User:    user,
    })
}
```

### 5. Frontend Receives Response

```javascript
// src/services/api.js
apiClient.interceptors.response.use(
  (response) => {
    // Success response
    return response;
  },
  (error) => {
    // Error handling
    if (error.response?.status === 401) {
      // Redirect to login
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);
```

## 🛡️ Security Implementation

### 1. CORS Configuration

**Frontend Origin:**
```javascript
// React app runs on http://localhost:3000 (development)
// or https://www.stemblock.ca (production)
```

**Backend CORS Headers:**
```go
// Your Go backend should set these headers:
w.Header().Set("Access-Control-Allow-Origin", "https://www.stemblock.ca")
w.Header().Set("Access-Control-Allow-Credentials", "true")
w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
```

### 2. JWT Cookie Security

**Cookie Attributes:**
- `HttpOnly`: Prevents JavaScript access (XSS protection)
- `Secure`: Only sent over HTTPS
- `SameSite=None`: Allows cross-origin requests
- `Path=/`: Available for all routes
- `Max-Age=86400`: 24-hour expiration

### 3. Request Authentication

**Every Protected Request:**
```javascript
// Automatic cookie inclusion
const response = await fetch('/dashboard-auth', {
  credentials: 'include' // Sends JWT cookie automatically
});
```

## 🔄 Session Management

### 1. Automatic Auth Checking

```javascript
// src/contexts/AuthContext.js
const { data: authData } = useQuery(
  'auth-status',
  () => apiService.checkAuth(),
  {
    retry: false,
    refetchOnWindowFocus: true,
    refetchInterval: 5 * 60 * 1000, // Check every 5 minutes
    onSuccess: (response) => {
      dispatch({ type: 'SET_USER', payload: response.data.user });
    },
    onError: (error) => {
      if (error.response?.status === 401) {
        dispatch({ type: 'LOGOUT' });
      }
    },
  }
);
```

### 2. Route Protection

```javascript
// src/components/auth/ProtectedRoute.jsx
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  const location = useLocation();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!isAuthenticated) {
    return (
      <Navigate 
        to={`/login?redirect=${encodeURIComponent(location.pathname)}`}
        replace 
      />
    );
  }

  return children || <Outlet />;
};
```

### 3. Session Timeout Warning

```javascript
// src/contexts/AuthContext.js
useEffect(() => {
  let warningTimer;
  
  if (isAuthenticated) {
    warningTimer = setTimeout(() => {
      const shouldExtend = window.confirm(
        'Your session will expire in 5 minutes. Extend it?'
      );
      
      if (shouldExtend) {
        queryClient.invalidateQueries('auth-status');
      }
    }, 25 * 60 * 1000); // 25 minutes (5 min before 30 min expiry)
  }

  return () => clearTimeout(warningTimer);
}, [isAuthenticated, queryClient]);
```

## 🚨 Error Handling

### 1. Network Errors

```javascript
// src/services/api.js
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.code === 'ECONNABORTED') {
      error.message = 'Request timed out. Please try again.';
    } else if (!error.response) {
      error.message = 'Unable to connect to server.';
    }
    
    return Promise.reject(error);
  }
);
```

### 2. Validation Errors

```javascript
// src/utils/validation.js
export const formatValidationErrors = (errors) => {
  if (!errors || !Array.isArray(errors)) return {};
  
  return errors.reduce((acc, error) => {
    acc[error.field] = error.message;
    return acc;
  }, {});
};
```

### 3. Global Error Boundary

```javascript
// src/components/common/ErrorBoundary.jsx
class ErrorBoundary extends React.Component {
  componentDidCatch(error, errorInfo) {
    console.error('Error Boundary caught:', error, errorInfo);
    // Could send to error reporting service
  }
  
  render() {
    if (this.state.hasError) {
      return <ErrorFallback />;
    }
    return this.props.children;
  }
}
```

## 📊 Complete Data Flow Diagram

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React User    │    │  React Frontend │    │   Go Backend    │
│   Interface     │    │   (Port 3000)   │    │ (DigitalOcean)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │ 1. User clicks login  │                       │
         ├──────────────────────►│                       │
         │                       │ 2. Form submission    │
         │                       │    (useState/useForm) │
         │                       │                       │
         │                       │ 3. AuthContext.login()│
         │                       │    (useContext)       │
         │                       │                       │
         │                       │ 4. apiService.login() │
         │                       │    (Axios HTTP POST)  │
         │                       ├──────────────────────►│
         │                       │                       │
         │                       │ 5. JWT Cookie Set     │
         │                       │    + User Data        │
         │                       │◄──────────────────────┤
         │                       │                       │
         │                       │ 6. React Query        │
         │                       │    invalidation       │
         │                       │                       │
         │                       │ 7. State update       │
         │                       │    (useReducer)       │
         │                       │                       │
         │ 8. UI re-render       │                       │
         │    (React JSX)        │                       │
         │◄──────────────────────┤                       │
         │                       │                       │
         │ 9. Navigate to        │                       │
         │    dashboard          │                       │
         │◄──────────────────────┤                       │
```

## 🔄 Authentication State Management

```
React Component Tree:
├── App.jsx
│   ├── AuthProvider (Context)
│   │   ├── NetworkProvider (Context)
│   │   │   ├── QueryClientProvider (React Query)
│   │   │   │   ├── BrowserRouter (React Router)
│   │   │   │   │   ├── Routes
│   │   │   │   │   │   ├── ProtectedRoute
│   │   │   │   │   │   │   └── Dashboard.jsx
│   │   │   │   │   │   ├── PublicRoute
│   │   │   │   │   │   │   └── Login.jsx
│   │   │   │   │   │   └── Layout.jsx
│   │   │   │   │   │       ├── Header.jsx (uses useAuth)
│   │   │   │   │   │       ├── Outlet
│   │   │   │   │   │       └── Footer.jsx

State Flow:
AuthContext → useAuth hook → Components → UI Updates
```

## 🛠️ Technical Implementation Details

### 1. **React Hooks Integration**

```javascript
// Login component using React hooks
const Login = () => {
  // React Router hook
  const navigate = useNavigate();
  const location = useLocation();

  // Custom hook (React Context)
  const { login } = useAuth();

  // Custom form hook
  const { values, errors, handleSubmit } = useForm(
    { email: '', password: '' },
    validationRules
  );

  // useCallback for performance
  const onSubmit = useCallback(async (formData) => {
    try {
      await login(formData);
      const redirect = new URLSearchParams(location.search).get('redirect');
      navigate(redirect || '/dashboard');
    } catch (error) {
      toast.error('Login failed');
    }
  }, [login, navigate, location]);

  // JSX with event handlers
  return (
    <form onSubmit={(e) => handleSubmit(onSubmit)(e)}>
      <input
        value={values.email}
        onChange={(e) => handleChange('email', e.target.value)}
      />
      <button type="submit">Login</button>
    </form>
  );
};
```

### 2. **Backend API Endpoints**

Your Go backend should handle these endpoints:

```go
// Authentication endpoints
POST /register          // User registration
POST /login             // User login (sets JWT cookie)
POST /forgot-password   // Password reset request
POST /reset-password    // Password reset with token
GET  /verify-email      // Email verification
GET  /dashboard-auth    // Check authentication status
POST /resend-verification // Resend verification email

// Protected endpoints (require JWT cookie)
GET  /dashboard-auth    // Get current user info
POST /logout           // Clear JWT cookie
```

### 3. **CORS Configuration Required**

Your Go backend needs these CORS settings:

```go
func enableCORS(w http.ResponseWriter, r *http.Request) {
    w.Header().Set("Access-Control-Allow-Origin", "http://localhost:3000") // Dev
    // w.Header().Set("Access-Control-Allow-Origin", "https://www.stemblock.ca") // Prod
    w.Header().Set("Access-Control-Allow-Credentials", "true")
    w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
    w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

    if r.Method == "OPTIONS" {
        w.WriteHeader(http.StatusOK)
        return
    }
}
```

## 📊 Data Flow Summary

1. **User Action** → React component event handler
2. **Component** → Calls AuthContext method (useContext)
3. **AuthContext** → Calls API service (useCallback)
4. **API Service** → Makes HTTP request with Axios
5. **Go Backend** → Processes request, validates JWT
6. **Database** → Stores/retrieves data
7. **Go Backend** → Returns JSON response + JWT cookie
8. **API Service** → Receives response
9. **AuthContext** → Updates React state (useReducer)
10. **Component** → Re-renders with new data (React JSX)

## 🔑 Key Integration Points

1. **JWT Cookies**: Automatic authentication via HTTP-only cookies
2. **CORS**: Cross-origin requests between React and Go
3. **React Query**: Automatic auth status checking and caching
4. **React Router**: Protected routes and redirects
5. **React Context**: Global authentication state
6. **Error Handling**: Network errors, validation, and user feedback
7. **Session Management**: Automatic logout and session warnings

This creates a complete, secure, and robust connection between your React frontend and Go backend! 🚀
