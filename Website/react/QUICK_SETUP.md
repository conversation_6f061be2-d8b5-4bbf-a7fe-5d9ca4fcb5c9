# 🚀 Quick Setup Guide

## 1. Install Dependencies

```bash
cd Website/react
npm install
```

## 2. Configure Environment

Create `.env` file:
```bash
cp .env.example .env
```

Edit `.env`:
```env
REACT_APP_API_URL=https://stemblock-login-gljgs.ondigitalocean.app
REACT_APP_FRONTEND_URL=https://www.stemblock.ca
REACT_APP_ENV=development
```

## 3. Update Backend CORS (Important!)

Your Go backend needs to allow requests from React:

```go
// Add this to your Go backend
func enableCORS(w http.ResponseWriter, r *http.Request) {
    // For development
    w.Header().Set("Access-Control-Allow-Origin", "http://localhost:3000")
    
    // For production (uncomment when deploying)
    // w.Header().Set("Access-Control-Allow-Origin", "https://www.stemblock.ca")
    
    w.<PERSON><PERSON>().Set("Access-Control-Allow-Credentials", "true")
    w.<PERSON><PERSON>().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
    w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
    
    if r.Method == "OPTIONS" {
        w.WriteHeader(http.StatusOK)
        return
    }
}

// Apply to all routes
func main() {
    http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
        enableCORS(w, r)
        // Your existing handler logic
    })
}
```

## 4. Start React App

```bash
npm start
```

Visit: `http://localhost:3000`

## 5. Test Integration

1. **Registration**: Go to `/register` and create an account
2. **Login**: Go to `/login` and sign in
3. **Dashboard**: Should redirect to `/dashboard` after login
4. **Protected Routes**: Try accessing `/dashboard` without login

## 6. React Examples

Visit these URLs to see React framework features:
- `/examples/counter` - useState, useEffect demo
- `/examples/todo` - Complex state management
- `/examples/react-dashboard` - All React hooks

## 🔧 Troubleshooting

### CORS Errors
If you see CORS errors in browser console:
1. Check your Go backend CORS configuration
2. Ensure `Access-Control-Allow-Credentials: true`
3. Verify the origin URL matches exactly

### Authentication Issues
If login doesn't work:
1. Check browser Network tab for 401/403 errors
2. Verify JWT cookie is being set
3. Check backend logs for authentication errors

### Network Errors
If requests fail:
1. Verify backend is running on correct URL
2. Check firewall/network settings
3. Test backend endpoints directly with curl/Postman

## 📱 Mobile Testing

The React app is responsive. Test on:
- Desktop browsers
- Mobile browsers
- Different screen sizes

## 🚀 Production Deployment

1. Update CORS origin to production URL
2. Build React app: `npm run build`
3. Serve build folder with web server
4. Update environment variables for production

## 📞 Support

If you encounter issues:
1. Check browser console for errors
2. Check network tab for failed requests
3. Verify backend API is responding
4. Review BACKEND_INTEGRATION.md for detailed explanations
