# STEMBlock React Frontend

A modern, responsive **React Framework Application** for the STEMBlock STEM learning platform with complete frontend-backend integration.

## 🚀 React Framework Features

### ✅ **Complete React Framework Implementation**
- **React 18** with functional components and hooks
- **JSX syntax** for component rendering
- **React Hooks**: useState, useEffect, useReducer, useCallback, useMemo, useRef, useContext
- **React Context API** for global state management
- **Custom React Hooks** for reusable logic
- **React Router v6** for client-side routing
- **React Query** for server state management

### ✅ **React Component Architecture**
- Functional components with proper React patterns
- Component composition and reusability
- Props passing and state lifting
- Conditional rendering and list rendering
- Event handling with React synthetic events
- Controlled components for forms

### ✅ **Complete Authentication System**
- User registration with email verification
- Secure login with JWT cookies
- Password reset functionality
- Protected routes and session management
- Real-time authentication state management

### ✅ **Advanced Form Handling**
- Custom form validation with real-time feedback
- Password strength indicator
- Email availability checking
- Error handling and user feedback

### ✅ **Responsive Design**
- Mobile-first responsive design
- Modern UI with smooth animations
- Loading states and skeleton screens
- Toast notifications for user feedback

### ✅ **Network & Error Handling**
- Offline detection and user feedback
- Global error boundary
- Retry mechanisms for failed requests
- Session timeout warnings

### ✅ **Backend Integration**
- Full integration with Go backend API
- Proper CORS configuration
- JWT cookie authentication
- Real-time session management

## 🏗️ React Project Structure

```
src/
├── components/           # Reusable React components
│   ├── auth/            # Authentication React components
│   │   ├── ProtectedRoute.jsx  # Route protection component
│   │   └── PublicRoute.jsx     # Public route component
│   ├── common/          # Common React UI components
│   │   ├── Button.jsx          # Reusable button component
│   │   ├── LoadingSpinner.jsx  # Loading component
│   │   └── ErrorBoundary.jsx   # Error boundary component
│   ├── examples/        # React Framework Examples
│   │   ├── SimpleCounter.jsx   # useState & useEffect demo
│   │   ├── TodoList.jsx        # Complex state management demo
│   │   ├── ReactDashboard.jsx  # useReducer & useCallback demo
│   │   └── ReactShowcase.jsx   # All React hooks demo
│   ├── forms/           # Form React components
│   │   ├── Input.jsx           # Input component with validation
│   │   └── PasswordInput.jsx   # Password input with strength
│   └── layout/          # Layout React components
│       ├── Header.jsx          # Navigation header
│       ├── Footer.jsx          # Site footer
│       └── Layout.jsx          # Main layout wrapper
├── contexts/            # React Context API
│   ├── AuthContext.js   # Authentication context & provider
│   └── NetworkContext.js # Network status context
├── hooks/               # Custom React hooks
│   ├── useForm.js       # Form handling custom hook
│   └── usePasswordStrength.js # Password validation hook
├── pages/               # React page components
│   ├── auth/            # Authentication pages
│   │   ├── Login.jsx           # Login page component
│   │   ├── Register.jsx        # Registration page
│   │   ├── ForgotPassword.jsx  # Password reset request
│   │   ├── ResetPassword.jsx   # Password reset form
│   │   └── EmailVerification.jsx # Email verification
│   ├── Dashboard.jsx    # User dashboard page
│   ├── Home.jsx         # Landing page component
│   ├── Courses.jsx      # Courses listing page
│   ├── About.jsx        # About page component
│   ├── Contact.jsx      # Contact form page
│   └── NotFound.jsx     # 404 error page
├── services/            # API services
│   └── api.js           # Axios configuration and API calls
├── styles/              # Styled Components
│   └── GlobalStyles.js  # Global styled-components
├── utils/               # Utility functions
│   └── validation.js    # Form validation rules
├── config/              # Configuration files
│   └── api.js           # API configuration
├── App.js               # Main React App component
└── index.js             # React app entry point
```

## 🛠️ Setup Instructions

### Prerequisites
- Node.js 16+ and npm
- Your Go backend running at `https://stemblock-login-gljgs.ondigitalocean.app`

### Installation

1. **Navigate to the React directory:**
   ```bash
   cd Website/react
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Start the development server:**
   ```bash
   npm start
   ```

4. **Open your browser:**
   Navigate to `http://localhost:3000`

### Build for Production

```bash
npm run build
```

This creates an optimized production build in the `build/` folder.

## 🔧 Configuration

### API Configuration
Update `src/config/api.js` to match your backend URL:

```javascript
export const API_CONFIG = {
  BASE_URL: 'https://stemblock-login-gljgs.ondigitalocean.app',
  FRONTEND_URL: 'https://www.stemblock.ca',
  // ... other config
};
```

### Environment Variables
Create a `.env` file in the root directory:

```env
REACT_APP_API_URL=https://stemblock-login-gljgs.ondigitalocean.app
REACT_APP_FRONTEND_URL=https://www.stemblock.ca
```

## 🧪 React Framework Examples

Visit `/examples` to see React framework features in action:

### 1. **Simple Counter** (`/examples/counter`)
- **useState** for state management
- **useEffect** for side effects
- **useCallback** for memoized functions
- Event handling and conditional rendering

### 2. **Todo List** (`/examples/todo`)
- **useState** for complex state
- **useEffect** for localStorage persistence
- **useMemo** for performance optimization
- **useCallback** for event handlers
- List rendering and controlled components

### 3. **React Showcase** (`/examples/react-dashboard`)
- **All React hooks** demonstrated
- **useContext** for theme management
- **useReducer** for complex state
- **useRef** for DOM manipulation
- **useMemo** for expensive calculations

## 🧪 Testing the Integration

### 1. **Authentication Flow**
- Register a new account → Email verification
- Login with credentials → Dashboard access
- Forgot password → Email reset link
- Protected routes → Automatic redirects

### 2. **Session Management**
- Automatic session checking
- Session timeout warnings
- Logout functionality
- Route protection

### 3. **Error Handling**
- Network errors → User-friendly messages
- Validation errors → Real-time feedback
- Server errors → Graceful degradation

## 📱 Pages & Features

### **Public Pages**
- **Home** (`/`) - Landing page with features
- **Courses** (`/courses`) - Course catalog with filtering
- **About** (`/about`) - About STEMBlock
- **Contact** (`/contact`) - Contact form

### **Authentication Pages**
- **Login** (`/login`) - User login with redirect support
- **Register** (`/register`) - User registration with validation
- **Forgot Password** (`/forgot-password`) - Password reset request
- **Reset Password** (`/reset-password`) - Password reset with token
- **Email Verification** (`/verify-email`) - Email verification

### **Protected Pages**
- **Dashboard** (`/dashboard`) - User dashboard with stats
- **Profile** (`/dashboard/profile`) - User profile management

## 🎨 UI Components

### **Form Components**
- `Input` - Enhanced input with validation
- `PasswordInput` - Password input with strength indicator
- `Button` - Flexible button component

### **Common Components**
- `LoadingSpinner` - Loading indicators
- `ErrorBoundary` - Error handling
- `NetworkStatus` - Offline detection

### **Layout Components**
- `Header` - Navigation with auth state
- `Footer` - Site footer
- `Layout` - Page layout wrapper

## 🔐 Security Features

- JWT cookie authentication
- CSRF protection via SameSite cookies
- Input validation and sanitization
- Protected route guards
- Session timeout handling

## 🌐 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 📦 Dependencies

### **Core**
- React 18.2.0
- React Router DOM 6.3.0
- Styled Components 5.3.11

### **State Management**
- React Query 3.39.3

### **Forms & Validation**
- Custom form hooks
- Real-time validation

### **UI & UX**
- React Hot Toast 2.4.1
- Custom loading states
- Responsive design

## 🚀 Deployment

### **Build**
```bash
npm run build
```

### **Deploy to Static Hosting**
The build folder can be deployed to:
- Netlify
- Vercel
- AWS S3 + CloudFront
- GitHub Pages

### **Environment Setup**
Ensure your production environment has:
- Correct API URLs
- HTTPS enabled
- CORS configured on backend

## 🤝 Contributing

1. Follow the existing code structure
2. Use TypeScript for new features (optional)
3. Write tests for new components
4. Follow the established styling patterns

## 📄 License

This project is part of the STEMBlock platform.

---

**Ready to use!** This React application provides a complete, production-ready frontend for your STEMBlock platform with full backend integration.
