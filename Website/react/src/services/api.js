import axios from 'axios';
import { API_CONFIG, HTTP_STATUS, ERROR_MESSAGES } from '../config/api';

// Create axios instance
const apiClient = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  withCredentials: true, // Important for JWT cookies
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // Add any auth headers if needed
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle common errors
    if (error.code === 'ECONNABORTED') {
      error.message = ERROR_MESSAGES.TIMEOUT_ERROR;
    } else if (!error.response) {
      error.message = ERROR_MESSAGES.NETWORK_ERROR;
    } else {
      const { status } = error.response;
      
      switch (status) {
        case HTTP_STATUS.UNAUTHORIZED:
          // Handle session expiry
          if (window.location.pathname !== '/login') {
            window.location.href = '/login?redirect=' + encodeURIComponent(window.location.pathname);
          }
          break;
        case HTTP_STATUS.TOO_MANY_REQUESTS:
          error.message = ERROR_MESSAGES.RATE_LIMIT_ERROR;
          break;
        case HTTP_STATUS.INTERNAL_SERVER_ERROR:
          error.message = ERROR_MESSAGES.GENERIC_ERROR;
          break;
        default:
          break;
      }
    }
    
    return Promise.reject(error);
  }
);

// API service methods
export const apiService = {
  // Authentication
  login: (credentials) => 
    apiClient.post(API_CONFIG.ENDPOINTS.LOGIN, credentials),
  
  register: (userData) => 
    apiClient.post(API_CONFIG.ENDPOINTS.REGISTER, userData),
  
  forgotPassword: (email) => 
    apiClient.post(API_CONFIG.ENDPOINTS.FORGOT_PASSWORD, { email }),
  
  resetPassword: (token, password) => 
    apiClient.post(API_CONFIG.ENDPOINTS.RESET_PASSWORD, { token, password }),
  
  verifyEmail: (token) => 
    apiClient.get(`${API_CONFIG.ENDPOINTS.VERIFY_EMAIL}?token=${token}`),
  
  checkAuth: () => 
    apiClient.get(API_CONFIG.ENDPOINTS.DASHBOARD_AUTH),
  
  resendVerification: (email) => 
    apiClient.post(API_CONFIG.ENDPOINTS.RESEND_VERIFICATION, { email }),

  // Utility
  checkEmailAvailability: (email) => 
    apiClient.post(API_CONFIG.ENDPOINTS.CHECK_EMAIL, { email }),
};

export default apiClient;
