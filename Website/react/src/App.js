import React from 'react';
import { Routes, Route } from 'react-router-dom';

import Layout from './components/layout/Layout';
import ProtectedRoute from './components/auth/ProtectedRoute';
import PublicRoute from './components/auth/PublicRoute';

// Pages
import Home from './pages/Home';
import Login from './pages/auth/Login';
import Register from './pages/auth/Register';
import ForgotPassword from './pages/auth/ForgotPassword';
import ResetPassword from './pages/auth/ResetPassword';
import EmailVerification from './pages/auth/EmailVerification';
import Dashboard from './pages/Dashboard';
import Profile from './pages/Profile';
import Courses from './pages/Courses';
import About from './pages/About';
import Contact from './pages/Contact';
import NotFound from './pages/NotFound';

// React Example Components
import SimpleCounter from './components/examples/SimpleCounter';
import TodoListComponent from './components/examples/TodoList';
import ReactDashboard from './components/examples/ReactDashboard';

// Network status component
import NetworkStatus from './components/common/NetworkStatus';

const App = () => {
  return (
    <React.Fragment>
      <NetworkStatus />
      <Routes>
        {/* Public routes */}
        <Route path="/" element={<Layout />}>
          <Route index element={<Home />} />
          <Route path="about" element={<About />} />
          <Route path="contact" element={<Contact />} />
          <Route path="courses" element={<Courses />} />
        </Route>

        {/* Auth routes - redirect if already authenticated */}
        <Route element={<PublicRoute />}>
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/forgot-password" element={<ForgotPassword />} />
          <Route path="/reset-password" element={<ResetPassword />} />
        </Route>

        {/* Email verification - accessible to all */}
        <Route path="/verify-email" element={<EmailVerification />} />

        {/* Protected routes */}
        <Route element={<ProtectedRoute />}>
          <Route path="/dashboard" element={<Layout />}>
            <Route index element={<Dashboard />} />
            <Route path="profile" element={<Profile />} />
            <Route path="react-examples" element={<ReactDashboard />} />
          </Route>
        </Route>

        {/* React Examples - Public routes for demonstration */}
        <Route path="/examples" element={<Layout />}>
          <Route index element={<SimpleCounter />} />
          <Route path="counter" element={<SimpleCounter />} />
          <Route path="todo" element={<TodoListComponent />} />
          <Route path="react-dashboard" element={<ReactDashboard />} />
        </Route>

        {/* 404 */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </React.Fragment>
  );
};

export default App;
