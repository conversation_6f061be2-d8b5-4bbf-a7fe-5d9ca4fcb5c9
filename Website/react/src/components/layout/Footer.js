import React from 'react';
import styled from 'styled-components';

const FooterContainer = styled.footer`
  background-color: #333;
  color: white;
  padding: 40px 20px 20px;
  margin-top: auto;
`;

const FooterContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
`;

const FooterSection = styled.div`
  h3 {
    margin-bottom: 16px;
    color: #fff;
    font-size: 18px;
  }

  p, address {
    line-height: 1.6;
    color: #ccc;
    font-style: normal;
  }

  a {
    color: #007bff;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
`;

const SocialLinks = styled.div`
  display: flex;
  gap: 16px;
  margin-top: 16px;
`;

const SocialLink = styled.a`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: #555;
  border-radius: 50%;
  color: white;
  text-decoration: none;
  transition: background-color 0.2s;

  &:hover {
    background-color: #007bff;
  }

  img {
    width: 20px;
    height: 20px;
  }
`;

const Copyright = styled.div`
  text-align: center;
  padding-top: 20px;
  margin-top: 20px;
  border-top: 1px solid #555;
  color: #ccc;
  font-size: 14px;
`;

function Footer() {
  return (
    <FooterContainer>
      <FooterContent>
        <FooterSection>
          <h3>Contact Us</h3>
          <address>
            Phone: (*************<br />
            Address: 50 Crowther Ln suite 140, Fredericton, NB E3C 0J1<br />
            <a href="mailto:<EMAIL>">Email: <EMAIL></a>
          </address>
        </FooterSection>

        <FooterSection>
          <h3>About STEMBlock</h3>
          <p>
            STEMBlock is an interactive STEM learning platform designed to make 
            science, technology, engineering, and mathematics education engaging 
            and accessible for students of all ages.
          </p>
        </FooterSection>

        <FooterSection>
          <h3>Quick Links</h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            <a href="/courses">Courses</a>
            <a href="/about">About Us</a>
            <a href="/contact">Contact</a>
            <a href="/privacy">Privacy Policy</a>
            <a href="/terms">Terms of Service</a>
          </div>
        </FooterSection>

        <FooterSection>
          <h3>Follow Us</h3>
          <SocialLinks>
            <SocialLink href="#" aria-label="Facebook">
              <img src="/img/facebook.png" alt="Facebook" />
            </SocialLink>
            <SocialLink href="#" aria-label="Instagram">
              <img src="/img/inswhite.png" alt="Instagram" />
            </SocialLink>
          </SocialLinks>
        </FooterSection>
      </FooterContent>

      <Copyright>
        © {new Date().getFullYear()} STEMBlock. All rights reserved.
      </Copyright>
    </FooterContainer>
  );
}

export default Footer;
