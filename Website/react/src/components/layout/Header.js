import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { useAuth } from '../../contexts/AuthContext';
import Button from '../common/Button';

const HeaderContainer = styled.header`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 0 20px;
`;

const HeaderContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
`;

const Logo = styled(Link)`
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: bold;
  color: #007bff;
  text-decoration: none;
  
  img {
    height: 40px;
    margin-right: 12px;
  }
`;

const Nav = styled.nav`
  display: flex;
  align-items: center;
  gap: 32px;

  @media (max-width: 768px) {
    display: ${props => props.isOpen ? 'flex' : 'none'};
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: white;
    flex-direction: column;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    gap: 16px;
  }
`;

const NavLink = styled(Link)`
  color: #333;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s;

  &:hover {
    color: #007bff;
  }

  &.active {
    color: #007bff;
  }
`;

const AuthSection = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;

  @media (max-width: 768px) {
    flex-direction: column;
    width: 100%;
  }
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  color: #333;
`;

const UserName = styled.span`
  font-weight: 500;
`;

const MobileMenuButton = styled.button`
  display: none;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #333;

  @media (max-width: 768px) {
    display: block;
  }
`;

function Header() {
  const { isAuthenticated, user, logout } = useAuth();
  const navigate = useNavigate();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleLogout = () => {
    logout();
    navigate('/');
    setIsMobileMenuOpen(false);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <HeaderContainer>
      <HeaderContent>
        <Logo to="/" onClick={closeMobileMenu}>
          <img src="/img/stem_block_logo.png" alt="STEMBlock" />
          STEMBlock
        </Logo>

        <Nav isOpen={isMobileMenuOpen}>
          <NavLink to="/" onClick={closeMobileMenu}>Home</NavLink>
          <NavLink to="/courses" onClick={closeMobileMenu}>Courses</NavLink>
          <NavLink to="/about" onClick={closeMobileMenu}>About Us</NavLink>
          <NavLink to="/contact" onClick={closeMobileMenu}>Contact</NavLink>
          
          {isAuthenticated && (
            <NavLink to="/dashboard" onClick={closeMobileMenu}>Dashboard</NavLink>
          )}

          <AuthSection>
            {isAuthenticated ? (
              <>
                <UserInfo>
                  <UserName>Welcome, {user?.username}!</UserName>
                </UserInfo>
                <Button 
                  variant="outline" 
                  size="small" 
                  onClick={handleLogout}
                >
                  Logout
                </Button>
              </>
            ) : (
              <>
                <Button 
                  as={Link} 
                  to="/login" 
                  variant="ghost" 
                  size="small"
                  onClick={closeMobileMenu}
                >
                  Login
                </Button>
                <Button 
                  as={Link} 
                  to="/register" 
                  size="small"
                  onClick={closeMobileMenu}
                >
                  Sign Up
                </Button>
              </>
            )}
          </AuthSection>
        </Nav>

        <MobileMenuButton onClick={toggleMobileMenu}>
          {isMobileMenuOpen ? '✕' : '☰'}
        </MobileMenuButton>
      </HeaderContent>
    </HeaderContainer>
  );
}

export default Header;
