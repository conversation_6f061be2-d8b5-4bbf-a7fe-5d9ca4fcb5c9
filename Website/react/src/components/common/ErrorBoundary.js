import React from 'react';
import styled from 'styled-components';

const ErrorContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  text-align: center;
  background-color: #f8f9fa;
`;

const ErrorTitle = styled.h1`
  color: #dc3545;
  margin-bottom: 16px;
  font-size: 2rem;
`;

const ErrorMessage = styled.p`
  color: #6c757d;
  margin-bottom: 24px;
  max-width: 600px;
  line-height: 1.6;
`;

const ErrorButton = styled.button`
  background-color: #007bff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #0056b3;
  }
`;

const ErrorDetails = styled.details`
  margin-top: 24px;
  max-width: 800px;
  text-align: left;

  summary {
    cursor: pointer;
    color: #6c757d;
    margin-bottom: 12px;
  }

  pre {
    background-color: #f1f3f4;
    padding: 16px;
    border-radius: 6px;
    overflow-x: auto;
    font-size: 14px;
    color: #333;
  }
`;

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    console.error('Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // You can also log the error to an error reporting service here
    // logErrorToService(error, errorInfo);
  }

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      return (
        <ErrorContainer>
          <ErrorTitle>Oops! Something went wrong</ErrorTitle>
          <ErrorMessage>
            We're sorry, but something unexpected happened. Please try reloading the page or go back to the home page.
          </ErrorMessage>
          
          <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
            <ErrorButton onClick={this.handleReload}>
              Reload Page
            </ErrorButton>
            <ErrorButton 
              onClick={this.handleGoHome}
              style={{ backgroundColor: '#6c757d' }}
            >
              Go Home
            </ErrorButton>
          </div>

          {process.env.NODE_ENV === 'development' && this.state.error && (
            <ErrorDetails>
              <summary>Error Details (Development Only)</summary>
              <pre>
                {this.state.error && this.state.error.toString()}
                <br />
                {this.state.errorInfo.componentStack}
              </pre>
            </ErrorDetails>
          )}
        </ErrorContainer>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
