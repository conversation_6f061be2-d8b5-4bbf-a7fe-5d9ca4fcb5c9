import React from 'react';
import styled from 'styled-components';
import { useNetwork } from '../../contexts/NetworkContext';

const NetworkBanner = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background-color: ${props => props.isOnline ? '#28a745' : '#dc3545'};
  color: white;
  padding: 8px 16px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  transform: translateY(${props => props.show ? '0' : '-100%'});
  transition: transform 0.3s ease-in-out;
  animation: ${props => props.show ? 'slideInFromTop 0.3s ease-out' : 'none'};

  @keyframes slideInFromTop {
    from {
      transform: translateY(-100%);
    }
    to {
      transform: translateY(0);
    }
  }
`;

const NetworkIcon = styled.span`
  margin-right: 8px;
`;

const ConnectionInfo = styled.span`
  margin-left: 8px;
  opacity: 0.8;
  font-size: 12px;
`;

function NetworkStatus() {
  const { isOnline, connectionType, isSlowConnection } = useNetwork();
  const [showBanner, setShowBanner] = React.useState(false);
  const [wasOffline, setWasOffline] = React.useState(false);

  React.useEffect(() => {
    if (!isOnline) {
      setShowBanner(true);
      setWasOffline(true);
    } else if (wasOffline) {
      // Show "back online" message briefly
      setShowBanner(true);
      const timer = setTimeout(() => {
        setShowBanner(false);
        setWasOffline(false);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [isOnline, wasOffline]);

  if (!showBanner) return null;

  return (
    <NetworkBanner show={showBanner} isOnline={isOnline}>
      <NetworkIcon>
        {isOnline ? '✅' : '⚠️'}
      </NetworkIcon>
      {isOnline ? (
        <>
          You're back online!
          {isSlowConnection && (
            <ConnectionInfo>
              (Slow connection detected)
            </ConnectionInfo>
          )}
        </>
      ) : (
        <>
          You're offline. Some features may not work.
          <ConnectionInfo>
            Check your internet connection
          </ConnectionInfo>
        </>
      )}
    </NetworkBanner>
  );
}

export default NetworkStatus;
