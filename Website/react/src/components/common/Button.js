import React from 'react';
import styled from 'styled-components';
import LoadingSpinner from './LoadingSpinner';

const StyledButton = styled.button`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: ${props => {
    switch (props.$size) {
      case 'small': return '6px 12px';
      case 'large': return '12px 24px';
      default: return '8px 16px';
    }
  }};
  font-size: ${props => {
    switch (props.$size) {
      case 'small': return '14px';
      case 'large': return '18px';
      default: return '16px';
    }
  }};
  font-weight: 500;
  border: none;
  border-radius: 6px;
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  transition: all 0.2s ease-in-out;
  text-decoration: none;
  min-height: ${props => {
    switch (props.$size) {
      case 'small': return '32px';
      case 'large': return '48px';
      default: return '40px';
    }
  }};

  /* Variant styles */
  ${props => {
    switch (props.$variant) {
      case 'secondary':
        return `
          background-color: #6c757d;
          color: white;
          &:hover:not(:disabled) {
            background-color: #545b62;
          }
        `;
      case 'outline':
        return `
          background-color: transparent;
          color: #007bff;
          border: 2px solid #007bff;
          &:hover:not(:disabled) {
            background-color: #007bff;
            color: white;
          }
        `;
      case 'danger':
        return `
          background-color: #dc3545;
          color: white;
          &:hover:not(:disabled) {
            background-color: #c82333;
          }
        `;
      case 'success':
        return `
          background-color: #28a745;
          color: white;
          &:hover:not(:disabled) {
            background-color: #218838;
          }
        `;
      case 'ghost':
        return `
          background-color: transparent;
          color: #007bff;
          &:hover:not(:disabled) {
            background-color: rgba(0, 123, 255, 0.1);
          }
        `;
      default: // primary
        return `
          background-color: #007bff;
          color: white;
          &:hover:not(:disabled) {
            background-color: #0056b3;
          }
        `;
    }
  }}

  /* Disabled state */
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  /* Full width */
  ${props => props.$fullWidth && `
    width: 100%;
  `}

  /* Focus styles */
  &:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
  }
`;

const Button = ({
  children,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  fullWidth = false,
  onClick,
  type = 'button',
  className = '',
  ...props
}) => {
  const handleClick = (e) => {
    if (disabled || loading) {
      e.preventDefault();
      return;
    }
    if (onClick) {
      onClick(e);
    }
  };

  return (
    <StyledButton
      $variant={variant}
      $size={size}
      $fullWidth={fullWidth}
      disabled={disabled || loading}
      onClick={handleClick}
      type={type}
      className={className}
      {...props}
    >
      {loading && <LoadingSpinner size="small" color="currentColor" />}
      {children}
    </StyledButton>
  );
};

export default Button;
