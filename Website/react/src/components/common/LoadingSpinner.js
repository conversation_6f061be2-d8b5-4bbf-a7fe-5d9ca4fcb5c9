import React from 'react';
import styled, { keyframes } from 'styled-components';

const spin = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

const SpinnerContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${props => props.size === 'large' ? '40px' : '20px'};
`;

const Spinner = styled.div`
  width: ${props => {
    switch (props.size) {
      case 'small': return '16px';
      case 'large': return '48px';
      default: return '24px';
    }
  }};
  height: ${props => {
    switch (props.size) {
      case 'small': return '16px';
      case 'large': return '48px';
      default: return '24px';
    }
  }};
  border: ${props => {
    const width = props.size === 'large' ? '4px' : '2px';
    return `${width} solid #f3f3f3`;
  }};
  border-top: ${props => {
    const width = props.size === 'large' ? '4px' : '2px';
    const color = props.color || '#007bff';
    return `${width} solid ${color}`;
  }};
  border-radius: 50%;
  animation: ${spin} 1s linear infinite;
`;

const LoadingText = styled.span`
  margin-left: 12px;
  color: #6c757d;
  font-size: ${props => props.size === 'large' ? '16px' : '14px'};
`;

function LoadingSpinner({ 
  size = 'medium', 
  color = '#007bff', 
  text = '', 
  className = '' 
}) {
  return (
    <SpinnerContainer size={size} className={className}>
      <Spinner size={size} color={color} />
      {text && <LoadingText size={size}>{text}</LoadingText>}
    </SpinnerContainer>
  );
}

export default LoadingSpinner;
