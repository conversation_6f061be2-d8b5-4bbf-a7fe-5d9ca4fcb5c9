import React, { useState, useEffect } from 'react';
import styled from 'styled-components';

// Styled components
const CounterContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border: 2px solid #007bff;
  border-radius: 8px;
  background-color: #f8f9fa;
  max-width: 300px;
  margin: 20px auto;
`;

const CounterTitle = styled.h2`
  color: #333;
  margin-bottom: 20px;
  font-size: 1.5rem;
`;

const CounterDisplay = styled.div`
  font-size: 3rem;
  font-weight: bold;
  color: #007bff;
  margin: 20px 0;
  padding: 20px;
  background-color: white;
  border-radius: 50%;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 10px;
  margin-top: 20px;
`;

const CounterButton = styled.button`
  padding: 10px 20px;
  font-size: 1rem;
  font-weight: 500;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &.increment {
    background-color: #28a745;
    color: white;
    
    &:hover {
      background-color: #218838;
    }
  }
  
  &.decrement {
    background-color: #dc3545;
    color: white;
    
    &:hover {
      background-color: #c82333;
    }
  }
  
  &.reset {
    background-color: #6c757d;
    color: white;
    
    &:hover {
      background-color: #545b62;
    }
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const StatusMessage = styled.p`
  margin-top: 15px;
  font-size: 0.9rem;
  color: ${props => {
    if (props.count > 10) return '#dc3545';
    if (props.count < 0) return '#fd7e14';
    return '#28a745';
  }};
  font-weight: 500;
`;

// React functional component with hooks
const SimpleCounter = ({ initialValue = 0, maxValue = 20, minValue = -10 }) => {
  // State hooks
  const [count, setCount] = useState(initialValue);
  const [isAnimating, setIsAnimating] = useState(false);

  // Effect hook for animations
  useEffect(() => {
    if (isAnimating) {
      const timer = setTimeout(() => {
        setIsAnimating(false);
      }, 200);
      
      return () => clearTimeout(timer);
    }
  }, [isAnimating]);

  // Event handlers
  const handleIncrement = () => {
    if (count < maxValue) {
      setCount(prevCount => prevCount + 1);
      setIsAnimating(true);
    }
  };

  const handleDecrement = () => {
    if (count > minValue) {
      setCount(prevCount => prevCount - 1);
      setIsAnimating(true);
    }
  };

  const handleReset = () => {
    setCount(initialValue);
    setIsAnimating(true);
  };

  // Helper function to get status message
  const getStatusMessage = () => {
    if (count >= maxValue) {
      return `Maximum reached! (${maxValue})`;
    }
    if (count <= minValue) {
      return `Minimum reached! (${minValue})`;
    }
    if (count > 10) {
      return 'Getting high! 🚀';
    }
    if (count < 0) {
      return 'Going negative! ⚠️';
    }
    return 'Looking good! ✅';
  };

  // JSX render
  return (
    <CounterContainer>
      <CounterTitle>React Counter Example</CounterTitle>
      
      <CounterDisplay style={{ 
        transform: isAnimating ? 'scale(1.1)' : 'scale(1)',
        transition: 'transform 0.2s ease'
      }}>
        {count}
      </CounterDisplay>
      
      <ButtonGroup>
        <CounterButton 
          className="decrement"
          onClick={handleDecrement}
          disabled={count <= minValue}
          aria-label="Decrease counter"
        >
          - Decrease
        </CounterButton>
        
        <CounterButton 
          className="reset"
          onClick={handleReset}
          aria-label="Reset counter"
        >
          🔄 Reset
        </CounterButton>
        
        <CounterButton 
          className="increment"
          onClick={handleIncrement}
          disabled={count >= maxValue}
          aria-label="Increase counter"
        >
          + Increase
        </CounterButton>
      </ButtonGroup>
      
      <StatusMessage count={count}>
        {getStatusMessage()}
      </StatusMessage>
    </CounterContainer>
  );
};

export default SimpleCounter;
