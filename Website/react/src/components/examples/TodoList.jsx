import React, { useState, useEffect, useCallback, useMemo } from 'react';
import styled from 'styled-components';

// Styled components for the Todo List
const TodoContainer = styled.div`
  max-width: 500px;
  margin: 20px auto;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const TodoHeader = styled.h2`
  text-align: center;
  color: #333;
  margin-bottom: 20px;
`;

const TodoForm = styled.form`
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
`;

const TodoInput = styled.input`
  flex: 1;
  padding: 10px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 16px;
  
  &:focus {
    outline: none;
    border-color: #007bff;
  }
`;

const AddButton = styled.button`
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  
  &:hover {
    background-color: #0056b3;
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const FilterButtons = styled.div`
  display: flex;
  gap: 8px;
  margin-bottom: 20px;
  justify-content: center;
`;

const FilterButton = styled.button`
  padding: 6px 12px;
  border: 2px solid ${props => props.active ? '#007bff' : '#e9ecef'};
  background: ${props => props.active ? '#007bff' : 'white'};
  color: ${props => props.active ? 'white' : '#666'};
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  
  &:hover {
    border-color: #007bff;
  }
`;

const TodoList = styled.ul`
  list-style: none;
  padding: 0;
`;

const TodoItem = styled.li`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  margin-bottom: 8px;
  background: ${props => props.completed ? '#f8f9fa' : 'white'};
`;

const TodoCheckbox = styled.input`
  width: 18px;
  height: 18px;
  cursor: pointer;
`;

const TodoText = styled.span`
  flex: 1;
  text-decoration: ${props => props.completed ? 'line-through' : 'none'};
  color: ${props => props.completed ? '#6c757d' : '#333'};
`;

const DeleteButton = styled.button`
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 12px;
  
  &:hover {
    background: #c82333;
  }
`;

const TodoStats = styled.div`
  text-align: center;
  margin-top: 20px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 14px;
  color: #666;
`;

// React functional component with hooks
const TodoListComponent = () => {
  // State hooks
  const [todos, setTodos] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [filter, setFilter] = useState('all'); // all, active, completed
  const [nextId, setNextId] = useState(1);

  // Load todos from localStorage on component mount
  useEffect(() => {
    const savedTodos = localStorage.getItem('stemblock-todos');
    if (savedTodos) {
      try {
        const parsedTodos = JSON.parse(savedTodos);
        setTodos(parsedTodos);
        const maxId = Math.max(...parsedTodos.map(todo => todo.id), 0);
        setNextId(maxId + 1);
      } catch (error) {
        console.error('Error loading todos:', error);
      }
    }
  }, []);

  // Save todos to localStorage whenever todos change
  useEffect(() => {
    localStorage.setItem('stemblock-todos', JSON.stringify(todos));
  }, [todos]);

  // Memoized filtered todos
  const filteredTodos = useMemo(() => {
    switch (filter) {
      case 'active':
        return todos.filter(todo => !todo.completed);
      case 'completed':
        return todos.filter(todo => todo.completed);
      default:
        return todos;
    }
  }, [todos, filter]);

  // Memoized statistics
  const stats = useMemo(() => {
    const total = todos.length;
    const completed = todos.filter(todo => todo.completed).length;
    const active = total - completed;
    return { total, completed, active };
  }, [todos]);

  // Event handlers using useCallback for optimization
  const handleSubmit = useCallback((e) => {
    e.preventDefault();
    if (inputValue.trim()) {
      const newTodo = {
        id: nextId,
        text: inputValue.trim(),
        completed: false,
        createdAt: new Date().toISOString()
      };
      setTodos(prev => [...prev, newTodo]);
      setInputValue('');
      setNextId(prev => prev + 1);
    }
  }, [inputValue, nextId]);

  const toggleTodo = useCallback((id) => {
    setTodos(prev => prev.map(todo =>
      todo.id === id ? { ...todo, completed: !todo.completed } : todo
    ));
  }, []);

  const deleteTodo = useCallback((id) => {
    setTodos(prev => prev.filter(todo => todo.id !== id));
  }, []);

  const clearCompleted = useCallback(() => {
    setTodos(prev => prev.filter(todo => !todo.completed));
  }, []);

  // JSX render
  return (
    <TodoContainer>
      <TodoHeader>📝 React Todo List</TodoHeader>
      
      <TodoForm onSubmit={handleSubmit}>
        <TodoInput
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          placeholder="Add a new todo..."
          maxLength={100}
        />
        <AddButton type="submit" disabled={!inputValue.trim()}>
          Add Todo
        </AddButton>
      </TodoForm>

      <FilterButtons>
        <FilterButton 
          active={filter === 'all'} 
          onClick={() => setFilter('all')}
        >
          All ({stats.total})
        </FilterButton>
        <FilterButton 
          active={filter === 'active'} 
          onClick={() => setFilter('active')}
        >
          Active ({stats.active})
        </FilterButton>
        <FilterButton 
          active={filter === 'completed'} 
          onClick={() => setFilter('completed')}
        >
          Completed ({stats.completed})
        </FilterButton>
      </FilterButtons>

      <TodoList>
        {filteredTodos.length === 0 ? (
          <li style={{ textAlign: 'center', color: '#666', padding: '20px' }}>
            {filter === 'all' ? 'No todos yet. Add one above!' : 
             filter === 'active' ? 'No active todos!' : 
             'No completed todos!'}
          </li>
        ) : (
          filteredTodos.map(todo => (
            <TodoItem key={todo.id} completed={todo.completed}>
              <TodoCheckbox
                type="checkbox"
                checked={todo.completed}
                onChange={() => toggleTodo(todo.id)}
              />
              <TodoText completed={todo.completed}>
                {todo.text}
              </TodoText>
              <DeleteButton onClick={() => deleteTodo(todo.id)}>
                Delete
              </DeleteButton>
            </TodoItem>
          ))
        )}
      </TodoList>

      {stats.completed > 0 && (
        <div style={{ textAlign: 'center', marginTop: '16px' }}>
          <button
            onClick={clearCompleted}
            style={{
              background: '#ffc107',
              color: '#212529',
              border: 'none',
              padding: '8px 16px',
              borderRadius: '6px',
              cursor: 'pointer'
            }}
          >
            Clear Completed ({stats.completed})
          </button>
        </div>
      )}

      <TodoStats>
        📊 Total: {stats.total} | ✅ Completed: {stats.completed} | 🔄 Active: {stats.active}
      </TodoStats>
    </TodoContainer>
  );
};

export default TodoListComponent;
