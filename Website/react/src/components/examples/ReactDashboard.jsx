import React, { useState, useEffect, useReducer, useCallback, useMemo } from 'react';
import styled from 'styled-components';
import { useAuth } from '../../contexts/AuthContext';

// Styled components
const DashboardContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
`;

const WelcomeCard = styled.div`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 30px;
  text-align: center;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
`;

const StatCard = styled.div`
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  
  h3 {
    margin: 0 0 10px 0;
    color: #333;
  }
  
  .number {
    font-size: 2rem;
    font-weight: bold;
    color: #007bff;
  }
`;

const ActivityFeed = styled.div`
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
`;

const ActivityItem = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
`;

const ActivityIcon = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: ${props => props.color || '#007bff'};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
`;

const Button = styled.button`
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  margin: 5px;
  
  &:hover {
    background: #0056b3;
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

// Reducer for managing dashboard state
const dashboardReducer = (state, action) => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_STATS':
      return { ...state, stats: action.payload };
    case 'ADD_ACTIVITY':
      return { 
        ...state, 
        activities: [action.payload, ...state.activities.slice(0, 9)] // Keep only 10 items
      };
    case 'INCREMENT_STAT':
      return {
        ...state,
        stats: {
          ...state.stats,
          [action.payload]: state.stats[action.payload] + 1
        }
      };
    default:
      return state;
  }
};

// Initial state for dashboard reducer
const initialDashboardState = {
  isLoading: true,
  stats: {
    coursesCompleted: 0,
    hoursLearned: 0,
    certificatesEarned: 0,
    projectsBuilt: 0
  },
  activities: []
};

// React functional component with multiple hooks
const ReactDashboard = () => {
  // Using useAuth custom hook (React Context)
  const { user, isAuthenticated } = useAuth();
  
  // Using useReducer for complex state management
  const [dashboardState, dispatch] = useReducer(dashboardReducer, initialDashboardState);
  
  // Using useState for simple local state
  const [selectedTimeframe, setSelectedTimeframe] = useState('week');
  const [notifications, setNotifications] = useState([]);

  // Using useEffect for side effects
  useEffect(() => {
    // Simulate loading dashboard data
    const loadDashboardData = async () => {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Set mock stats
      dispatch({ 
        type: 'SET_STATS', 
        payload: {
          coursesCompleted: 12,
          hoursLearned: 48,
          certificatesEarned: 8,
          projectsBuilt: 15
        }
      });
      
      // Add initial activities
      const initialActivities = [
        { id: 1, type: 'course', message: 'Completed "React Fundamentals"', time: '2 hours ago', color: '#28a745' },
        { id: 2, type: 'project', message: 'Built a Todo App', time: '1 day ago', color: '#007bff' },
        { id: 3, type: 'certificate', message: 'Earned JavaScript Certificate', time: '3 days ago', color: '#ffc107' }
      ];
      
      initialActivities.forEach(activity => {
        dispatch({ type: 'ADD_ACTIVITY', payload: activity });
      });
      
      dispatch({ type: 'SET_LOADING', payload: false });
    };

    if (isAuthenticated) {
      loadDashboardData();
    }
  }, [isAuthenticated]);

  // Using useCallback for memoized event handlers
  const handleStatIncrement = useCallback((statName) => {
    dispatch({ type: 'INCREMENT_STAT', payload: statName });
    
    // Add activity
    const activities = {
      coursesCompleted: { message: 'Completed a new course!', color: '#28a745', type: 'course' },
      hoursLearned: { message: 'Logged another hour of learning!', color: '#007bff', type: 'time' },
      certificatesEarned: { message: 'Earned a new certificate!', color: '#ffc107', type: 'certificate' },
      projectsBuilt: { message: 'Built a new project!', color: '#dc3545', type: 'project' }
    };
    
    const activity = activities[statName];
    if (activity) {
      dispatch({ 
        type: 'ADD_ACTIVITY', 
        payload: {
          id: Date.now(),
          ...activity,
          time: 'Just now'
        }
      });
    }
  }, []);

  const handleTimeframeChange = useCallback((timeframe) => {
    setSelectedTimeframe(timeframe);
    // In a real app, this would trigger a data refetch
    console.log(`Timeframe changed to: ${timeframe}`);
  }, []);

  // Using useMemo for expensive calculations
  const totalProgress = useMemo(() => {
    const { coursesCompleted, hoursLearned, certificatesEarned, projectsBuilt } = dashboardState.stats;
    return coursesCompleted + hoursLearned + certificatesEarned + projectsBuilt;
  }, [dashboardState.stats]);

  const progressPercentage = useMemo(() => {
    return Math.min((totalProgress / 100) * 100, 100);
  }, [totalProgress]);

  // Conditional rendering based on authentication
  if (!isAuthenticated) {
    return (
      <DashboardContainer>
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <h2>Please log in to view your dashboard</h2>
        </div>
      </DashboardContainer>
    );
  }

  // Loading state
  if (dashboardState.isLoading) {
    return (
      <DashboardContainer>
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <h2>Loading your dashboard...</h2>
        </div>
      </DashboardContainer>
    );
  }

  // Main JSX render
  return (
    <DashboardContainer>
      <WelcomeCard>
        <h1>Welcome back, {user?.username || 'Student'}! 🎉</h1>
        <p>You've made {totalProgress} achievements so far!</p>
        <div style={{ 
          background: 'rgba(255,255,255,0.2)', 
          borderRadius: '10px', 
          height: '10px', 
          margin: '10px 0' 
        }}>
          <div style={{ 
            background: 'white', 
            height: '100%', 
            borderRadius: '10px',
            width: `${progressPercentage}%`,
            transition: 'width 0.3s ease'
          }} />
        </div>
        <small>{progressPercentage.toFixed(1)}% towards your next milestone</small>
      </WelcomeCard>

      {/* Timeframe selector */}
      <div style={{ marginBottom: '20px', textAlign: 'center' }}>
        {['week', 'month', 'year'].map(timeframe => (
          <Button
            key={timeframe}
            onClick={() => handleTimeframeChange(timeframe)}
            style={{ 
              background: selectedTimeframe === timeframe ? '#0056b3' : '#6c757d',
              margin: '0 5px'
            }}
          >
            This {timeframe}
          </Button>
        ))}
      </div>

      <StatsGrid>
        {Object.entries(dashboardState.stats).map(([key, value]) => (
          <StatCard key={key}>
            <h3>{key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</h3>
            <div className="number">{value}</div>
            <Button onClick={() => handleStatIncrement(key)}>
              + Add One
            </Button>
          </StatCard>
        ))}
      </StatsGrid>

      <ActivityFeed>
        <h2>Recent Activity</h2>
        {dashboardState.activities.length === 0 ? (
          <p>No recent activity. Start learning to see your progress here!</p>
        ) : (
          dashboardState.activities.map(activity => (
            <ActivityItem key={activity.id}>
              <ActivityIcon color={activity.color}>
                {activity.type === 'course' ? '📚' : 
                 activity.type === 'project' ? '🛠️' : 
                 activity.type === 'certificate' ? '🏆' : '⏰'}
              </ActivityIcon>
              <div style={{ flex: 1 }}>
                <div>{activity.message}</div>
                <small style={{ color: '#666' }}>{activity.time}</small>
              </div>
            </ActivityItem>
          ))
        )}
      </ActivityFeed>
    </DashboardContainer>
  );
};

export default ReactDashboard;
