import React, { useState } from 'react';
import styled from 'styled-components';
import Input from './Input';
import { usePasswordStrength } from '../../hooks/usePasswordStrength';

const PasswordContainer = styled.div`
  width: 100%;
`;

const ToggleButton = styled.button`
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    color: #495057;
  }
`;

const StrengthIndicator = styled.div`
  margin-top: 8px;
`;

const StrengthBar = styled.div`
  width: 100%;
  height: 6px;
  background-color: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
`;

const StrengthFill = styled.div`
  height: 100%;
  width: ${props => props.percentage}%;
  background-color: ${props => props.color};
  transition: all 0.3s ease;
`;

const StrengthText = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
`;

const StrengthLabel = styled.span`
  color: ${props => props.color};
  font-weight: 500;
`;

const RequirementsList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 8px 0 0 0;
  font-size: 12px;
`;

const Requirement = styled.li`
  display: flex;
  align-items: center;
  gap: 6px;
  color: ${props => props.met ? '#28a745' : '#6c757d'};
  margin-bottom: 2px;
`;

const EyeIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
  </svg>
);

const EyeOffIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7zM2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3 2 4.27zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2zm4.31-.78l3.15 3.15.02-.16c0-1.66-1.34-3-3-3l-.17.01z"/>
  </svg>
);

function PasswordInput({
  showStrength = false,
  showRequirements = false,
  value = '',
  onChange,
  ...props
}) {
  const [showPassword, setShowPassword] = useState(false);
  const strength = usePasswordStrength(value);

  const requirements = [
    { label: 'At least 8 characters', met: strength.checks.length },
    { label: 'One lowercase letter', met: strength.checks.lowercase },
    { label: 'One uppercase letter', met: strength.checks.uppercase },
    { label: 'One number', met: strength.checks.numbers },
    { label: 'One special character', met: strength.checks.symbols },
  ];

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <PasswordContainer>
      <Input
        type={showPassword ? 'text' : 'password'}
        value={value}
        onChange={onChange}
        icon={
          <ToggleButton
            type="button"
            onClick={togglePasswordVisibility}
            aria-label={showPassword ? 'Hide password' : 'Show password'}
          >
            {showPassword ? <EyeOffIcon /> : <EyeIcon />}
          </ToggleButton>
        }
        {...props}
      />

      {showStrength && value && (
        <StrengthIndicator>
          <StrengthBar>
            <StrengthFill 
              percentage={strength.percentage} 
              color={strength.color} 
            />
          </StrengthBar>
          <StrengthText>
            <StrengthLabel color={strength.color}>
              {strength.label}
            </StrengthLabel>
            <span>{strength.score}/5</span>
          </StrengthText>
        </StrengthIndicator>
      )}

      {showRequirements && value && (
        <RequirementsList>
          {requirements.map((req, index) => (
            <Requirement key={index} met={req.met}>
              <span>{req.met ? '✓' : '○'}</span>
              <span>{req.label}</span>
            </Requirement>
          ))}
        </RequirementsList>
      )}
    </PasswordContainer>
  );
}

export default PasswordInput;
