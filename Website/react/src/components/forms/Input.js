import React, { forwardRef } from 'react';
import styled from 'styled-components';

const InputContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 6px;
  width: 100%;
`;

const Label = styled.label`
  font-size: 14px;
  font-weight: 500;
  color: #333;
  
  ${props => props.required && `
    &::after {
      content: ' *';
      color: #dc3545;
    }
  `}
`;

const InputWrapper = styled.div`
  position: relative;
  display: flex;
  align-items: center;
`;

const StyledInput = styled.input`
  width: 100%;
  padding: 12px 16px;
  padding-right: ${props => props.hasIcon ? '48px' : '16px'};
  border: 2px solid ${props => {
    if (props.error) return '#dc3545';
    if (props.success) return '#28a745';
    return '#e9ecef';
  }};
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  background-color: white;

  &:focus {
    outline: none;
    border-color: ${props => props.error ? '#dc3545' : '#007bff'};
    box-shadow: 0 0 0 3px ${props => {
      if (props.error) return 'rgba(220, 53, 69, 0.25)';
      return 'rgba(0, 123, 255, 0.25)';
    }};
  }

  &:disabled {
    background-color: #f8f9fa;
    cursor: not-allowed;
    opacity: 0.6;
  }

  &::placeholder {
    color: #6c757d;
  }
`;

const IconContainer = styled.div`
  position: absolute;
  right: 12px;
  display: flex;
  align-items: center;
  color: #6c757d;
  pointer-events: none;
`;

const ErrorMessage = styled.span`
  font-size: 14px;
  color: #dc3545;
  margin-top: 4px;
`;

const SuccessMessage = styled.span`
  font-size: 14px;
  color: #28a745;
  margin-top: 4px;
`;

const HelpText = styled.span`
  font-size: 14px;
  color: #6c757d;
  margin-top: 4px;
`;

const Input = forwardRef(({
  label,
  error,
  success,
  helpText,
  icon,
  required = false,
  className = '',
  ...props
}, ref) => {
  const hasIcon = !!icon;
  const inputId = props.id || `input-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <InputContainer className={className}>
      {label && (
        <Label htmlFor={inputId} required={required}>
          {label}
        </Label>
      )}
      
      <InputWrapper>
        <StyledInput
          ref={ref}
          id={inputId}
          error={!!error}
          success={!!success}
          hasIcon={hasIcon}
          required={required}
          {...props}
        />
        
        {hasIcon && (
          <IconContainer>
            {icon}
          </IconContainer>
        )}
      </InputWrapper>

      {error && <ErrorMessage>{error}</ErrorMessage>}
      {success && !error && <SuccessMessage>{success}</SuccessMessage>}
      {helpText && !error && !success && <HelpText>{helpText}</HelpText>}
    </InputContainer>
  );
});

Input.displayName = 'Input';

export default Input;
