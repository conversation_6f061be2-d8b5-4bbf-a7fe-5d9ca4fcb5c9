import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import toast from 'react-hot-toast';

import { apiService } from '../../services/api';
import { useForm } from '../../hooks/useForm';
import { authValidation } from '../../utils/validation';
import Input from '../../components/forms/Input';
import Button from '../../components/common/Button';

const ForgotPasswordContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
`;

const ForgotPasswordCard = styled.div`
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
`;

const Logo = styled.div`
  text-align: center;
  margin-bottom: 32px;
  
  img {
    height: 60px;
    margin-bottom: 16px;
  }
  
  h1 {
    color: #333;
    font-size: 28px;
    margin-bottom: 8px;
  }
  
  p {
    color: #666;
    font-size: 16px;
    line-height: 1.5;
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const FormActions = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 24px;
`;

const BackToLoginLink = styled(Link)`
  color: #007bff;
  text-decoration: none;
  font-size: 14px;
  text-align: center;
  
  &:hover {
    text-decoration: underline;
  }
`;

const SuccessMessage = styled.div`
  background-color: #d4edda;
  color: #155724;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 20px;
  text-align: center;
  border: 1px solid #c3e6cb;
`;

function ForgotPassword() {
  const [emailSent, setEmailSent] = useState(false);
  const [sentEmail, setSentEmail] = useState('');

  const {
    values,
    errors,
    touched,
    isSubmitting,
    handleChange,
    handleBlur,
    handleSubmit,
    setError,
  } = useForm(
    { email: '' },
    { email: authValidation.email }
  );

  const onSubmit = async (formData) => {
    try {
      await apiService.forgotPassword(formData.email);
      
      setEmailSent(true);
      setSentEmail(formData.email);
      toast.success('Password reset link sent to your email!');
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to send reset email';
      setError('email', errorMessage);
      toast.error(errorMessage);
    }
  };

  const handleResendEmail = async () => {
    try {
      await apiService.forgotPassword(sentEmail);
      toast.success('Password reset link sent again!');
    } catch (error) {
      toast.error('Failed to resend email. Please try again.');
    }
  };

  if (emailSent) {
    return (
      <ForgotPasswordContainer>
        <ForgotPasswordCard>
          <Logo>
            <img src="/img/stemblocklogo.png" alt="STEMBlock" />
            <h1>Check Your Email</h1>
            <p>
              We've sent a password reset link to <strong>{sentEmail}</strong>
            </p>
          </Logo>

          <SuccessMessage>
            <p>
              Please check your email and click the link to reset your password.
              The link will expire in 1 hour.
            </p>
          </SuccessMessage>

          <FormActions>
            <Button
              variant="outline"
              onClick={handleResendEmail}
              fullWidth
            >
              Resend Email
            </Button>

            <BackToLoginLink to="/login">
              Back to Login
            </BackToLoginLink>
          </FormActions>
        </ForgotPasswordCard>
      </ForgotPasswordContainer>
    );
  }

  return (
    <ForgotPasswordContainer>
      <ForgotPasswordCard>
        <Logo>
          <img src="/img/stemblocklogo.png" alt="STEMBlock" />
          <h1>Forgot Password</h1>
          <p>
            Enter your email address and we'll send you a link to reset your password.
          </p>
        </Logo>

        <Form onSubmit={(e) => handleSubmit(onSubmit)(e)}>
          <Input
            type="email"
            label="Email Address"
            placeholder="Enter your email"
            value={values.email}
            onChange={(e) => handleChange('email', e.target.value)}
            onBlur={() => handleBlur('email')}
            error={touched.email ? errors.email : ''}
            required
          />

          <FormActions>
            <Button
              type="submit"
              loading={isSubmitting}
              fullWidth
              size="large"
            >
              Send Reset Link
            </Button>

            <BackToLoginLink to="/login">
              Back to Login
            </BackToLoginLink>
          </FormActions>
        </Form>
      </ForgotPasswordCard>
    </ForgotPasswordContainer>
  );
}

export default ForgotPassword;
