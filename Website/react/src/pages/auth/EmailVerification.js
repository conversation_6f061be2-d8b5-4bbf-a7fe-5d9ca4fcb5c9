import React, { useState, useEffect } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import styled from 'styled-components';
import toast from 'react-hot-toast';

import { apiService } from '../../services/api';
import Button from '../../components/common/Button';
import LoadingSpinner from '../../components/common/LoadingSpinner';

const VerificationContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
`;

const VerificationCard = styled.div`
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
  text-align: center;
`;

const Logo = styled.div`
  margin-bottom: 32px;
  
  img {
    height: 60px;
    margin-bottom: 16px;
  }
  
  h1 {
    color: #333;
    font-size: 28px;
    margin-bottom: 8px;
  }
`;

const StatusIcon = styled.div`
  font-size: 64px;
  margin-bottom: 24px;
`;

const Message = styled.div`
  margin-bottom: 24px;
  
  p {
    color: #666;
    font-size: 16px;
    line-height: 1.5;
    margin-bottom: 12px;
  }
`;

const Actions = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const ResendSection = styled.div`
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e9ecef;
  
  p {
    color: #666;
    font-size: 14px;
    margin-bottom: 12px;
  }
`;

function EmailVerification() {
  const [searchParams] = useSearchParams();
  const [verificationState, setVerificationState] = useState('loading'); // loading, success, error
  const [errorMessage, setErrorMessage] = useState('');
  const [isResending, setIsResending] = useState(false);
  
  const token = searchParams.get('token');

  useEffect(() => {
    if (!token) {
      setVerificationState('error');
      setErrorMessage('No verification token provided');
      return;
    }

    verifyEmail();
  }, [token]);

  const verifyEmail = async () => {
    try {
      setVerificationState('loading');
      await apiService.verifyEmail(token);
      
      setVerificationState('success');
      toast.success('Email verified successfully!');
    } catch (error) {
      setVerificationState('error');
      const errorMsg = error.response?.data?.error || 'Verification failed';
      setErrorMessage(errorMsg);
      toast.error(errorMsg);
    }
  };

  const handleResendVerification = async () => {
    try {
      setIsResending(true);
      // Note: This would need the user's email, which we don't have here
      // In a real app, you might store this in localStorage or ask the user to enter it
      toast.success('Please go to the registration page to resend verification email');
    } catch (error) {
      toast.error('Failed to resend verification email');
    } finally {
      setIsResending(false);
    }
  };

  const renderContent = () => {
    switch (verificationState) {
      case 'loading':
        return (
          <>
            <StatusIcon>⏳</StatusIcon>
            <Message>
              <p>Verifying your email address...</p>
            </Message>
            <LoadingSpinner size="large" />
          </>
        );

      case 'success':
        return (
          <>
            <StatusIcon>✅</StatusIcon>
            <Message>
              <p><strong>Email verified successfully!</strong></p>
              <p>Your account is now active. You can sign in to access your dashboard.</p>
            </Message>
            <Actions>
              <Button
                as={Link}
                to="/login"
                size="large"
                fullWidth
              >
                Continue to Login
              </Button>
            </Actions>
          </>
        );

      case 'error':
        return (
          <>
            <StatusIcon>❌</StatusIcon>
            <Message>
              <p><strong>Verification Failed</strong></p>
              <p>{errorMessage}</p>
            </Message>
            <Actions>
              <Button
                onClick={verifyEmail}
                variant="outline"
                size="large"
                fullWidth
              >
                Try Again
              </Button>
              <Button
                as={Link}
                to="/register"
                size="large"
                fullWidth
              >
                Back to Registration
              </Button>
            </Actions>
            <ResendSection>
              <p>Didn't receive the verification email?</p>
              <Button
                onClick={handleResendVerification}
                loading={isResending}
                variant="ghost"
                size="small"
              >
                Resend Verification Email
              </Button>
            </ResendSection>
          </>
        );

      default:
        return null;
    }
  };

  return (
    <VerificationContainer>
      <VerificationCard>
        <Logo>
          <img src="/img/stemblocklogo.png" alt="STEMBlock" />
          <h1>Email Verification</h1>
        </Logo>
        
        {renderContent()}
      </VerificationCard>
    </VerificationContainer>
  );
}

export default EmailVerification;
