import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import styled from 'styled-components';
import toast from 'react-hot-toast';

import { apiService } from '../../services/api';
import { useForm } from '../../hooks/useForm';
import { authValidation } from '../../utils/validation';
import PasswordInput from '../../components/forms/PasswordInput';
import Button from '../../components/common/Button';

const ResetPasswordContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
`;

const ResetPasswordCard = styled.div`
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
`;

const Logo = styled.div`
  text-align: center;
  margin-bottom: 32px;
  
  img {
    height: 60px;
    margin-bottom: 16px;
  }
  
  h1 {
    color: #333;
    font-size: 28px;
    margin-bottom: 8px;
  }
  
  p {
    color: #666;
    font-size: 16px;
    line-height: 1.5;
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const FormActions = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 24px;
`;

const BackToLoginLink = styled(Link)`
  color: #007bff;
  text-decoration: none;
  font-size: 14px;
  text-align: center;
  
  &:hover {
    text-decoration: underline;
  }
`;

const ErrorMessage = styled.div`
  background-color: #f8d7da;
  color: #721c24;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 20px;
  text-align: center;
  border: 1px solid #f5c6cb;
`;

const SuccessMessage = styled.div`
  background-color: #d4edda;
  color: #155724;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 20px;
  text-align: center;
  border: 1px solid #c3e6cb;
`;

function ResetPassword() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [resetSuccess, setResetSuccess] = useState(false);
  const [tokenError, setTokenError] = useState('');
  
  const token = searchParams.get('token');

  const {
    values,
    errors,
    touched,
    isSubmitting,
    handleChange,
    handleBlur,
    handleSubmit,
    setError,
  } = useForm(
    { password: '', confirmPassword: '' },
    {
      password: authValidation.password,
      confirmPassword: authValidation.confirmPassword,
    }
  );

  useEffect(() => {
    if (!token) {
      setTokenError('Invalid or missing reset token. Please request a new password reset.');
    }
  }, [token]);

  const onSubmit = async (formData) => {
    if (!token) {
      setTokenError('Invalid reset token');
      return;
    }

    try {
      await apiService.resetPassword(token, formData.password);
      
      setResetSuccess(true);
      toast.success('Password reset successful!');
      
      // Redirect to login after 3 seconds
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to reset password';
      
      if (error.response?.status === 400) {
        setTokenError('Invalid or expired reset token. Please request a new password reset.');
      } else {
        setError('password', errorMessage);
      }
      
      toast.error(errorMessage);
    }
  };

  if (tokenError) {
    return (
      <ResetPasswordContainer>
        <ResetPasswordCard>
          <Logo>
            <img src="/img/stemblocklogo.png" alt="STEMBlock" />
            <h1>Reset Password</h1>
          </Logo>

          <ErrorMessage>
            {tokenError}
          </ErrorMessage>

          <FormActions>
            <Button
              as={Link}
              to="/forgot-password"
              fullWidth
              size="large"
            >
              Request New Reset Link
            </Button>

            <BackToLoginLink to="/login">
              Back to Login
            </BackToLoginLink>
          </FormActions>
        </ResetPasswordCard>
      </ResetPasswordContainer>
    );
  }

  if (resetSuccess) {
    return (
      <ResetPasswordContainer>
        <ResetPasswordCard>
          <Logo>
            <img src="/img/stemblocklogo.png" alt="STEMBlock" />
            <h1>Password Reset Successful!</h1>
          </Logo>

          <SuccessMessage>
            <p>
              Your password has been reset successfully!
            </p>
            <p style={{ marginTop: '12px', fontSize: '14px' }}>
              You will be redirected to the login page in a few seconds...
            </p>
          </SuccessMessage>

          <Button
            as={Link}
            to="/login"
            fullWidth
            size="large"
          >
            Go to Login
          </Button>
        </ResetPasswordCard>
      </ResetPasswordContainer>
    );
  }

  return (
    <ResetPasswordContainer>
      <ResetPasswordCard>
        <Logo>
          <img src="/img/stemblocklogo.png" alt="STEMBlock" />
          <h1>Reset Password</h1>
          <p>Enter your new password below.</p>
        </Logo>

        <Form onSubmit={(e) => handleSubmit(onSubmit)(e)}>
          <PasswordInput
            label="New Password"
            placeholder="Enter your new password"
            value={values.password}
            onChange={(e) => handleChange('password', e.target.value)}
            onBlur={() => handleBlur('password')}
            error={touched.password ? errors.password : ''}
            showStrength={true}
            showRequirements={true}
            required
          />

          <PasswordInput
            label="Confirm New Password"
            placeholder="Confirm your new password"
            value={values.confirmPassword}
            onChange={(e) => handleChange('confirmPassword', e.target.value)}
            onBlur={() => handleBlur('confirmPassword')}
            error={touched.confirmPassword ? errors.confirmPassword : ''}
            required
          />

          <FormActions>
            <Button
              type="submit"
              loading={isSubmitting}
              fullWidth
              size="large"
            >
              Reset Password
            </Button>

            <BackToLoginLink to="/login">
              Back to Login
            </BackToLoginLink>
          </FormActions>
        </Form>
      </ResetPasswordCard>
    </ResetPasswordContainer>
  );
}

export default ResetPassword;
