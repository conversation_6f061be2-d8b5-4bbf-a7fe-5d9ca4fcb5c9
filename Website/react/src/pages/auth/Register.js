import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import toast from 'react-hot-toast';

import { useAuth } from '../../contexts/AuthContext';
import { useForm } from '../../hooks/useForm';
import { authValidation } from '../../utils/validation';
import Input from '../../components/forms/Input';
import PasswordInput from '../../components/forms/PasswordInput';
import Button from '../../components/common/Button';

const RegisterContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
`;

const RegisterCard = styled.div`
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 450px;
`;

const Logo = styled.div`
  text-align: center;
  margin-bottom: 32px;
  
  img {
    height: 60px;
    margin-bottom: 16px;
  }
  
  h1 {
    color: #333;
    font-size: 28px;
    margin-bottom: 8px;
  }
  
  p {
    color: #666;
    font-size: 16px;
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const FormActions = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 24px;
`;

const LoginPrompt = styled.div`
  text-align: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e9ecef;
  color: #666;
  
  a {
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
    
    &:hover {
      text-decoration: underline;
    }
  }
`;

const SuccessMessage = styled.div`
  background-color: #d4edda;
  color: #155724;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 20px;
  text-align: center;
  border: 1px solid #c3e6cb;
`;

function Register() {
  const { register } = useAuth();
  const navigate = useNavigate();
  const [registrationSuccess, setRegistrationSuccess] = React.useState(false);

  const {
    values,
    errors,
    touched,
    isSubmitting,
    handleChange,
    handleBlur,
    handleSubmit,
  } = useForm(
    { username: '', email: '', password: '', confirmPassword: '' },
    authValidation
  );

  const onSubmit = async (formData) => {
    try {
      await register({
        username: formData.username,
        email: formData.email,
        password: formData.password,
      });

      setRegistrationSuccess(true);
      
      // Redirect to login after 3 seconds
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (error) {
      // Error is already handled by AuthContext
      console.error('Registration error:', error);
    }
  };

  if (registrationSuccess) {
    return (
      <RegisterContainer>
        <RegisterCard>
          <Logo>
            <img src="/img/stemblocklogo.png" alt="STEMBlock" />
            <h1>Registration Successful!</h1>
          </Logo>
          
          <SuccessMessage>
            <p>
              Your account has been created successfully! 
              Please check your email to verify your account.
            </p>
            <p style={{ marginTop: '12px', fontSize: '14px' }}>
              You will be redirected to the login page in a few seconds...
            </p>
          </SuccessMessage>

          <Button
            as={Link}
            to="/login"
            fullWidth
            size="large"
          >
            Go to Login
          </Button>
        </RegisterCard>
      </RegisterContainer>
    );
  }

  return (
    <RegisterContainer>
      <RegisterCard>
        <Logo>
          <img src="/img/stemblocklogo.png" alt="STEMBlock" />
          <h1>Create Account</h1>
          <p>Join STEMBlock today</p>
        </Logo>

        <Form onSubmit={(e) => handleSubmit(onSubmit)(e)}>
          <Input
            type="text"
            label="Username"
            placeholder="Choose a username"
            value={values.username}
            onChange={(e) => handleChange('username', e.target.value)}
            onBlur={() => handleBlur('username')}
            error={touched.username ? errors.username : ''}
            helpText="3-30 characters, letters, numbers, hyphens, and underscores only"
            required
          />

          <Input
            type="email"
            label="Email Address"
            placeholder="Enter your email"
            value={values.email}
            onChange={(e) => handleChange('email', e.target.value)}
            onBlur={() => handleBlur('email')}
            error={touched.email ? errors.email : ''}
            required
          />

          <PasswordInput
            label="Password"
            placeholder="Create a password"
            value={values.password}
            onChange={(e) => handleChange('password', e.target.value)}
            onBlur={() => handleBlur('password')}
            error={touched.password ? errors.password : ''}
            showStrength={true}
            showRequirements={true}
            required
          />

          <PasswordInput
            label="Confirm Password"
            placeholder="Confirm your password"
            value={values.confirmPassword}
            onChange={(e) => handleChange('confirmPassword', e.target.value)}
            onBlur={() => handleBlur('confirmPassword')}
            error={touched.confirmPassword ? errors.confirmPassword : ''}
            required
          />

          <FormActions>
            <Button
              type="submit"
              loading={isSubmitting}
              fullWidth
              size="large"
            >
              Create Account
            </Button>
          </FormActions>
        </Form>

        <LoginPrompt>
          Already have an account?{' '}
          <Link to="/login">Sign in here!</Link>
        </LoginPrompt>
      </RegisterCard>
    </RegisterContainer>
  );
}

export default Register;
