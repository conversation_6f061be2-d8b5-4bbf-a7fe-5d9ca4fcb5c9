import React, { useState, useEffect, useCallback } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import toast from 'react-hot-toast';

import { useAuth } from '../../contexts/AuthContext';
import { useForm } from '../../hooks/useForm';
import { authValidation } from '../../utils/validation';
import Input from '../../components/forms/Input';
import PasswordInput from '../../components/forms/PasswordInput';
import Button from '../../components/common/Button';

const LoginContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
`;

const LoginCard = styled.div`
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
`;

const Logo = styled.div`
  text-align: center;
  margin-bottom: 32px;
  
  img {
    height: 60px;
    margin-bottom: 16px;
  }
  
  h1 {
    color: #333;
    font-size: 28px;
    margin-bottom: 8px;
  }
  
  p {
    color: #666;
    font-size: 16px;
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const FormActions = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 24px;
`;

const ForgotPasswordLink = styled(Link)`
  color: #007bff;
  text-decoration: none;
  font-size: 14px;
  text-align: center;
  
  &:hover {
    text-decoration: underline;
  }
`;

const SignUpPrompt = styled.div`
  text-align: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e9ecef;
  color: #666;
  
  a {
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
    
    &:hover {
      text-decoration: underline;
    }
  }
`;

const Login = () => {
  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Local state for form handling
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);

  // Custom form validation using React hooks
  const {
    values,
    errors,
    touched,
    isSubmitting,
    handleChange,
    handleBlur,
    handleSubmit,
  } = useForm(
    { email: '', password: '' },
    {
      email: authValidation.email,
      password: [authValidation.password[0]], // Only required validation for login
    }
  );

  // Effect hook for handling redirect on mount
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const redirectUrl = searchParams.get('redirect');

    if (redirectUrl) {
      toast.info('Please log in to continue');
    }
  }, [location.search]);

  // Memoized submit handler using useCallback
  const onSubmit = useCallback(async (formData) => {
    setIsLoading(true);

    try {
      await login({
        username: formData.email, // Backend expects username field
        password: formData.password,
      });

      // Get redirect URL from query params
      const searchParams = new URLSearchParams(location.search);
      const redirect = searchParams.get('redirect') || '/dashboard';

      toast.success('Login successful!');
      navigate(redirect, { replace: true });
    } catch (error) {
      // Error is already handled by AuthContext
      console.error('Login error:', error);
      toast.error('Login failed. Please check your credentials.');
    } finally {
      setIsLoading(false);
    }
  }, [login, location.search, navigate]);

  // Handle input changes with React state
  const handleInputChange = useCallback((field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    handleChange(field, value);
  }, [handleChange]);

  // JSX render with React components
  return (
    <LoginContainer>
      <LoginCard>
        <Logo>
          <img src="/img/stemblocklogo.png" alt="STEMBlock" />
          <h1>Welcome Back</h1>
          <p>Sign in to your account</p>
        </Logo>

        <Form onSubmit={(e) => handleSubmit(onSubmit)(e)}>
          <Input
            type="email"
            label="Email Address"
            placeholder="Enter your email"
            value={values.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            onBlur={() => handleBlur('email')}
            error={touched.email ? errors.email : ''}
            required
          />

          <PasswordInput
            label="Password"
            placeholder="Enter your password"
            value={values.password}
            onChange={(e) => handleInputChange('password', e.target.value)}
            onBlur={() => handleBlur('password')}
            error={touched.password ? errors.password : ''}
            required
          />

          {/* Remember me checkbox - React controlled component */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', margin: '16px 0' }}>
            <input
              type="checkbox"
              id="rememberMe"
              checked={rememberMe}
              onChange={(e) => setRememberMe(e.target.checked)}
            />
            <label htmlFor="rememberMe" style={{ fontSize: '14px', color: '#666' }}>
              Remember me
            </label>
          </div>

          <FormActions>
            <Button
              type="submit"
              loading={isSubmitting || isLoading}
              fullWidth
              size="large"
              disabled={!values.email || !values.password}
            >
              {isLoading ? 'Signing In...' : 'Sign In'}
            </Button>

            <ForgotPasswordLink to="/forgot-password">
              Forgot your password?
            </ForgotPasswordLink>
          </FormActions>
        </Form>

        <SignUpPrompt>
          Don't have an account?{' '}
          <Link to="/register">Sign up here!</Link>
        </SignUpPrompt>
      </LoginCard>
    </LoginContainer>
  );
};

export default Login;
