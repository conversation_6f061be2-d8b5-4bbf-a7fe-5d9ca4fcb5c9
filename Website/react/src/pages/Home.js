import React from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { useAuth } from '../contexts/AuthContext';
import Button from '../components/common/Button';

const HomeContainer = styled.div`
  min-height: calc(100vh - 80px);
`;

const HeroSection = styled.section`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 100px 20px;
  text-align: center;
`;

const HeroContent = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const HeroTitle = styled.h1`
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 24px;
  line-height: 1.2;
  
  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const HeroSubtitle = styled.p`
  font-size: 1.25rem;
  margin-bottom: 40px;
  opacity: 0.9;
  line-height: 1.6;
`;

const HeroActions = styled.div`
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
`;

const FeaturesSection = styled.section`
  padding: 80px 20px;
  background-color: #f8f9fa;
`;

const SectionTitle = styled.h2`
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 60px;
  color: #333;
`;

const FeaturesGrid = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
`;

const FeatureCard = styled.div`
  background: white;
  padding: 40px 30px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
`;

const FeatureIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 20px;
`;

const FeatureTitle = styled.h3`
  font-size: 1.5rem;
  margin-bottom: 16px;
  color: #333;
`;

const FeatureDescription = styled.p`
  color: #666;
  line-height: 1.6;
`;

const CTASection = styled.section`
  padding: 80px 20px;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  text-align: center;
`;

const CTAContent = styled.div`
  max-width: 600px;
  margin: 0 auto;
`;

const CTATitle = styled.h2`
  font-size: 2.5rem;
  margin-bottom: 20px;
`;

const CTADescription = styled.p`
  font-size: 1.2rem;
  margin-bottom: 40px;
  opacity: 0.9;
`;

function Home() {
  const { isAuthenticated } = useAuth();

  const features = [
    {
      icon: '🤖',
      title: 'Interactive Learning',
      description: 'Hands-on projects and simulations that make STEM concepts come alive through practical application.',
    },
    {
      icon: '🎯',
      title: 'Personalized Path',
      description: 'Adaptive learning paths that adjust to your pace and learning style for optimal progress.',
    },
    {
      icon: '🏆',
      title: 'Achievement System',
      description: 'Earn certificates and badges as you complete courses and master new skills.',
    },
    {
      icon: '👥',
      title: 'Community Support',
      description: 'Connect with fellow learners and expert instructors in our vibrant learning community.',
    },
    {
      icon: '📱',
      title: 'Mobile Learning',
      description: 'Learn anywhere, anytime with our responsive platform that works on all devices.',
    },
    {
      icon: '🔬',
      title: 'Real-world Projects',
      description: 'Apply your knowledge to real-world challenges and build a portfolio of impressive projects.',
    },
  ];

  return (
    <HomeContainer>
      <HeroSection>
        <HeroContent>
          <HeroTitle>Master STEM with STEMBlock</HeroTitle>
          <HeroSubtitle>
            Interactive learning platform designed to make Science, Technology, 
            Engineering, and Mathematics education engaging and accessible for everyone.
          </HeroSubtitle>
          <HeroActions>
            {isAuthenticated ? (
              <Button
                as={Link}
                to="/dashboard"
                size="large"
                style={{ backgroundColor: 'white', color: '#667eea' }}
              >
                Go to Dashboard
              </Button>
            ) : (
              <>
                <Button
                  as={Link}
                  to="/register"
                  size="large"
                  style={{ backgroundColor: 'white', color: '#667eea' }}
                >
                  Get Started Free
                </Button>
                <Button
                  as={Link}
                  to="/courses"
                  variant="outline"
                  size="large"
                  style={{ borderColor: 'white', color: 'white' }}
                >
                  Explore Courses
                </Button>
              </>
            )}
          </HeroActions>
        </HeroContent>
      </HeroSection>

      <FeaturesSection>
        <SectionTitle>Why Choose STEMBlock?</SectionTitle>
        <FeaturesGrid>
          {features.map((feature, index) => (
            <FeatureCard key={index}>
              <FeatureIcon>{feature.icon}</FeatureIcon>
              <FeatureTitle>{feature.title}</FeatureTitle>
              <FeatureDescription>{feature.description}</FeatureDescription>
            </FeatureCard>
          ))}
        </FeaturesGrid>
      </FeaturesSection>

      <CTASection>
        <CTAContent>
          <CTATitle>Ready to Start Learning?</CTATitle>
          <CTADescription>
            Join thousands of students already mastering STEM skills with STEMBlock.
            Start your journey today!
          </CTADescription>
          {!isAuthenticated && (
            <Button
              as={Link}
              to="/register"
              size="large"
              style={{ backgroundColor: 'white', color: '#28a745' }}
            >
              Sign Up Now
            </Button>
          )}
        </CTAContent>
      </CTASection>
    </HomeContainer>
  );
}

export default Home;
