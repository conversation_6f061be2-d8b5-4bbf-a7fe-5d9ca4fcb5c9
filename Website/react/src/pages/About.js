import React from 'react';
import styled from 'styled-components';

const AboutContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
`;

const AboutHeader = styled.div`
  text-align: center;
  margin-bottom: 60px;
`;

const AboutTitle = styled.h1`
  font-size: 3rem;
  margin-bottom: 16px;
  color: #333;
`;

const AboutSubtitle = styled.p`
  font-size: 1.2rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
`;

const ContentSection = styled.section`
  margin-bottom: 60px;
`;

const SectionTitle = styled.h2`
  font-size: 2rem;
  margin-bottom: 24px;
  color: #333;
  text-align: center;
`;

const SectionContent = styled.div`
  max-width: 800px;
  margin: 0 auto;
  font-size: 1.1rem;
  line-height: 1.8;
  color: #555;
  
  p {
    margin-bottom: 20px;
  }
`;

const ValuesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  margin-top: 40px;
`;

const ValueCard = styled.div`
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
  border: 1px solid #e9ecef;
`;

const ValueIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 20px;
`;

const ValueTitle = styled.h3`
  font-size: 1.3rem;
  margin-bottom: 16px;
  color: #333;
`;

const ValueDescription = styled.p`
  color: #666;
  line-height: 1.6;
`;

const TeamSection = styled.section`
  background: #f8f9fa;
  padding: 60px 20px;
  margin: 60px -20px;
  border-radius: 12px;
`;

const TeamGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
`;

const TeamMember = styled.div`
  text-align: center;
`;

const MemberAvatar = styled.div`
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2.5rem;
  font-weight: bold;
  margin: 0 auto 20px;
`;

const MemberName = styled.h4`
  font-size: 1.2rem;
  margin-bottom: 8px;
  color: #333;
`;

const MemberRole = styled.p`
  color: #007bff;
  font-weight: 500;
  margin-bottom: 12px;
`;

const MemberBio = styled.p`
  color: #666;
  font-size: 14px;
  line-height: 1.6;
`;

function About() {
  const values = [
    {
      icon: '🎯',
      title: 'Excellence',
      description: 'We strive for excellence in everything we do, from course content to student support.',
    },
    {
      icon: '🤝',
      title: 'Accessibility',
      description: 'Quality STEM education should be accessible to everyone, regardless of background or location.',
    },
    {
      icon: '💡',
      title: 'Innovation',
      description: 'We embrace innovative teaching methods and cutting-edge technology to enhance learning.',
    },
    {
      icon: '🌱',
      title: 'Growth',
      description: 'We believe in continuous learning and growth for both our students and our platform.',
    },
  ];

  const teamMembers = [
    {
      name: 'Dr. Sarah Johnson',
      role: 'Founder & CEO',
      bio: 'Former MIT professor with 15 years of experience in STEM education and curriculum development.',
      avatar: 'SJ',
    },
    {
      name: 'Michael Chen',
      role: 'CTO',
      bio: 'Software engineer and educator passionate about creating innovative learning technologies.',
      avatar: 'MC',
    },
    {
      name: 'Emily Rodriguez',
      role: 'Head of Curriculum',
      bio: 'Educational specialist with expertise in designing engaging and effective STEM curricula.',
      avatar: 'ER',
    },
    {
      name: 'David Kim',
      role: 'Lead Developer',
      bio: 'Full-stack developer focused on building scalable and user-friendly educational platforms.',
      avatar: 'DK',
    },
  ];

  return (
    <AboutContainer>
      <AboutHeader>
        <AboutTitle>About STEMBlock</AboutTitle>
        <AboutSubtitle>
          Empowering the next generation of innovators through interactive STEM education
        </AboutSubtitle>
      </AboutHeader>

      <ContentSection>
        <SectionTitle>Our Mission</SectionTitle>
        <SectionContent>
          <p>
            At STEMBlock, we believe that Science, Technology, Engineering, and Mathematics 
            education should be engaging, accessible, and practical. Our mission is to 
            revolutionize STEM learning by providing interactive, hands-on experiences 
            that prepare students for the challenges of tomorrow.
          </p>
          <p>
            We combine cutting-edge technology with proven educational methodologies to 
            create learning experiences that are not only effective but also enjoyable. 
            Our platform serves students of all ages and backgrounds, from curious 
            beginners to advanced learners seeking to deepen their expertise.
          </p>
        </SectionContent>
      </ContentSection>

      <ContentSection>
        <SectionTitle>Our Story</SectionTitle>
        <SectionContent>
          <p>
            STEMBlock was founded in 2023 by a team of educators and technologists who 
            recognized the need for more engaging and practical STEM education. Having 
            witnessed firsthand the challenges students face in traditional learning 
            environments, we set out to create a platform that would make STEM subjects 
            more accessible and exciting.
          </p>
          <p>
            Our journey began with a simple question: "How can we make learning STEM 
            subjects as engaging as playing a game?" This led us to develop our unique 
            block-based learning approach, where complex concepts are broken down into 
            manageable, interactive modules that build upon each other.
          </p>
          <p>
            Today, STEMBlock serves thousands of students worldwide, helping them develop 
            the skills and knowledge they need to succeed in an increasingly technology-driven world.
          </p>
        </SectionContent>
      </ContentSection>

      <ContentSection>
        <SectionTitle>Our Values</SectionTitle>
        <ValuesGrid>
          {values.map((value, index) => (
            <ValueCard key={index}>
              <ValueIcon>{value.icon}</ValueIcon>
              <ValueTitle>{value.title}</ValueTitle>
              <ValueDescription>{value.description}</ValueDescription>
            </ValueCard>
          ))}
        </ValuesGrid>
      </ContentSection>

      <TeamSection>
        <SectionTitle>Meet Our Team</SectionTitle>
        <TeamGrid>
          {teamMembers.map((member, index) => (
            <TeamMember key={index}>
              <MemberAvatar>{member.avatar}</MemberAvatar>
              <MemberName>{member.name}</MemberName>
              <MemberRole>{member.role}</MemberRole>
              <MemberBio>{member.bio}</MemberBio>
            </TeamMember>
          ))}
        </TeamGrid>
      </TeamSection>

      <ContentSection>
        <SectionTitle>Join Our Community</SectionTitle>
        <SectionContent>
          <p>
            Whether you're a student looking to explore STEM subjects, an educator 
            seeking innovative teaching tools, or a parent wanting to support your 
            child's learning journey, STEMBlock has something for you.
          </p>
          <p>
            Join our growing community of learners, educators, and innovators who are 
            passionate about STEM education. Together, we're building the future of learning.
          </p>
        </SectionContent>
      </ContentSection>
    </AboutContainer>
  );
}

export default About;
