import React from 'react';
import styled from 'styled-components';
import { useAuth } from '../contexts/AuthContext';
import { useForm } from '../hooks/useForm';
import { authValidation } from '../utils/validation';
import Input from '../components/forms/Input';
import Button from '../components/common/Button';
import toast from 'react-hot-toast';

const ProfileContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 40px 20px;
`;

const ProfileHeader = styled.div`
  text-align: center;
  margin-bottom: 40px;
`;

const ProfileTitle = styled.h1`
  font-size: 2.5rem;
  margin-bottom: 16px;
  color: #333;
`;

const ProfileSubtitle = styled.p`
  color: #666;
  font-size: 1.1rem;
`;

const ProfileContent = styled.div`
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 40px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const ProfileSidebar = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const ProfileMain = styled.div`
  background: white;
  padding: 32px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
`;

const SidebarCard = styled.div`
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
`;

const SidebarTitle = styled.h3`
  margin-bottom: 16px;
  color: #333;
`;

const Avatar = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  font-weight: bold;
  margin: 0 auto 16px;
`;

const UserInfo = styled.div`
  text-align: center;
  
  h4 {
    margin-bottom: 4px;
    color: #333;
  }
  
  p {
    color: #666;
    font-size: 14px;
  }
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-top: 16px;
`;

const StatItem = styled.div`
  text-align: center;
  
  .number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #007bff;
  }
  
  .label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
`;

const SectionTitle = styled.h2`
  margin-bottom: 24px;
  color: #333;
  font-size: 1.5rem;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const FormActions = styled.div`
  display: flex;
  gap: 16px;
  margin-top: 24px;
  
  @media (max-width: 480px) {
    flex-direction: column;
  }
`;

function Profile() {
  const { user } = useAuth();

  const {
    values,
    errors,
    touched,
    isSubmitting,
    handleChange,
    handleBlur,
    handleSubmit,
    reset,
  } = useForm(
    {
      username: user?.username || '',
      email: user?.email || '',
      firstName: '',
      lastName: '',
      bio: '',
    },
    {
      username: authValidation.username,
      email: authValidation.email,
    }
  );

  const onSubmit = async (formData) => {
    try {
      // In a real app, you would call an API to update the profile
      console.log('Updating profile:', formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success('Profile updated successfully!');
    } catch (error) {
      toast.error('Failed to update profile');
    }
  };

  const handleCancel = () => {
    reset();
    toast.info('Changes cancelled');
  };

  return (
    <ProfileContainer>
      <ProfileHeader>
        <ProfileTitle>Profile Settings</ProfileTitle>
        <ProfileSubtitle>
          Manage your account information and preferences
        </ProfileSubtitle>
      </ProfileHeader>

      <ProfileContent>
        <ProfileSidebar>
          <SidebarCard>
            <Avatar>
              {user?.username?.charAt(0).toUpperCase()}
            </Avatar>
            <UserInfo>
              <h4>{user?.username}</h4>
              <p>{user?.email}</p>
              <p>Member since January 2024</p>
            </UserInfo>
            <StatsGrid>
              <StatItem>
                <div className="number">12</div>
                <div className="label">Courses</div>
              </StatItem>
              <StatItem>
                <div className="number">8</div>
                <div className="label">Certificates</div>
              </StatItem>
            </StatsGrid>
          </SidebarCard>

          <SidebarCard>
            <SidebarTitle>Account Status</SidebarTitle>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>Email Verified</span>
                <span style={{ color: '#28a745' }}>✓</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>Account Type</span>
                <span style={{ color: '#007bff' }}>Student</span>
              </div>
            </div>
          </SidebarCard>
        </ProfileSidebar>

        <ProfileMain>
          <SectionTitle>Personal Information</SectionTitle>
          
          <Form onSubmit={(e) => handleSubmit(onSubmit)(e)}>
            <Input
              label="Username"
              type="text"
              value={values.username}
              onChange={(e) => handleChange('username', e.target.value)}
              onBlur={() => handleBlur('username')}
              error={touched.username ? errors.username : ''}
              required
            />

            <Input
              label="Email Address"
              type="email"
              value={values.email}
              onChange={(e) => handleChange('email', e.target.value)}
              onBlur={() => handleBlur('email')}
              error={touched.email ? errors.email : ''}
              required
            />

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
              <Input
                label="First Name"
                type="text"
                value={values.firstName}
                onChange={(e) => handleChange('firstName', e.target.value)}
                placeholder="Enter your first name"
              />

              <Input
                label="Last Name"
                type="text"
                value={values.lastName}
                onChange={(e) => handleChange('lastName', e.target.value)}
                placeholder="Enter your last name"
              />
            </div>

            <div>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                Bio
              </label>
              <textarea
                value={values.bio}
                onChange={(e) => handleChange('bio', e.target.value)}
                placeholder="Tell us about yourself..."
                rows={4}
                style={{
                  width: '100%',
                  padding: '12px 16px',
                  border: '2px solid #e9ecef',
                  borderRadius: '6px',
                  fontSize: '16px',
                  fontFamily: 'inherit',
                  resize: 'vertical',
                }}
              />
            </div>

            <FormActions>
              <Button
                type="submit"
                loading={isSubmitting}
                size="large"
              >
                Save Changes
              </Button>
              <Button
                type="button"
                variant="outline"
                size="large"
                onClick={handleCancel}
              >
                Cancel
              </Button>
            </FormActions>
          </Form>
        </ProfileMain>
      </ProfileContent>
    </ProfileContainer>
  );
}

export default Profile;
