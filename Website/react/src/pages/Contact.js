import React from 'react';
import styled from 'styled-components';
import { useForm } from '../hooks/useForm';
import { validationRules } from '../utils/validation';
import Input from '../components/forms/Input';
import Button from '../components/common/Button';
import toast from 'react-hot-toast';

const ContactContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
`;

const ContactHeader = styled.div`
  text-align: center;
  margin-bottom: 60px;
`;

const ContactTitle = styled.h1`
  font-size: 3rem;
  margin-bottom: 16px;
  color: #333;
`;

const ContactSubtitle = styled.p`
  font-size: 1.2rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
`;

const ContactContent = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 40px;
  }
`;

const ContactForm = styled.div`
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
`;

const ContactInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 40px;
`;

const InfoCard = styled.div`
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
`;

const InfoIcon = styled.div`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  margin-bottom: 20px;
`;

const InfoTitle = styled.h3`
  font-size: 1.3rem;
  margin-bottom: 12px;
  color: #333;
`;

const InfoText = styled.p`
  color: #666;
  line-height: 1.6;
  margin-bottom: 8px;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const FormTitle = styled.h2`
  font-size: 1.8rem;
  margin-bottom: 20px;
  color: #333;
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 16px;
  font-family: inherit;
  resize: vertical;
  min-height: 120px;
  transition: border-color 0.2s ease-in-out;
  
  &:focus {
    outline: none;
    border-color: #007bff;
  }
  
  &::placeholder {
    color: #6c757d;
  }
`;

const MapSection = styled.div`
  margin-top: 60px;
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  text-align: center;
`;

const MapTitle = styled.h2`
  font-size: 1.8rem;
  margin-bottom: 20px;
  color: #333;
`;

const MapPlaceholder = styled.div`
  height: 300px;
  background: #f8f9fa;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 1.1rem;
  border: 2px dashed #dee2e6;
`;

function Contact() {
  const {
    values,
    errors,
    touched,
    isSubmitting,
    handleChange,
    handleBlur,
    handleSubmit,
    reset,
  } = useForm(
    {
      name: '',
      email: '',
      subject: '',
      message: '',
    },
    {
      name: [validationRules.required('Name is required')],
      email: [
        validationRules.required('Email is required'),
        validationRules.email(),
      ],
      subject: [validationRules.required('Subject is required')],
      message: [
        validationRules.required('Message is required'),
        validationRules.minLength(10, 'Message must be at least 10 characters'),
      ],
    }
  );

  const onSubmit = async (formData) => {
    try {
      // In a real app, you would send this to your backend
      console.log('Contact form submission:', formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success('Message sent successfully! We\'ll get back to you soon.');
      reset();
    } catch (error) {
      toast.error('Failed to send message. Please try again.');
    }
  };

  return (
    <ContactContainer>
      <ContactHeader>
        <ContactTitle>Contact Us</ContactTitle>
        <ContactSubtitle>
          Have questions? We'd love to hear from you. Send us a message and we'll respond as soon as possible.
        </ContactSubtitle>
      </ContactHeader>

      <ContactContent>
        <ContactForm>
          <FormTitle>Send us a Message</FormTitle>
          <Form onSubmit={(e) => handleSubmit(onSubmit)(e)}>
            <Input
              label="Full Name"
              type="text"
              value={values.name}
              onChange={(e) => handleChange('name', e.target.value)}
              onBlur={() => handleBlur('name')}
              error={touched.name ? errors.name : ''}
              placeholder="Enter your full name"
              required
            />

            <Input
              label="Email Address"
              type="email"
              value={values.email}
              onChange={(e) => handleChange('email', e.target.value)}
              onBlur={() => handleBlur('email')}
              error={touched.email ? errors.email : ''}
              placeholder="Enter your email address"
              required
            />

            <Input
              label="Subject"
              type="text"
              value={values.subject}
              onChange={(e) => handleChange('subject', e.target.value)}
              onBlur={() => handleBlur('subject')}
              error={touched.subject ? errors.subject : ''}
              placeholder="What is this about?"
              required
            />

            <div>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                Message *
              </label>
              <TextArea
                value={values.message}
                onChange={(e) => handleChange('message', e.target.value)}
                onBlur={() => handleBlur('message')}
                placeholder="Tell us more about your inquiry..."
                required
              />
              {touched.message && errors.message && (
                <span style={{ color: '#dc3545', fontSize: '14px', marginTop: '4px', display: 'block' }}>
                  {errors.message}
                </span>
              )}
            </div>

            <Button
              type="submit"
              loading={isSubmitting}
              size="large"
              fullWidth
            >
              Send Message
            </Button>
          </Form>
        </ContactForm>

        <ContactInfo>
          <InfoCard>
            <InfoIcon>📧</InfoIcon>
            <InfoTitle>Email Us</InfoTitle>
            <InfoText><EMAIL></InfoText>
            <InfoText>We typically respond within 24 hours</InfoText>
          </InfoCard>

          <InfoCard>
            <InfoIcon>📞</InfoIcon>
            <InfoTitle>Call Us</InfoTitle>
            <InfoText>(*************</InfoText>
            <InfoText>Monday - Friday, 9:00 AM - 5:00 PM EST</InfoText>
          </InfoCard>

          <InfoCard>
            <InfoIcon>📍</InfoIcon>
            <InfoTitle>Visit Us</InfoTitle>
            <InfoText>50 Crowther Ln suite 140</InfoText>
            <InfoText>Fredericton, NB E3C 0J1</InfoText>
            <InfoText>Canada</InfoText>
          </InfoCard>

          <InfoCard>
            <InfoIcon>💬</InfoIcon>
            <InfoTitle>Follow Us</InfoTitle>
            <InfoText>Stay connected on social media</InfoText>
            <div style={{ display: 'flex', gap: '12px', marginTop: '12px' }}>
              <a href="#" style={{ color: '#007bff', textDecoration: 'none' }}>Facebook</a>
              <a href="#" style={{ color: '#007bff', textDecoration: 'none' }}>Instagram</a>
              <a href="#" style={{ color: '#007bff', textDecoration: 'none' }}>Twitter</a>
            </div>
          </InfoCard>
        </ContactInfo>
      </ContactContent>

      <MapSection>
        <MapTitle>Find Us</MapTitle>
        <MapPlaceholder>
          📍 Interactive Map Coming Soon
          <br />
          50 Crowther Ln suite 140, Fredericton, NB E3C 0J1
        </MapPlaceholder>
      </MapSection>
    </ContactContainer>
  );
}

export default Contact;
