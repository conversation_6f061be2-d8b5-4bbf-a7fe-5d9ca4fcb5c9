import React, { useState } from 'react';
import styled from 'styled-components';
import { useAuth } from '../contexts/AuthContext';
import Button from '../components/common/Button';
import { Link } from 'react-router-dom';

const CoursesContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
`;

const CoursesHeader = styled.div`
  text-align: center;
  margin-bottom: 60px;
`;

const CoursesTitle = styled.h1`
  font-size: 3rem;
  margin-bottom: 16px;
  color: #333;
`;

const CoursesSubtitle = styled.p`
  font-size: 1.2rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
`;

const FilterSection = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  flex-wrap: wrap;
  gap: 20px;
`;

const FilterTabs = styled.div`
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
`;

const FilterTab = styled.button`
  padding: 8px 16px;
  border: 2px solid ${props => props.active ? '#007bff' : '#e9ecef'};
  background: ${props => props.active ? '#007bff' : 'white'};
  color: ${props => props.active ? 'white' : '#666'};
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #007bff;
    color: ${props => props.active ? 'white' : '#007bff'};
  }
`;

const SearchBox = styled.input`
  padding: 10px 16px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 16px;
  width: 300px;
  max-width: 100%;
  
  &:focus {
    outline: none;
    border-color: #007bff;
  }
`;

const CoursesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 30px;
`;

const CourseCard = styled.div`
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #e9ecef;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
`;

const CourseImage = styled.div`
  height: 200px;
  background: linear-gradient(135deg, ${props => props.gradient || '#667eea 0%, #764ba2 100%'});
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: white;
`;

const CourseContent = styled.div`
  padding: 24px;
`;

const CourseCategory = styled.span`
  display: inline-block;
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 12px;
`;

const CourseTitle = styled.h3`
  font-size: 1.25rem;
  margin-bottom: 12px;
  color: #333;
  line-height: 1.4;
`;

const CourseDescription = styled.p`
  color: #666;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 16px;
`;

const CourseFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
`;

const CourseDifficulty = styled.span`
  font-size: 12px;
  color: #666;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
`;

const CoursePrice = styled.span`
  font-weight: bold;
  color: #28a745;
  font-size: 1.1rem;
`;

function Courses() {
  const { isAuthenticated } = useAuth();
  const [activeFilter, setActiveFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const categories = [
    { id: 'all', label: 'All Courses' },
    { id: 'robotics', label: 'Robotics' },
    { id: 'programming', label: 'Programming' },
    { id: 'engineering', label: 'Engineering' },
    { id: 'mathematics', label: 'Mathematics' },
    { id: 'science', label: 'Science' },
  ];

  const courses = [
    {
      id: 1,
      title: 'Introduction to Robotics',
      description: 'Learn the fundamentals of robotics, including basic programming, sensors, and actuators.',
      category: 'robotics',
      difficulty: 'Beginner',
      price: 'Free',
      icon: '🤖',
      gradient: '#667eea 0%, #764ba2 100%',
    },
    {
      id: 2,
      title: 'Python Programming Basics',
      description: 'Master the basics of Python programming with hands-on projects and real-world applications.',
      category: 'programming',
      difficulty: 'Beginner',
      price: '$49',
      icon: '🐍',
      gradient: '#f093fb 0%, #f5576c 100%',
    },
    {
      id: 3,
      title: 'Advanced Engineering Design',
      description: 'Explore advanced engineering principles and design methodologies for complex systems.',
      category: 'engineering',
      difficulty: 'Advanced',
      price: '$99',
      icon: '⚙️',
      gradient: '#4facfe 0%, #00f2fe 100%',
    },
    {
      id: 4,
      title: 'Calculus for Engineers',
      description: 'Comprehensive calculus course tailored for engineering applications and problem-solving.',
      category: 'mathematics',
      difficulty: 'Intermediate',
      price: '$79',
      icon: '📐',
      gradient: '#43e97b 0%, #38f9d7 100%',
    },
    {
      id: 5,
      title: 'Physics Fundamentals',
      description: 'Explore the fundamental principles of physics through interactive experiments and simulations.',
      category: 'science',
      difficulty: 'Beginner',
      price: 'Free',
      icon: '⚛️',
      gradient: '#fa709a 0%, #fee140 100%',
    },
    {
      id: 6,
      title: 'Machine Learning Basics',
      description: 'Introduction to machine learning concepts, algorithms, and practical applications.',
      category: 'programming',
      difficulty: 'Intermediate',
      price: '$129',
      icon: '🧠',
      gradient: '#a8edea 0%, #fed6e3 100%',
    },
  ];

  const filteredCourses = courses.filter(course => {
    const matchesFilter = activeFilter === 'all' || course.category === activeFilter;
    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  return (
    <CoursesContainer>
      <CoursesHeader>
        <CoursesTitle>Explore Our Courses</CoursesTitle>
        <CoursesSubtitle>
          Discover a wide range of STEM courses designed to help you build practical skills 
          and advance your knowledge in science, technology, engineering, and mathematics.
        </CoursesSubtitle>
      </CoursesHeader>

      <FilterSection>
        <FilterTabs>
          {categories.map(category => (
            <FilterTab
              key={category.id}
              active={activeFilter === category.id}
              onClick={() => setActiveFilter(category.id)}
            >
              {category.label}
            </FilterTab>
          ))}
        </FilterTabs>
        
        <SearchBox
          type="text"
          placeholder="Search courses..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </FilterSection>

      <CoursesGrid>
        {filteredCourses.map(course => (
          <CourseCard key={course.id}>
            <CourseImage gradient={course.gradient}>
              {course.icon}
            </CourseImage>
            <CourseContent>
              <CourseCategory>{course.category}</CourseCategory>
              <CourseTitle>{course.title}</CourseTitle>
              <CourseDescription>{course.description}</CourseDescription>
              <CourseFooter>
                <CourseDifficulty>{course.difficulty}</CourseDifficulty>
                <CoursePrice>{course.price}</CoursePrice>
              </CourseFooter>
              <Button 
                fullWidth 
                style={{ marginTop: '16px' }}
                as={isAuthenticated ? 'button' : Link}
                to={!isAuthenticated ? '/register' : undefined}
              >
                {isAuthenticated ? 'Enroll Now' : 'Sign Up to Enroll'}
              </Button>
            </CourseContent>
          </CourseCard>
        ))}
      </CoursesGrid>

      {filteredCourses.length === 0 && (
        <div style={{ textAlign: 'center', padding: '60px 20px', color: '#666' }}>
          <h3>No courses found</h3>
          <p>Try adjusting your search or filter criteria.</p>
        </div>
      )}
    </CoursesContainer>
  );
}

export default Courses;
