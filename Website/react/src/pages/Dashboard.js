import React from 'react';
import styled from 'styled-components';
import { useAuth } from '../contexts/AuthContext';
import { useQuery } from 'react-query';
import { apiService } from '../services/api';
import LoadingSpinner from '../components/common/LoadingSpinner';
import Button from '../components/common/Button';

const DashboardContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
`;

const WelcomeSection = styled.div`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px;
  border-radius: 12px;
  margin-bottom: 40px;
  text-align: center;
`;

const WelcomeTitle = styled.h1`
  font-size: 2.5rem;
  margin-bottom: 16px;
  font-weight: 700;
`;

const WelcomeSubtitle = styled.p`
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 24px;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
`;

const StatCard = styled.div`
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
  border: 1px solid #e9ecef;
`;

const StatNumber = styled.div`
  font-size: 2rem;
  font-weight: 700;
  color: #007bff;
  margin-bottom: 8px;
`;

const StatLabel = styled.div`
  color: #666;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const ContentGrid = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const MainContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 32px;
`;

const Sidebar = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const Section = styled.div`
  background: white;
  padding: 32px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
`;

const SectionTitle = styled.h2`
  font-size: 1.5rem;
  margin-bottom: 24px;
  color: #333;
`;

const ProfileInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const InfoRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f8f9fa;
  
  &:last-child {
    border-bottom: none;
  }
`;

const InfoLabel = styled.span`
  font-weight: 500;
  color: #666;
`;

const InfoValue = styled.span`
  color: #333;
`;

const CoursesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const CourseCard = styled.div`
  padding: 20px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
  }
`;

const CourseTitle = styled.h3`
  margin-bottom: 8px;
  color: #333;
`;

const CourseDescription = styled.p`
  color: #666;
  font-size: 14px;
  margin-bottom: 12px;
`;

const CourseProgress = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const ProgressBar = styled.div`
  flex: 1;
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
`;

const ProgressFill = styled.div`
  height: 100%;
  background-color: #28a745;
  width: ${props => props.progress}%;
  transition: width 0.3s ease;
`;

const ProgressText = styled.span`
  font-size: 12px;
  color: #666;
  font-weight: 500;
`;

function Dashboard() {
  const { user } = useAuth();

  // Mock data - in a real app, this would come from your API
  const mockCourses = [
    {
      id: 1,
      title: 'Introduction to Robotics',
      description: 'Learn the basics of robotics and programming',
      progress: 75,
    },
    {
      id: 2,
      title: 'Advanced Programming',
      description: 'Master advanced programming concepts',
      progress: 45,
    },
    {
      id: 3,
      title: 'Engineering Design',
      description: 'Design and build engineering solutions',
      progress: 20,
    },
  ];

  const stats = [
    { number: mockCourses.length, label: 'Enrolled Courses' },
    { number: Math.round(mockCourses.reduce((acc, course) => acc + course.progress, 0) / mockCourses.length), label: 'Average Progress' },
    { number: mockCourses.filter(course => course.progress === 100).length, label: 'Completed Courses' },
    { number: '12', label: 'Certificates Earned' },
  ];

  return (
    <DashboardContainer>
      <WelcomeSection>
        <WelcomeTitle>Welcome back, {user?.username}!</WelcomeTitle>
        <WelcomeSubtitle>
          Ready to continue your STEM learning journey?
        </WelcomeSubtitle>
        <Button variant="outline" size="large" style={{ color: 'white', borderColor: 'white' }}>
          Browse New Courses
        </Button>
      </WelcomeSection>

      <StatsGrid>
        {stats.map((stat, index) => (
          <StatCard key={index}>
            <StatNumber>{stat.number}</StatNumber>
            <StatLabel>{stat.label}</StatLabel>
          </StatCard>
        ))}
      </StatsGrid>

      <ContentGrid>
        <MainContent>
          <Section>
            <SectionTitle>Your Courses</SectionTitle>
            <CoursesList>
              {mockCourses.map(course => (
                <CourseCard key={course.id}>
                  <CourseTitle>{course.title}</CourseTitle>
                  <CourseDescription>{course.description}</CourseDescription>
                  <CourseProgress>
                    <ProgressBar>
                      <ProgressFill progress={course.progress} />
                    </ProgressBar>
                    <ProgressText>{course.progress}%</ProgressText>
                  </CourseProgress>
                </CourseCard>
              ))}
            </CoursesList>
          </Section>
        </MainContent>

        <Sidebar>
          <Section>
            <SectionTitle>Profile Information</SectionTitle>
            <ProfileInfo>
              <InfoRow>
                <InfoLabel>Username:</InfoLabel>
                <InfoValue>{user?.username}</InfoValue>
              </InfoRow>
              <InfoRow>
                <InfoLabel>Email:</InfoLabel>
                <InfoValue>{user?.email}</InfoValue>
              </InfoRow>
              <InfoRow>
                <InfoLabel>Role:</InfoLabel>
                <InfoValue>{user?.role || 'Student'}</InfoValue>
              </InfoRow>
              <InfoRow>
                <InfoLabel>Member Since:</InfoLabel>
                <InfoValue>January 2024</InfoValue>
              </InfoRow>
            </ProfileInfo>
            <Button variant="outline" fullWidth style={{ marginTop: '20px' }}>
              Edit Profile
            </Button>
          </Section>

          <Section>
            <SectionTitle>Quick Actions</SectionTitle>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              <Button fullWidth>Browse Courses</Button>
              <Button variant="outline" fullWidth>View Certificates</Button>
              <Button variant="outline" fullWidth>Settings</Button>
            </div>
          </Section>
        </Sidebar>
      </ContentGrid>
    </DashboardContainer>
  );
}

export default Dashboard;
