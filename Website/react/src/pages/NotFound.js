import React from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import Button from '../components/common/Button';

const NotFoundContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  text-align: center;
`;

const NotFoundCard = styled.div`
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 60px 40px;
  max-width: 500px;
  width: 100%;
`;

const ErrorCode = styled.h1`
  font-size: 8rem;
  font-weight: 700;
  color: #007bff;
  margin-bottom: 20px;
  line-height: 1;
  
  @media (max-width: 480px) {
    font-size: 6rem;
  }
`;

const ErrorTitle = styled.h2`
  font-size: 2rem;
  color: #333;
  margin-bottom: 16px;
`;

const ErrorMessage = styled.p`
  color: #666;
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 40px;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
`;

const SuggestionsList = styled.ul`
  text-align: left;
  margin: 30px 0;
  padding: 0;
  list-style: none;
  
  li {
    margin-bottom: 8px;
    color: #666;
    
    &:before {
      content: '•';
      color: #007bff;
      font-weight: bold;
      margin-right: 8px;
    }
  }
`;

function NotFound() {
  const suggestions = [
    'Check the URL for typos',
    'Go back to the previous page',
    'Visit our homepage',
    'Browse our courses',
    'Contact us if you need help'
  ];

  return (
    <NotFoundContainer>
      <NotFoundCard>
        <ErrorCode>404</ErrorCode>
        <ErrorTitle>Page Not Found</ErrorTitle>
        <ErrorMessage>
          Oops! The page you're looking for doesn't exist. It might have been moved, 
          deleted, or you entered the wrong URL.
        </ErrorMessage>
        
        <div style={{ textAlign: 'left', marginBottom: '30px' }}>
          <h4 style={{ color: '#333', marginBottom: '16px' }}>Here's what you can do:</h4>
          <SuggestionsList>
            {suggestions.map((suggestion, index) => (
              <li key={index}>{suggestion}</li>
            ))}
          </SuggestionsList>
        </div>

        <ActionButtons>
          <Button
            as={Link}
            to="/"
            size="large"
          >
            Go Home
          </Button>
          <Button
            as={Link}
            to="/courses"
            variant="outline"
            size="large"
          >
            Browse Courses
          </Button>
        </ActionButtons>
      </NotFoundCard>
    </NotFoundContainer>
  );
}

export default NotFound;
