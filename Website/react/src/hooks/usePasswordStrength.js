import { useMemo } from 'react';

export function usePasswordStrength(password) {
  const strength = useMemo(() => {
    if (!password) return { score: 0, label: 'Enter password', color: '#ddd' };

    let score = 0;
    const checks = {
      length: password.length >= 8,
      lowercase: /[a-z]/.test(password),
      uppercase: /[A-Z]/.test(password),
      numbers: /[0-9]/.test(password),
      symbols: /[^A-Za-z0-9]/.test(password),
    };

    // Calculate score
    Object.values(checks).forEach(check => {
      if (check) score++;
    });

    // Determine label and color
    const strengthLevels = [
      { label: 'Very Weak', color: '#ff4444' },
      { label: 'Weak', color: '#ff8800' },
      { label: 'Fair', color: '#ffaa00' },
      { label: 'Good', color: '#88aa00' },
      { label: 'Strong', color: '#44aa44' },
    ];

    const level = strengthLevels[score - 1] || strengthLevels[0];

    return {
      score,
      percentage: (score / 5) * 100,
      label: level.label,
      color: level.color,
      checks,
    };
  }, [password]);

  return strength;
}
