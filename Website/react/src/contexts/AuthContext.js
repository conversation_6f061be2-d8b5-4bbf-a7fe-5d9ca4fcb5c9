import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';
import { useQuery, useQueryClient } from 'react-query';
import toast from 'react-hot-toast';

import { apiService } from '../services/api';
import { SESSION_CONFIG } from '../config/api';

// Create React Context
const AuthContext = createContext(undefined);

// Auth action types for reducer
const AUTH_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_USER: 'SET_USER',
  SET_ERROR: 'SET_ERROR',
  LOGOUT: 'LOGOUT',
  CLEAR_ERROR: 'CLEAR_ERROR',
};

// Initial state for auth reducer
const initialState = {
  user: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
};

// Reducer
function authReducer(state, action) {
  switch (action.type) {
    case AUTH_ACTIONS.SET_LOADING:
      return { ...state, isLoading: action.payload };
    
    case AUTH_ACTIONS.SET_USER:
      return {
        ...state,
        user: action.payload,
        isAuthenticated: !!action.payload,
        isLoading: false,
        error: null,
      };
    
    case AUTH_ACTIONS.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };
    
    case AUTH_ACTIONS.LOGOUT:
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };
    
    case AUTH_ACTIONS.CLEAR_ERROR:
      return { ...state, error: null };
    
    default:
      return state;
  }
}

// React Context Provider component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);
  const queryClient = useQueryClient();

  // Query to check authentication status
  const { data: authData, error: authError, isLoading } = useQuery(
    'auth-status',
    () => apiService.checkAuth(),
    {
      retry: false,
      refetchOnWindowFocus: true,
      refetchInterval: SESSION_CONFIG.CHECK_INTERVAL,
      onSuccess: (response) => {
        dispatch({ type: AUTH_ACTIONS.SET_USER, payload: response.data.user });
      },
      onError: (error) => {
        if (error.response?.status === 401) {
          dispatch({ type: AUTH_ACTIONS.LOGOUT });
        } else {
          dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: error.message });
        }
      },
    }
  );

  // Session warning effect
  useEffect(() => {
    let warningTimer;
    let warningShown = false;

    if (state.isAuthenticated) {
      warningTimer = setTimeout(() => {
        if (!warningShown) {
          warningShown = true;
          const shouldExtend = window.confirm(
            'Your session will expire in 5 minutes. Would you like to extend it?'
          );
          
          if (shouldExtend) {
            // Trigger a refetch to extend session
            queryClient.invalidateQueries('auth-status');
            warningShown = false;
          }
        }
      }, SESSION_CONFIG.WARNING_TIME);
    }

    return () => {
      if (warningTimer) clearTimeout(warningTimer);
    };
  }, [state.isAuthenticated, queryClient]);

  // Auth methods using useCallback for optimization
  const login = useCallback(async (credentials) => {
    try {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
      const response = await apiService.login(credentials);

      // Invalidate auth query to refetch user data
      queryClient.invalidateQueries('auth-status');

      toast.success('Login successful!');
      return response.data;
    } catch (error) {
      const errorMessage = error.response?.data?.error || error.message;
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage });
      toast.error(errorMessage);
      throw error;
    }
  }, [queryClient]);

  const register = useCallback(async (userData) => {
    try {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
      const response = await apiService.register(userData);

      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
      toast.success('Registration successful! Please check your email to verify your account.');
      return response.data;
    } catch (error) {
      const errorMessage = error.response?.data?.error || error.message;
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage });
      toast.error(errorMessage);
      throw error;
    }
  }, []);

  const logout = useCallback(() => {
    dispatch({ type: AUTH_ACTIONS.LOGOUT });
    queryClient.clear(); // Clear all cached data

    // Clear cookies by setting them to expire
    document.cookie = 'jwt=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

    toast.success('Logged out successfully');
  }, [queryClient]);

  const clearError = useCallback(() => {
    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });
  }, []);

  const value = {
    ...state,
    login,
    register,
    logout,
    clearError,
    isLoading: isLoading || state.isLoading,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Custom React hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
