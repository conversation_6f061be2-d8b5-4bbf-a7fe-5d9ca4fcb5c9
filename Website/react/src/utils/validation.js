// Validation rules
export const validationRules = {
  required: (message = 'This field is required') => (value) => {
    if (!value || (typeof value === 'string' && !value.trim())) {
      return message;
    }
    return '';
  },

  email: (message = 'Please enter a valid email address') => (value) => {
    if (!value) return '';
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      return message;
    }
    return '';
  },

  minLength: (min, message) => (value) => {
    if (!value) return '';
    if (value.length < min) {
      return message || `Must be at least ${min} characters long`;
    }
    return '';
  },

  maxLength: (max, message) => (value) => {
    if (!value) return '';
    if (value.length > max) {
      return message || `Must be no more than ${max} characters long`;
    }
    return '';
  },

  username: (message = 'Username must be 3-30 characters and contain only letters, numbers, hyphens, and underscores') => (value) => {
    if (!value) return '';
    const usernameRegex = /^[a-zA-Z0-9_-]{3,30}$/;
    if (!usernameRegex.test(value)) {
      return message;
    }
    return '';
  },

  password: (message = 'Password must be at least 8 characters and contain uppercase, lowercase, number, and special character') => (value) => {
    if (!value) return '';
    
    const checks = {
      length: value.length >= 8,
      lowercase: /[a-z]/.test(value),
      uppercase: /[A-Z]/.test(value),
      numbers: /[0-9]/.test(value),
      symbols: /[^A-Za-z0-9]/.test(value),
    };

    const failedChecks = Object.entries(checks)
      .filter(([_, passed]) => !passed)
      .map(([check]) => check);

    if (failedChecks.length > 0) {
      return message;
    }
    return '';
  },

  confirmPassword: (message = 'Passwords do not match') => (value, allValues) => {
    if (!value) return '';
    if (value !== allValues.password) {
      return message;
    }
    return '';
  },

  custom: (validator, message) => (value, allValues) => {
    const isValid = validator(value, allValues);
    return isValid ? '' : message;
  },
};

// Common validation sets
export const authValidation = {
  email: [
    validationRules.required('Email is required'),
    validationRules.email(),
  ],
  
  username: [
    validationRules.required('Username is required'),
    validationRules.username(),
  ],
  
  password: [
    validationRules.required('Password is required'),
    validationRules.password(),
  ],
  
  confirmPassword: [
    validationRules.required('Please confirm your password'),
    validationRules.confirmPassword(),
  ],
};

// Utility functions
export const formatValidationErrors = (errors) => {
  if (!errors || !Array.isArray(errors)) return {};
  
  return errors.reduce((acc, error) => {
    acc[error.field] = error.message;
    return acc;
  }, {});
};
