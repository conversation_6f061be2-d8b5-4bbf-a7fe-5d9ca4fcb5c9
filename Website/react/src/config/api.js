// API Configuration
export const API_CONFIG = {
  BASE_URL: 'https://stemblock-login-gljgs.ondigitalocean.app',
  FRONTEND_URL: 'https://www.stemblock.ca',
  
  // Endpoints
  ENDPOINTS: {
    LOGIN: '/login',
    REGISTER: '/register',
    FORGOT_PASSWORD: '/forgot-password',
    RESET_PASSWORD: '/reset-password',
    VERIFY_EMAIL: '/verify-email',
    DASHBOARD_AUTH: '/dashboard-auth',
    RESEND_VERIFICATION: '/resend-verification',
    CHECK_EMAIL: '/check-email', // If backend supports this
  },

  // Request timeout
  TIMEOUT: 10000,

  // Retry configuration
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
};

// HTTP Status codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
};

// Error messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Unable to connect to server. Please check your internet connection.',
  TIMEOUT_ERROR: 'Request timed out. Please try again.',
  RATE_LIMIT_ERROR: 'Too many requests. Please wait a moment and try again.',
  VALIDATION_ERROR: 'Please fix the validation errors and try again.',
  GENERIC_ERROR: 'An unexpected error occurred. Please try again.',
  SESSION_EXPIRED: 'Your session has expired. Please log in again.',
};

// Session configuration
export const SESSION_CONFIG = {
  CHECK_INTERVAL: 5 * 60 * 1000, // Check every 5 minutes
  WARNING_TIME: 25 * 60 * 1000, // Warn 5 minutes before expiry (assuming 30min session)
  REFRESH_THRESHOLD: 5 * 60 * 1000, // Refresh if less than 5 minutes remaining
};
