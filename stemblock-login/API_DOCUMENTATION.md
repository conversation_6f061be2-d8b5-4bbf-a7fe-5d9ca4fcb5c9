# STEMBlock Backend API Documentation

## Base URL
```
Production: https://your-backend-domain.com
```

> **Note**: Both frontend and backend are cloud-hosted. No local development URLs needed.

## Authentication
- Uses JWT tokens stored in HTTP-only cookies
- Include `credentials: 'include'` in fetch requests
- Protected routes return 401 if not authenticated

## CORS Configuration
- Your frontend domain must be added to `FRONTEND_URL` environment variable
- Supports credentials (cookies)
- Allows common headers and methods

---

## API Endpoints

### 1. User Registration
**POST** `/register`

**Request Body:**
```json
{
  "username": "string (3-30 chars, alphanumeric + underscore/hyphen)",
  "email": "string (valid email format)",
  "password": "string (8+ chars, must contain uppercase, lowercase, number, special char)"
}
```

**Success Response (200):**
```json
{
  "message": "Registration successful. Please check your email to verify your account."
}
```

**Error Response (400):**
```json
{
  "error": "Validation failed",
  "validation_errors": [
    {
      "field": "username",
      "message": "username must be at least 3 characters long"
    },
    {
      "field": "password", 
      "message": "password must contain at least one uppercase letter"
    }
  ]
}
```

### 2. User Login
**POST** `/login`

**Request Body:**
```json
{
  "username": "string",
  "password": "string"
}
```

**Success Response (200):**
```json
{
  "message": "Login successful",
  "user": {
    "username": "testuser",
    "email": "<EMAIL>",
    "role": "student"
  }
}
```

**Error Response (401):**
```json
{
  "error": "Invalid credentials"
}
```

### 3. Email Verification
**GET** `/verify-email?token=<verification_token>`

**Success Response (200):**
```json
{
  "message": "Email verified successfully"
}
```

**Error Response (400):**
```json
{
  "error": "Invalid or expired verification token"
}
```

### 4. Forgot Password
**POST** `/forgot-password`

**Request Body:**
```json
{
  "email": "string (valid email)"
}
```

**Success Response (200):**
```json
{
  "message": "Password reset link sent to your email"
}
```

**Error Response (400):**
```json
{
  "error": "Validation failed",
  "validation_errors": [
    {
      "field": "email",
      "message": "invalid email format"
    }
  ]
}
```

### 5. Reset Password
**POST** `/reset-password`

**Request Body:**
```json
{
  "token": "string (reset token from email)",
  "password": "string (new password)"
}
```

**Success Response (200):**
```json
{
  "message": "Password reset successful"
}
```

### 6. Dashboard Authentication Check
**GET** `/dashboard-auth`

**Headers Required:**
- Cookies with JWT token (automatic if credentials included)

**Success Response (200):**
```json
{
  "message": "Authenticated",
  "user": {
    "username": "testuser",
    "email": "<EMAIL>", 
    "role": "student"
  }
}
```

**Error Response (401):**
```json
{
  "error": "Unauthorized"
}
```

### 7. Resend Verification Email
**POST** `/resend-verification`

**Request Body:**
```json
{
  "email": "string"
}
```

**Success Response (200):**
```json
{
  "message": "Verification email sent"
}
```

---

## Rate Limiting

All endpoints have rate limiting:
- **Login**: 5 attempts per 15 minutes per IP
- **Register**: 3 attempts per 10 minutes per IP  
- **Password Reset**: 3 attempts per 5 minutes per email
- **Resend Verification**: 3 attempts per 5 minutes per email

**Rate Limit Response (429):**
```json
{
  "error": "Too many requests. Please try again later."
}
```

---

## Frontend Integration Example

### JavaScript Fetch Configuration
```javascript
const API_BASE_URL = 'https://your-backend-domain.com';

// Configure fetch with credentials
async function apiCall(endpoint, method = 'GET', data = null) {
  const config = {
    method,
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include', // IMPORTANT: Include cookies
  };

  if (data) {
    config.body = JSON.stringify(data);
  }

  const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
  const result = await response.json();

  if (!response.ok) {
    throw { response: { data: result, status: response.status } };
  }

  return result;
}
```

### Example Usage
```javascript
// Register user
try {
  const result = await apiCall('/register', 'POST', {
    username: 'testuser',
    email: '<EMAIL>',
    password: 'SecurePass123!'
  });
  console.log('Registration successful:', result);
} catch (error) {
  if (error.response?.data?.validation_errors) {
    // Handle validation errors
    error.response.data.validation_errors.forEach(err => {
      console.log(`${err.field}: ${err.message}`);
    });
  }
}

// Login user
try {
  const result = await apiCall('/login', 'POST', {
    username: 'testuser',
    password: 'SecurePass123!'
  });
  console.log('Login successful:', result);
  // User is now authenticated, JWT cookie is set
} catch (error) {
  console.log('Login failed:', error.response?.data?.error);
}

// Check authentication status
try {
  const result = await apiCall('/dashboard-auth');
  console.log('User is authenticated:', result.user);
} catch (error) {
  console.log('User not authenticated');
  // Redirect to login
}
```

---

## Environment Variables for Backend

```bash
# Required for your backend
DB_USER=your_db_user
DB_PASSWORD=your_db_password
SUPPORT_EMAIL_PASSWORD=your_email_password
JWT_SECRET=your_secure_jwt_secret

# Frontend integration
FRONTEND_URL=https://your-frontend-domain.com

# Optional
PORT=8080
BASE_URL=https://your-backend-domain.com
```

---

## Testing the API

You can test the API endpoints using curl:

```bash
# Register
curl -X POST http://localhost:8080/register \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","email":"<EMAIL>","password":"SecurePass123!"}'

# Login
curl -X POST http://localhost:8080/login \
  -H "Content-Type: application/json" \
  -c cookies.txt \
  -d '{"username":"testuser","password":"SecurePass123!"}'

# Check auth (using saved cookies)
curl -X GET http://localhost:8080/dashboard-auth \
  -b cookies.txt
```
