# Bazel build for STEMBlock login Go application
load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library", "go_test")
load("@bazel_gazelle//:def.bzl", "gazelle")

# Gazelle configuration
gazelle(name = "gazelle")

# Main server binary
go_binary(
    name = "stemblock_login_server",
    embed = [":login_lib"],
    visibility = ["//visibility:public"],
)

# Main library
go_library(
    name = "login_lib",
    srcs = ["main.go"],
    importpath = "login",
    visibility = ["//visibility:public"],
    deps = [
        "//cmd/server",
        "//internal/config",
        "@com_github_gin_gonic_gin//:gin",
    ],
)

# Admin tool binary
go_binary(
    name = "admin_tool",
    embed = ["//cmd/admin:admin_lib"],
    visibility = ["//visibility:public"],
)

# Generate token tool binary
go_binary(
    name = "generate_token",
    embed = ["//cmd/generate-token:generate_token_lib"],
    visibility = ["//visibility:public"],
)

# Tests
go_test(
    name = "login_test",
    srcs = glob(["*_test.go"]),
    embed = [":login_lib"],
    deps = [
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)
