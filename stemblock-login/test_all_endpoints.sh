#!/bin/bash

# 🧪 Comprehensive API Endpoint Testing Script
# Tests all endpoints to simulate frontend behavior and find errors

set -e  # Exit on any error

# Configuration
BASE_URL="http://localhost:8080"
TEST_USER="testuser_$(date +%s)"
TEST_EMAIL="test_$(date +%s)@example.com"
TEST_PASSWORD="SecurePass123!"
COOKIE_JAR="/tmp/cookies_$(date +%s).txt"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
    ((PASSED_TESTS++))
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
    ((FAILED_TESTS++))
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

test_endpoint() {
    local name="$1"
    local method="$2"
    local endpoint="$3"
    local data="$4"
    local expected_status="$5"
    local description="$6"
    
    ((TOTAL_TESTS++))
    
    log_info "Testing: $name - $description"
    
    local curl_cmd="curl -s -w '%{http_code}' -o /tmp/response_$$.json"
    curl_cmd="$curl_cmd -X $method"
    curl_cmd="$curl_cmd -H 'Content-Type: application/json'"
    curl_cmd="$curl_cmd -b $COOKIE_JAR -c $COOKIE_JAR"
    
    if [ -n "$data" ]; then
        curl_cmd="$curl_cmd -d '$data'"
    fi
    
    curl_cmd="$curl_cmd '$BASE_URL$endpoint'"
    
    local status_code
    status_code=$(eval $curl_cmd)
    
    local response_body
    response_body=$(cat /tmp/response_$$.json 2>/dev/null || echo "")
    
    if [ "$status_code" = "$expected_status" ]; then
        log_success "$name: HTTP $status_code (Expected: $expected_status)"
        if [ -n "$response_body" ]; then
            echo "   Response: $response_body"
        fi
    else
        log_error "$name: HTTP $status_code (Expected: $expected_status)"
        if [ -n "$response_body" ]; then
            echo "   Response: $response_body"
        fi
    fi
    
    rm -f /tmp/response_$$.json
    echo ""
}

# Start testing
echo "🚀 Starting comprehensive API endpoint testing..."
echo "Base URL: $BASE_URL"
echo "Test User: $TEST_USER"
echo "Test Email: $TEST_EMAIL"
echo "Cookie Jar: $COOKIE_JAR"
echo ""

# Test 1: Home page (UI route)
test_endpoint "HOME_PAGE" "GET" "/" "" "200" "Home page with registration form"

# Test 2: Dashboard page (UI route)
test_endpoint "DASHBOARD_PAGE" "GET" "/dashboard" "" "200" "Dashboard page UI"

# Test 3: Forgot password page (UI route)
test_endpoint "FORGOT_PASSWORD_PAGE" "GET" "/forgot-password" "" "200" "Forgot password page UI"

# Test 4: Reset password page (UI route)
test_endpoint "RESET_PASSWORD_PAGE" "GET" "/reset-password" "" "200" "Reset password page UI"

# Test 5: Swagger documentation
test_endpoint "SWAGGER_DOCS" "GET" "/swagger/index.html" "" "200" "Swagger API documentation"

# Test 6: User registration (valid)
test_endpoint "REGISTER_VALID" "POST" "/register" \
    "{\"username\":\"$TEST_USER\",\"email\":\"$TEST_EMAIL\",\"password\":\"$TEST_PASSWORD\"}" \
    "200" "Valid user registration"

# Test 7: User registration (duplicate username)
test_endpoint "REGISTER_DUPLICATE" "POST" "/register" \
    "{\"username\":\"$TEST_USER\",\"email\":\"different_$TEST_EMAIL\",\"password\":\"$TEST_PASSWORD\"}" \
    "409" "Duplicate username registration"

# Test 8: User registration (invalid password)
test_endpoint "REGISTER_WEAK_PASSWORD" "POST" "/register" \
    "{\"username\":\"weak_$TEST_USER\",\"email\":\"weak_$TEST_EMAIL\",\"password\":\"123\"}" \
    "400" "Weak password registration"

# Test 9: User registration (invalid email)
test_endpoint "REGISTER_INVALID_EMAIL" "POST" "/register" \
    "{\"username\":\"invalid_$TEST_USER\",\"email\":\"invalid-email\",\"password\":\"$TEST_PASSWORD\"}" \
    "400" "Invalid email registration"

# Test 10: User registration (missing fields)
test_endpoint "REGISTER_MISSING_FIELDS" "POST" "/register" \
    "{\"username\":\"$TEST_USER\"}" \
    "400" "Missing required fields"

# Test 11: User login (before email verification)
test_endpoint "LOGIN_UNVERIFIED" "POST" "/login" \
    "{\"username\":\"$TEST_USER\",\"password\":\"$TEST_PASSWORD\"}" \
    "401" "Login with unverified email"

# Test 12: User login (invalid credentials)
test_endpoint "LOGIN_INVALID" "POST" "/login" \
    "{\"username\":\"$TEST_USER\",\"password\":\"wrongpassword\"}" \
    "401" "Login with wrong password"

# Test 13: User login (missing fields)
test_endpoint "LOGIN_MISSING_FIELDS" "POST" "/login" \
    "{\"username\":\"$TEST_USER\"}" \
    "400" "Login with missing password"

# Test 14: Dashboard auth (without authentication)
test_endpoint "DASHBOARD_AUTH_UNAUTH" "GET" "/dashboard-auth" "" "401" "Dashboard access without auth"

# Test 15: Forgot password (valid email)
test_endpoint "FORGOT_PASSWORD_VALID" "POST" "/forgot-password" \
    "{\"email\":\"$TEST_EMAIL\"}" \
    "200" "Valid forgot password request"

# Test 16: Forgot password (invalid email)
test_endpoint "FORGOT_PASSWORD_INVALID" "POST" "/forgot-password" \
    "{\"email\":\"<EMAIL>\"}" \
    "404" "Forgot password with non-existent email"

# Test 17: Forgot password (missing email)
test_endpoint "FORGOT_PASSWORD_MISSING" "POST" "/forgot-password" \
    "{}" \
    "400" "Forgot password without email"

# Test 18: Reset password (without token)
test_endpoint "RESET_PASSWORD_NO_TOKEN" "POST" "/reset-password" \
    "{\"password\":\"NewPassword123!\"}" \
    "400" "Reset password without token"

# Test 19: Reset password (invalid token)
test_endpoint "RESET_PASSWORD_INVALID_TOKEN" "POST" "/reset-password" \
    "{\"token\":\"invalid-token\",\"password\":\"NewPassword123!\"}" \
    "400" "Reset password with invalid token"

# Test 20: Email verification (without token)
test_endpoint "VERIFY_EMAIL_NO_TOKEN" "GET" "/verify-email" "" "400" "Email verification without token"

# Test 21: Email verification (invalid token)
test_endpoint "VERIFY_EMAIL_INVALID_TOKEN" "GET" "/verify-email?token=invalid-token" "" "400" "Email verification with invalid token"

# Test 22: Resend verification (valid)
test_endpoint "RESEND_VERIFICATION_VALID" "POST" "/resend-verification" \
    "{\"email\":\"$TEST_EMAIL\",\"username\":\"$TEST_USER\",\"password\":\"$TEST_PASSWORD\"}" \
    "200" "Valid resend verification request"

# Test 23: Resend verification (missing fields)
test_endpoint "RESEND_VERIFICATION_MISSING" "POST" "/resend-verification" \
    "{\"email\":\"$TEST_EMAIL\"}" \
    "400" "Resend verification with missing fields"

# Test 24: Contact endpoint (if exists)
test_endpoint "CONTACT_FORM" "POST" "/contact" \
    "{\"name\":\"Test User\",\"email\":\"$TEST_EMAIL\",\"subject\":\"Test\",\"message\":\"Test message\"}" \
    "404" "Contact form submission (may not exist)"

# Test 25: Non-existent endpoint
test_endpoint "NON_EXISTENT" "GET" "/non-existent-endpoint" "" "404" "Non-existent endpoint"

# Test 26: JSON parsing test (malformed JSON)
test_endpoint "MALFORMED_JSON" "POST" "/register" \
    "{\"username\":\"test\",\"email\":\"<EMAIL>\",\"password\":" \
    "400" "Malformed JSON request"

# Test 27: Empty request body
test_endpoint "EMPTY_BODY" "POST" "/register" \
    "" \
    "400" "Empty request body"

# Test 28: Wrong content type
log_info "Testing wrong content type..."
((TOTAL_TESTS++))
status_code=$(curl -s -w '%{http_code}' -o /tmp/response_$$.json \
    -X POST \
    -H 'Content-Type: text/plain' \
    -b $COOKIE_JAR -c $COOKIE_JAR \
    -d 'plain text data' \
    "$BASE_URL/register")

if [ "$status_code" = "400" ]; then
    log_success "WRONG_CONTENT_TYPE: HTTP $status_code (Expected: 400)"
else
    log_error "WRONG_CONTENT_TYPE: HTTP $status_code (Expected: 400)"
fi
echo ""

# Test 29: Rate limiting test (multiple rapid requests)
log_info "Testing rate limiting with multiple rapid requests..."
for i in {1..6}; do
    test_endpoint "RATE_LIMIT_$i" "POST" "/register" \
        "{\"username\":\"rate_test_$i\",\"email\":\"rate_test_$<EMAIL>\",\"password\":\"$TEST_PASSWORD\"}" \
        "$([ $i -le 5 ] && echo "200" || echo "429")" \
        "Rate limit test attempt $i"
done

# Test 30: CORS preflight (OPTIONS request)
test_endpoint "CORS_PREFLIGHT" "OPTIONS" "/register" "" "200" "CORS preflight request"

# Cleanup
rm -f $COOKIE_JAR

# Summary
echo "🏁 Testing completed!"
echo "=============================="
echo "Total tests: $TOTAL_TESTS"
echo "Passed: $PASSED_TESTS"
echo "Failed: $FAILED_TESTS"
echo "Success rate: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"
echo ""

if [ $FAILED_TESTS -gt 0 ]; then
    echo "❌ Some tests failed. Check the output above for details."
    exit 1
else
    echo "✅ All tests passed!"
    exit 0
fi
