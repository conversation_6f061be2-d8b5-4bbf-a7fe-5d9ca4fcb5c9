package server

import (
	"context"
	"log/slog"
	"login/internal/auth"
	"login/internal/config"
	"login/internal/db"
	"login/internal/errors"
	"login/internal/middleware"
	"login/internal/user"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	_ "github.com/lib/pq"
	"gorm.io/gorm"

	_ "login/api/docs" // replace with your actual module path

	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

var pgs *gorm.DB
var cfg *config.Config

func StartServer(configuration *config.Config) {
	cfg = configuration

	slog.Info("Initializing server components")

	// Initialize JWT with configuration
	auth.InitJWT(cfg)
	slog.Info("JWT initialized")

	// Initialize database
	pgs = db.InitDB(cfg.Database.DSN())
	slog.Info("Database connection established", "host", cfg.Database.Host, "database", cfg.Database.Name)

	sqlDB, err := pgs.DB()
	if err != nil {
		slog.Error("Failed to get raw DB handle", "error", err)
		panic(err)
	}
	defer sqlDB.Close()

	r := gin.New()

	// Add middleware
	r.Use(errors.ErrorMiddleware())
	r.Use(middleware.RequestLoggingMiddleware())
	r.Use(middleware.SecurityHeadersMiddleware())
	r.Use(middleware.CORSMiddleware(cfg))

	slog.Info("Security middleware initialized")

	//	@securityDefinitions.apikey	ApiKeyAuth
	//	@in							cookie
	//	@name						token
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// Create smart database-backed rate limiters (only count meaningful attempts)
	loginLimiter := middleware.NewSmartDBRateLimiter(pgs, 5, 15*time.Minute, "login")       // 5 attempts per 15 minutes
	registerLimiter := middleware.NewSmartDBRateLimiter(pgs, 5, 10*time.Minute, "register") // 5 attempts per 10 minutes (increased from 3)
	passwordLimiter := middleware.NewSmartDBRateLimiter(pgs, 3, 5*time.Minute, "password")  // 3 attempts per 5 minutes

	// Start background cleanup for rate limit entries
	middleware.StartRateLimitCleanup(pgs)

	slog.Info("Database rate limiters initialized")

	// --- UI Routes (for testing/documentation) ---
	r.GET("/", homePage)
	r.GET("/dashboard", dashboardPage)

	// --- API Routes ---
	r.POST("/register",
		middleware.ConditionalRateLimitMiddleware(registerLimiter, middleware.GetClientKey, middleware.ShouldCountRegistrationAttempt),
		user.RegisterHandler(pgs, cfg))
	r.POST("/login",
		middleware.ConditionalRateLimitMiddleware(loginLimiter, middleware.GetClientKey, middleware.ShouldCountLoginAttempt),
		user.LoginHandler(pgs))
	r.GET("/verify-email", user.VerifyEmailHandler(pgs))
	r.GET("/dashboard-auth", auth.AuthMiddleware(), dashboardHandler)
	r.POST("/forgot-password",
		middleware.ConditionalRateLimitMiddleware(passwordLimiter, middleware.GetEmailKey, middleware.ShouldCountPasswordResetAttempt),
		user.ForgotPasswordHandler(pgs, cfg))
	r.POST("/reset-password", user.ResetPasswordHandler(pgs))
	r.POST("/resend-verification",
		middleware.ConditionalRateLimitMiddleware(passwordLimiter, middleware.GetEmailKey, middleware.ShouldCountPasswordResetAttempt),
		user.ResendVerificationHandler(pgs, cfg))
	r.POST("/contact", contactHandler)
	r.POST("/logout", auth.AuthMiddleware(), logoutHandler)
	r.POST("/update-profile", auth.AuthMiddleware(), updateProfileHandler)
	r.POST("/update-settings", auth.AuthMiddleware(), updateSettingsHandler)
	r.GET("/validate-reset-token", validateResetTokenHandler)
	r.GET("/forgot-password", forgotPasswordPage)
	r.GET("/reset-password", resetPasswordPage)

	// Start background cleanup for expired pending registrations
	user.StartPendingRegistrationCleanup(pgs)
	slog.Info("Started pending registration cleanup service")

	// Start background cleanup for expired password reset tokens
	user.StartPasswordResetTokenCleanup(pgs)
	slog.Info("Started password reset token cleanup service")

	// Create HTTP server
	srv := &http.Server{
		Addr:    ":" + cfg.Server.Port,
		Handler: r,
	}

	// Start server in a goroutine
	go func() {
		slog.Info("Server starting", "port", cfg.Server.Port, "base_url", cfg.Server.BaseURL)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			slog.Error("Failed to start server", "error", err)
			panic(err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	slog.Info("Server shutting down...")

	// Give outstanding requests 30 seconds to complete
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		slog.Error("Server forced to shutdown", "error", err)
	} else {
		slog.Info("Server shutdown complete")
	}
}

// ====================
//  UI HANDLERS
// ====================

func homePage(c *gin.Context) {
	c.Header("Content-Type", "text/html")
	c.String(http.StatusOK, `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Login & Register</title>
</head>
<body>
  <h1>Welcome</h1>

  <h2>Register</h2>
  <form id="registerForm">
    <input name="username" placeholder="Username" required><br>
    <input name="email" placeholder="Email" required><br>
    <input name="password" type="password" placeholder="Password" required><br>
    <button type="submit">Register</button>
</form>
  <pre id="registerResult"></pre>
  <pre id="registerWait" style="color: #888;"></pre>
  <button id="resendBtn" style="display:none; margin-top:10px;">Resend Verification Email</button>
  <pre id="resendWait" style="color: #888;"></pre>
  <pre id="resendResult"></pre>

  <h2>Login</h2>
  <form id="loginForm">
    <input name="username" placeholder="Username" required><br>
    <input name="password" type="password" placeholder="Password" required><br>
    <button type="submit">Login</button>
  </form>
  <pre id="loginResult"></pre>

  <script>
    // REGISTER
    document.getElementById("registerForm").addEventListener("submit", async e => {
      e.preventDefault();
      const form = e.target;
      document.getElementById("registerWait").innerText = "Registering your account and sending verification email. Please wait...";
      document.getElementById("registerResult").innerText = "";
      document.getElementById("resendBtn").style.display = "none";
      document.getElementById("resendResult").innerText = "";
      const body = {
        username: form.username.value,
        email: form.email.value,
        password: form.password.value
      };
      const res = await fetch("/register", {
        method: "POST",
        headers: {"Content-Type":"application/json"},
        body: JSON.stringify(body)
      });
      document.getElementById("registerWait").innerText = "";
      const data = await res.json();
      document.getElementById("registerResult").innerText = res.ok
        ? "✅ " + (data.message || "Registered!")
        : "❌ " + (data.error || "Registration failed");
      if (res.ok) {
        document.getElementById("resendBtn").style.display = "inline-block";
        // Store registration info for resend
        window._lastReg = body;
      }
    });

    // RESEND VERIFICATION EMAIL
    document.getElementById("resendBtn").addEventListener("click", async e => {
      e.preventDefault();
      const btn = e.target;
      btn.disabled = true;
      document.getElementById("resendWait").innerText = "Resending verification email. Please wait...";
      document.getElementById("resendResult").innerText = "";
      const reg = window._lastReg;
      if (!reg) {
        document.getElementById("resendWait").innerText = "No registration info found.";
        btn.disabled = false;
        return;
      }
      const res = await fetch("/resend-verification", {
        method: "POST",
        headers: {"Content-Type":"application/json"},
        body: JSON.stringify(reg)
      });
      document.getElementById("resendWait").innerText = "";
      const data = await res.json();
      document.getElementById("resendResult").innerText = res.ok
        ? "✅ " + (data.message || "Verification email resent!")
        : "❌ " + (data.error || "Failed to resend email");
      btn.disabled = false;
    });

    // LOGIN
    document.getElementById("loginForm").addEventListener("submit", async e => {
      e.preventDefault();
      const form = e.target;
      const body = {
        username: form.username.value,
        password: form.password.value
      };
      const res = await fetch("/login", {
        method: "POST",
        headers: {"Content-Type":"application/json"},
        credentials: "same-origin",       
        body: JSON.stringify({
          username: e.target.username.value,
          password: e.target.password.value
        })
      });
      const data = await res.json();
      if (res.ok ) {
        window.location = "/dashboard";
      } else {
        document.getElementById("loginResult").innerText = "❌ " + (data.error || "Login failed");
      }
    });
  </script>
</body>
</html>
`)
}

func dashboardPage(c *gin.Context) {
	c.Header("Content-Type", "text/html")
	c.String(http.StatusOK, `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Dashboard</title>
</head>
<body>
  <h1>Dashboard</h1>
  <pre id="message">Loading...</pre>
  <script>
    (async () => {
      const res = await fetch("/dashboard-auth", {
        credentials: "same-origin"
      });
      const data = await res.json();
      document.getElementById("message").innerText = res.ok
        ? "🎉 " + (data.message || "Welcome!")
        : "❌ " + (data.error || "Failed to load dashboard");
    })();
  </script>
</body>
</html>
`)
}

func dashboardHandler(c *gin.Context) {
	usernameVal, userExists := c.Get("user")
	if !userExists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized: user not found in context"})
		return
	}
	username, ok := usernameVal.(string)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "User context value is not a string"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Welcome to your dashboard, " + username + "!"})
}

// ====================
//  MISSING API HANDLERS
// ====================

func contactHandler(c *gin.Context) {
	var req struct {
		Name    string `json:"name" binding:"required"`
		Email   string `json:"email" binding:"required,email"`
		Subject string `json:"subject" binding:"required"`
		Message string `json:"message" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// TODO: Implement actual contact form processing (email sending, database storage, etc.)
	// For now, just return success
	slog.Info("Contact form submitted",
		"name", req.Name,
		"email", req.Email,
		"subject", req.Subject,
		"message_length", len(req.Message))

	c.JSON(http.StatusOK, gin.H{
		"message": "Thank you for your message. We'll get back to you soon!",
	})
}

func logoutHandler(c *gin.Context) {
	// Clear the JWT cookie
	c.SetCookie("token", "", -1, "/", "", false, true)

	c.JSON(http.StatusOK, gin.H{
		"message": "Logged out successfully",
	})
}

func updateProfileHandler(c *gin.Context) {
	var req struct {
		FirstName string `json:"firstName"`
		LastName  string `json:"lastName"`
		Email     string `json:"email"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// TODO: Implement actual profile update logic
	// For now, just return success
	userInterface := c.MustGet("user")
	slog.Info("Profile update requested",
		"user", userInterface,
		"firstName", req.FirstName,
		"lastName", req.LastName,
		"email", req.Email)

	c.JSON(http.StatusOK, gin.H{
		"message": "Profile updated successfully",
	})
}

func updateSettingsHandler(c *gin.Context) {
	var req map[string]interface{}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// TODO: Implement actual settings update logic
	// For now, just return success
	userInterface := c.MustGet("user")
	slog.Info("Settings update requested",
		"user", userInterface,
		"settings", req)

	c.JSON(http.StatusOK, gin.H{
		"message": "Settings updated successfully",
	})
}

func validateResetTokenHandler(c *gin.Context) {
	token := c.Query("token")
	if token == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Token parameter is required",
		})
		return
	}

	// TODO: Implement actual token validation logic
	// For now, just check if token is not "invalid"
	if token == "invalid" || len(token) < 10 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid or expired reset token",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Token is valid",
		"valid":   true,
	})
}

func forgotPasswordPage(c *gin.Context) {
	c.Header("Content-Type", "text/html")
	c.String(http.StatusOK, `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Forgot Password</title>
</head>
<body>
  <h1>Forgot Password</h1>
  <form id="forgotForm">
    <input name="email" placeholder="Email" required><br>
    <button type="submit">Send Reset Email</button>
  </form>
  <pre id="forgotResult"></pre>
  <script>
    document.getElementById("forgotForm").addEventListener("submit", async e => {
      e.preventDefault();
      const form = e.target;
      const body = { email: form.email.value };
      const res = await fetch("/forgot-password", {
        method: "POST",
        headers: {"Content-Type":"application/json"},
        body: JSON.stringify(body)
      });
      const data = await res.json();
      document.getElementById("forgotResult").innerText = res.ok
        ? "✅ " + (data.message || "Email sent!")
        : "❌ " + (data.error || "Failed");
    });
  </script>
</body>
</html>
`)
}

func resetPasswordPage(c *gin.Context) {
	token := c.Query("token")
	c.Header("Content-Type", "text/html")
	c.String(http.StatusOK, `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Reset Password</title>
</head>
<body>
  <h1>Reset Password</h1>
  <form id="resetForm">
    <input type="password" name="password" placeholder="New Password" required><br>
    <button type="submit">Reset Password</button>
  </form>
  <pre id="resetResult"></pre>
  <script>
    const token = "`+token+`";
    document.getElementById("resetForm").addEventListener("submit", async e => {
      e.preventDefault();
      const form = e.target;
      const body = { password: form.password.value };
      const res = await fetch("/reset-password?token=" + encodeURIComponent(token), {
        method: "POST",
        headers: {"Content-Type":"application/json"},
        body: JSON.stringify(body)
      });
      const data = await res.json();
      document.getElementById("resetResult").innerText = res.ok
        ? "✅ " + (data.message || "Password reset!")
        : "❌ " + (data.error || "Failed");
    });
  </script>
</body>
</html>
`)
}
