load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "server",
    srcs = glob(["*.go"], exclude = ["*_test.go"]),
    importpath = "login/cmd/server",
    visibility = ["//visibility:public"],
    deps = [
        "//api/docs",
        "//internal/auth",
        "//internal/config",
        "//internal/db",
        "//internal/errors",
        "//internal/middleware",
        "//internal/user",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_lib_pq//:pq",
        "@com_github_swaggo_files//:files",
        "@com_github_swaggo_gin_swagger//:gin-swagger",
        "@io_gorm_gorm//:gorm",
    ],
)

go_test(
    name = "server_test",
    srcs = glob(["*_test.go"]),
    embed = [":server"],
    deps = [
        "//internal/config",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)
