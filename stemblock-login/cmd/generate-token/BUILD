load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "generate_token_lib",
    srcs = glob(["*.go"], exclude = ["*_test.go"]),
    importpath = "login/cmd/generate-token",
    visibility = ["//visibility:public"],
    deps = [
        "//internal/auth",
        "//internal/config",
        "@com_github_golang_jwt_jwt_v5//:jwt",
    ],
)

go_test(
    name = "generate_token_test",
    srcs = glob(["*_test.go"]),
    embed = [":generate_token_lib"],
    deps = [
        "//internal/auth",
        "//internal/config",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)
