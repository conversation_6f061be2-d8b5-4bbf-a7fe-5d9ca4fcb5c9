load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "admin_lib",
    srcs = glob(["*.go"], exclude = ["*_test.go"]),
    importpath = "login/cmd/admin",
    visibility = ["//visibility:public"],
    deps = [
        "//internal/auth",
        "//internal/config",
        "//internal/db",
        "//internal/user",
        "@io_gorm_gorm//:gorm",
    ],
)

go_test(
    name = "admin_test",
    srcs = glob(["*_test.go"]),
    embed = [":admin_lib"],
    deps = [
        "//internal/config",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)
