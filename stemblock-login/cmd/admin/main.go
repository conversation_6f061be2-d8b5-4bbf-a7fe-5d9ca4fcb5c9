package main

import (
	"fmt"
	"log"
	"login/internal/auth"
	"login/internal/config"
	"login/internal/db"
	"login/internal/user"
	"os"
	"strconv"
	"time"

	"gorm.io/gorm"
)

func main() {
	if len(os.Args) < 2 {
		printUsage()
		os.Exit(1)
	}

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize database
	database := db.InitDB(cfg.Database.DSN())

	command := os.Args[1]
	args := os.Args[2:]

	switch command {
	case "clear-rate-limits":
		clearRateLimits(database, args)
	case "clear-rate-limit":
		clearRateLimit(database, args)
	case "list-rate-limits":
		listRateLimits(database, args)
	case "delete-user":
		deleteUser(database, args)
	case "delete-user-by-email":
		deleteUserByEmail(database, args)
	case "show-user":
		showUserDetails(database, args)
	case "list-users":
		listUsers(database, args)
	case "create-user":
		createUser(database, args)
	case "reset-password":
		resetUserPassword(database, args)
	case "verify-user":
		verifyUser(database, args)
	case "clear-pending":
		clearPendingRegistrations(database, args)
	case "list-pending":
		listPendingRegistrations(database, args)
	case "db-stats":
		showDatabaseStats(database)
	case "cleanup":
		cleanupDatabase(database, args)
	default:
		fmt.Printf("Unknown command: %s\n", command)
		printUsage()
		os.Exit(1)
	}
}

func printUsage() {
	fmt.Println("🛠️  STEMBlock Admin CLI Tool")
	fmt.Println("Usage: go run cmd/admin/main.go <command> [args...]")
	fmt.Println()
	fmt.Println("🚦 Rate Limiting Commands:")
	fmt.Println("  clear-rate-limits                    - Clear all rate limit records")
	fmt.Println("  clear-rate-limit <key> [endpoint]    - Clear rate limits for specific IP/email")
	fmt.Println("  list-rate-limits [key]               - List rate limit records")
	fmt.Println()
	fmt.Println("👥 User Management Commands:")
	fmt.Println("  delete-user <username>               - Delete a user by username")
	fmt.Println("  delete-user-by-email <email>         - Delete a user by email")
	fmt.Println("  show-user <username>                 - Show detailed user information")
	fmt.Println("  list-users [limit]                   - List users (default: 10)")
	fmt.Println("  create-user <username> <email> <password> [role] - Create a user")
	fmt.Println("  reset-password <username> <new_password> - Reset user password")
	fmt.Println("  verify-user <username>               - Mark user as verified")
	fmt.Println()
	fmt.Println("⏳ Pending Registration Commands:")
	fmt.Println("  clear-pending                        - Clear all pending registrations")
	fmt.Println("  list-pending [limit]                 - List pending registrations")
	fmt.Println()
	fmt.Println("📊 Database Commands:")
	fmt.Println("  db-stats                             - Show database statistics")
	fmt.Println("  cleanup [hours]                      - Clean up old records (default: 24 hours)")
	fmt.Println()
	fmt.Println("💡 Examples:")
	fmt.Println("  go run cmd/admin/main.go clear-rate-limit 192.168.1.100")
	fmt.Println("  go run cmd/admin/main.go delete-user testuser")
	fmt.Println("  go run cmd/admin/main.go delete-user-by-email <EMAIL>")
	fmt.Println("  go run cmd/admin/main.go show-user testuser")
	fmt.Println("  go run cmd/admin/main.go create-<NAME_EMAIL> password123 admin")
}

// Rate limiting functions
func clearRateLimits(db *gorm.DB, args []string) {
	result := db.Delete(&user.RateLimitEntry{}, "1 = 1")
	if result.Error != nil {
		log.Fatalf("Failed to clear rate limits: %v", result.Error)
	}
	fmt.Printf("✅ Cleared %d rate limit records\n", result.RowsAffected)
}

func clearRateLimit(db *gorm.DB, args []string) {
	if len(args) < 1 {
		fmt.Println("❌ Usage: clear-rate-limit <key> [endpoint]")
		return
	}

	key := args[0]
	query := db.Where("key = ?", key)

	if len(args) > 1 {
		endpoint := args[1]
		query = query.Where("endpoint = ?", endpoint)
	}

	result := query.Delete(&user.RateLimitEntry{})
	if result.Error != nil {
		log.Fatalf("Failed to clear rate limit: %v", result.Error)
	}
	fmt.Printf("✅ Cleared %d rate limit records for key: %s\n", result.RowsAffected, key)
}

func listRateLimits(db *gorm.DB, args []string) {
	var entries []user.RateLimitEntry
	query := db.Order("timestamp DESC").Limit(50)

	if len(args) > 0 {
		key := args[0]
		query = query.Where("key = ?", key)
	}

	if err := query.Find(&entries).Error; err != nil {
		log.Fatalf("Failed to list rate limits: %v", err)
	}

	if len(entries) == 0 {
		fmt.Println("📭 No rate limit records found")
		return
	}

	fmt.Printf("📊 Rate Limit Records (showing %d):\n", len(entries))
	fmt.Println("Key                    | Endpoint  | Timestamp           | Age")
	fmt.Println("----------------------|-----------|---------------------|----------")

	for _, entry := range entries {
		age := time.Since(entry.Timestamp).Round(time.Second)
		fmt.Printf("%-20s | %-9s | %s | %s\n",
			truncateString(entry.Key, 20),
			entry.Endpoint,
			entry.Timestamp.Format("2006-01-02 15:04:05"),
			age)
	}
}

// User management functions
func deleteUser(db *gorm.DB, args []string) {
	if len(args) < 1 {
		fmt.Println("❌ Usage: delete-user <username>")
		return
	}

	username := args[0]

	// First, check if user exists and get their info
	var existingUser user.User
	if err := db.Where("username = ?", username).First(&existingUser).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			fmt.Printf("❌ User '%s' not found in users table\n", username)
			return
		}
		log.Fatalf("Failed to check user existence: %v", err)
	}

	fmt.Printf("🔍 Found user: %s (ID: %s, Email: %s, Role: %s)\n",
		existingUser.Username, existingUser.ID, existingUser.Email, existingUser.Role)

	// Delete user and related records in transaction
	tx := db.Begin()
	if tx.Error != nil {
		log.Fatalf("Failed to begin transaction: %v", tx.Error)
	}

	// Count and delete related records first
	var pendingCount, passwordResetCount, emailVerificationCount, rateLimitCount int64

	// Count related records using correct column names
	tx.Model(&user.PendingRegistration{}).Where("username = ?", username).Count(&pendingCount)
	tx.Model(&user.PasswordResetToken{}).Where("user_id = ?", existingUser.ID).Count(&passwordResetCount)
	tx.Model(&user.EmailVerificationToken{}).Where("user_id = ?", existingUser.ID).Count(&emailVerificationCount)
	tx.Model(&user.RateLimitEntry{}).Where("key = ? OR key = ?", username, existingUser.Email).Count(&rateLimitCount)

	// Delete related records using correct column names
	if pendingCount > 0 {
		if err := tx.Where("username = ?", username).Delete(&user.PendingRegistration{}).Error; err != nil {
			tx.Rollback()
			log.Fatalf("Failed to delete pending registrations: %v", err)
		}
		fmt.Printf("  🗑️  Deleted %d pending registration(s)\n", pendingCount)
	}

	if passwordResetCount > 0 {
		if err := tx.Where("user_id = ?", existingUser.ID).Delete(&user.PasswordResetToken{}).Error; err != nil {
			tx.Rollback()
			log.Fatalf("Failed to delete password reset tokens: %v", err)
		}
		fmt.Printf("  🗑️  Deleted %d password reset token(s)\n", passwordResetCount)
	}

	if emailVerificationCount > 0 {
		if err := tx.Where("user_id = ?", existingUser.ID).Delete(&user.EmailVerificationToken{}).Error; err != nil {
			tx.Rollback()
			log.Fatalf("Failed to delete email verification tokens: %v", err)
		}
		fmt.Printf("  🗑️  Deleted %d email verification token(s)\n", emailVerificationCount)
	}

	if rateLimitCount > 0 {
		if err := tx.Where("key = ? OR key = ?", username, existingUser.Email).Delete(&user.RateLimitEntry{}).Error; err != nil {
			tx.Rollback()
			log.Fatalf("Failed to delete rate limit entries: %v", err)
		}
		fmt.Printf("  🗑️  Deleted %d rate limit entry(ies)\n", rateLimitCount)
	}

	// Finally, delete the main user record
	result := tx.Where("username = ?", username).Delete(&user.User{})
	if result.Error != nil {
		tx.Rollback()
		log.Fatalf("Failed to delete user from users table: %v", result.Error)
	}

	if result.RowsAffected == 0 {
		tx.Rollback()
		fmt.Printf("❌ Failed to delete user '%s' from users table\n", username)
		return
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		log.Fatalf("Failed to commit transaction: %v", err)
	}

	fmt.Printf("✅ Successfully deleted user '%s' and all related records from the database\n", username)
	fmt.Printf("   📊 Summary: User + %d pending + %d password resets + %d email tokens + %d rate limits\n",
		pendingCount, passwordResetCount, emailVerificationCount, rateLimitCount)
}

func deleteUserByEmail(db *gorm.DB, args []string) {
	if len(args) < 1 {
		fmt.Println("❌ Usage: delete-user-by-email <email>")
		return
	}

	email := args[0]

	// First, check if user exists and get their info
	var existingUser user.User
	if err := db.Where("email = ?", email).First(&existingUser).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			fmt.Printf("❌ User with email '%s' not found in users table\n", email)
			return
		}
		log.Fatalf("Failed to check user existence: %v", err)
	}

	fmt.Printf("🔍 Found user: %s (ID: %s, Email: %s, Role: %s)\n",
		existingUser.Username, existingUser.ID, existingUser.Email, existingUser.Role)

	// Use the existing deleteUser logic but with the found username
	fmt.Printf("🔄 Proceeding to delete user '%s'...\n", existingUser.Username)
	deleteUser(db, []string{existingUser.Username})
}

func showUserDetails(db *gorm.DB, args []string) {
	if len(args) < 1 {
		fmt.Println("❌ Usage: show-user <username>")
		return
	}

	username := args[0]

	// Get user info
	var existingUser user.User
	if err := db.Where("username = ?", username).First(&existingUser).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			fmt.Printf("❌ User '%s' not found in users table\n", username)
			return
		}
		log.Fatalf("Failed to get user details: %v", err)
	}

	// Count related records using correct column names
	var pendingCount, passwordResetCount, emailVerificationCount, rateLimitCount int64
	db.Model(&user.PendingRegistration{}).Where("username = ?", username).Count(&pendingCount)
	db.Model(&user.PasswordResetToken{}).Where("user_id = ?", existingUser.ID).Count(&passwordResetCount)
	db.Model(&user.EmailVerificationToken{}).Where("user_id = ?", existingUser.ID).Count(&emailVerificationCount)
	db.Model(&user.RateLimitEntry{}).Where("key = ? OR key = ?", username, existingUser.Email).Count(&rateLimitCount)

	// Check if user has pending email verification
	var hasEmailVerification bool = emailVerificationCount > 0

	// Display user details
	fmt.Println("👤 User Details:")
	fmt.Printf("   ID: %s\n", existingUser.ID)
	fmt.Printf("   Username: %s\n", existingUser.Username)
	fmt.Printf("   Email: %s\n", existingUser.Email)
	fmt.Printf("   Role: %s\n", existingUser.Role)
	fmt.Printf("   First Name: %s\n", existingUser.FirstName)
	fmt.Printf("   Last Name: %s\n", existingUser.LastName)
	fmt.Printf("   Email Verified: %t\n", !hasEmailVerification)

	fmt.Println("\n📊 Related Records:")
	fmt.Printf("   Pending Registrations: %d\n", pendingCount)
	fmt.Printf("   Password Reset Tokens: %d\n", passwordResetCount)
	fmt.Printf("   Email Verification Tokens: %d\n", emailVerificationCount)
	fmt.Printf("   Rate Limit Entries: %d\n", rateLimitCount)

	if pendingCount > 0 || passwordResetCount > 0 || emailVerificationCount > 0 || rateLimitCount > 0 {
		fmt.Println("\n⚠️  This user has related records that will be deleted if you delete the user.")
	}
}

func listUsers(db *gorm.DB, args []string) {
	limit := 10
	if len(args) > 0 {
		if l, err := strconv.Atoi(args[0]); err == nil {
			limit = l
		}
	}

	var users []user.User
	if err := db.Order("username ASC").Limit(limit).Find(&users).Error; err != nil {
		log.Fatalf("Failed to list users: %v", err)
	}

	if len(users) == 0 {
		fmt.Println("📭 No users found")
		return
	}

	fmt.Printf("👥 Users (showing %d):\n", len(users))
	fmt.Println("ID                                   | Username           | Email                     | Role")
	fmt.Println("-------------------------------------|--------------------|--------------------------|---------")

	for _, u := range users {
		fmt.Printf("%-36s | %-18s | %-25s | %-7s\n",
			u.ID,
			truncateString(u.Username, 18),
			truncateString(u.Email, 25),
			u.Role)
	}
}

func createUser(db *gorm.DB, args []string) {
	if len(args) < 3 {
		fmt.Println("❌ Usage: create-user <username> <email> <password> [role]")
		return
	}

	username := args[0]
	email := args[1]
	password := args[2]
	role := "student"
	if len(args) > 3 {
		role = args[3]
	}

	// Hash password
	hashedPassword, err := auth.HashPassword(password)
	if err != nil {
		log.Fatalf("Failed to hash password: %v", err)
	}

	// Create user
	newUser := user.User{
		Username:     username,
		Email:        email,
		PasswordHash: hashedPassword,
		Role:         role,
	}

	if err := db.Create(&newUser).Error; err != nil {
		log.Fatalf("Failed to create user: %v", err)
	}

	fmt.Printf("✅ Created user '%s' with email '%s' and role '%s'\n", username, email, role)
}

func resetUserPassword(db *gorm.DB, args []string) {
	if len(args) < 2 {
		fmt.Println("❌ Usage: reset-password <username> <new_password>")
		return
	}

	username := args[0]
	newPassword := args[1]

	// Hash new password
	hashedPassword, err := auth.HashPassword(newPassword)
	if err != nil {
		log.Fatalf("Failed to hash password: %v", err)
	}

	// Update user password
	result := db.Model(&user.User{}).Where("username = ?", username).Update("password_hash", hashedPassword)
	if result.Error != nil {
		log.Fatalf("Failed to reset password: %v", result.Error)
	}

	if result.RowsAffected == 0 {
		fmt.Printf("❌ User '%s' not found\n", username)
		return
	}

	fmt.Printf("✅ Reset password for user '%s'\n", username)
}

func verifyUser(db *gorm.DB, args []string) {
	if len(args) < 1 {
		fmt.Println("❌ Usage: verify-user <username>")
		return
	}

	username := args[0]

	result := db.Model(&user.User{}).Where("username = ?", username).Update("is_verified", true)
	if result.Error != nil {
		log.Fatalf("Failed to verify user: %v", result.Error)
	}

	if result.RowsAffected == 0 {
		fmt.Printf("❌ User '%s' not found\n", username)
		return
	}

	fmt.Printf("✅ Verified user '%s'\n", username)
}

// Pending registration functions
func clearPendingRegistrations(db *gorm.DB, args []string) {
	result := db.Delete(&user.PendingRegistration{}, "1 = 1")
	if result.Error != nil {
		log.Fatalf("Failed to clear pending registrations: %v", result.Error)
	}
	fmt.Printf("✅ Cleared %d pending registrations\n", result.RowsAffected)
}

func listPendingRegistrations(db *gorm.DB, args []string) {
	limit := 10
	if len(args) > 0 {
		if l, err := strconv.Atoi(args[0]); err == nil {
			limit = l
		}
	}

	var pending []user.PendingRegistration
	if err := db.Order("created_at DESC").Limit(limit).Find(&pending).Error; err != nil {
		log.Fatalf("Failed to list pending registrations: %v", err)
	}

	if len(pending) == 0 {
		fmt.Println("📭 No pending registrations found")
		return
	}

	fmt.Printf("⏳ Pending Registrations (showing %d):\n", len(pending))
	fmt.Println("Username           | Email                     | Created    | Expires")
	fmt.Println("-------------------|---------------------------|------------|----------")

	for _, p := range pending {
		fmt.Printf("%-18s | %-25s | %s | %s\n",
			truncateString(p.Username, 18),
			truncateString(p.Email, 25),
			p.CreatedAt.Format("2006-01-02"),
			p.ExpiresAt.Format("2006-01-02"))
	}
}

// Database functions
func showDatabaseStats(db *gorm.DB) {
	fmt.Println("📊 Database Statistics:")
	fmt.Println("======================")

	tables := []struct {
		name  string
		model interface{}
	}{
		{"Users", &user.User{}},
		{"Pending Registrations", &user.PendingRegistration{}},
		{"Rate Limit Entries", &user.RateLimitEntry{}},
		{"Password Reset Tokens", &user.PasswordResetToken{}},
		{"Email Verification Tokens", &user.EmailVerificationToken{}},
		{"Resend Limits", &user.ResendLimit{}},
	}

	for _, table := range tables {
		var count int64
		db.Model(table.model).Count(&count)
		fmt.Printf("%-25s: %d records\n", table.name, count)
	}

	// Show recent rate limit activity
	var recentRateLimit int64
	db.Model(&user.RateLimitEntry{}).Where("timestamp > ?", time.Now().Add(-1*time.Hour)).Count(&recentRateLimit)
	fmt.Printf("%-25s: %d requests\n", "Rate Limits (last hour)", recentRateLimit)
}

func cleanupDatabase(db *gorm.DB, args []string) {
	hours := 24
	if len(args) > 0 {
		if h, err := strconv.Atoi(args[0]); err == nil {
			hours = h
		}
	}

	cutoff := time.Now().Add(-time.Duration(hours) * time.Hour)
	fmt.Printf("🧹 Cleaning up records older than %d hours (%s)...\n", hours, cutoff.Format("2006-01-02 15:04:05"))

	// Clean up rate limit entries
	result1 := db.Where("timestamp < ?", cutoff).Delete(&user.RateLimitEntry{})
	fmt.Printf("✅ Deleted %d old rate limit entries\n", result1.RowsAffected)

	// Clean up expired pending registrations
	result2 := db.Where("expires_at < ?", time.Now()).Delete(&user.PendingRegistration{})
	fmt.Printf("✅ Deleted %d expired pending registrations\n", result2.RowsAffected)

	// Clean up old password reset tokens
	result3 := db.Where("expires_at < ?", time.Now()).Delete(&user.PasswordResetToken{})
	fmt.Printf("✅ Deleted %d expired password reset tokens\n", result3.RowsAffected)

	// Clean up old email verification tokens
	result4 := db.Where("expires_at < ?", time.Now()).Delete(&user.EmailVerificationToken{})
	fmt.Printf("✅ Deleted %d expired email verification tokens\n", result4.RowsAffected)

	fmt.Println("🎉 Database cleanup completed!")
}

// Utility functions
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen-3] + "..."
}
