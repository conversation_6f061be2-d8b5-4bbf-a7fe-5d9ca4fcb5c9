#!/bin/bash

# STEMBlock Admin Tool Wrapper
# Usage: ./admin.sh <command> [args...]

# Show help if no arguments provided
if [ $# -eq 0 ]; then
    echo "🛠️  STEMBlock Admin Tool"
    echo ""
    echo "📋 Quick Commands:"
    echo "  ./admin.sh list-users                    - Show all users"
    echo "  ./admin.sh show-user <username>          - Show detailed user info"
    echo "  ./admin.sh delete-user <username>        - Delete user by username"
    echo "  ./admin.sh delete-user-by-email <email>  - Delete user by email"
    echo "  ./admin.sh db-stats                      - Show database statistics"
    echo "  ./admin.sh clear-rate-limits             - Clear all rate limits"
    echo ""
    echo "💡 For full command list, run any command to see complete usage"
    echo ""
    exit 0
fi

# Load environment variables if .env exists AND development mode is enabled
if [ -f .env ] && [ "$DEVELOPMENT" = "true" ]; then
    export $(cat .env | grep -v '^#' | xargs)
    echo "ℹ️  Loaded .env file for development"
elif [ -f .env ] && [ "$DEVELOPMENT" != "true" ]; then
    echo "⚠️  .env file exists but DEVELOPMENT is not set to 'true'"
    echo "   For security, .env file will not be loaded"
    echo "   Set DEVELOPMENT=true for local development"
fi

# Check if required environment variables are set
if [ -z "$DB_USER" ] || [ -z "$DB_PASSWORD" ]; then
    echo "❌ Database credentials not configured."
    echo "Please create and configure your .env file:"
    echo "   cp .env.example .env"
    echo "   # Edit .env with your database credentials"
    exit 1
fi

# Use compiled binary if available (production), otherwise go run (development)
if [ -f "./bin/admin" ]; then
    # Digital Ocean production: use compiled binary
    ./bin/admin "$@"
elif [ -f "./admin-tool" ]; then
    # Local production build: use compiled binary
    ./admin-tool "$@"
elif command -v go >/dev/null 2>&1; then
    # Development: use go run
    go run cmd/admin/main.go "$@"
else
    # Production without Go: use production admin script
    echo "🔧 Go runtime not available - using production admin helper"
    ./admin-production.sh "$@"
fi
