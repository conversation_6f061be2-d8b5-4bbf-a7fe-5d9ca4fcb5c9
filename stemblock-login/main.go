/*
Status	Meaning	          		Use Case Example
200	    OK	              		Fetching user data after login
201	    Created	          		Creating a new user or course
302	    Found (Redirect)  		Redirect after login
400	    Bad Request	      		Invalid form input
401	    Unauthorized	  		User not logged in
403	    Forbidden		  		User logged in but not allowed to access
404	    Not Found		  		Resource doesn't exist
500	    Internal Server Error	Code crashed or DB failed

Database format:

CREATE TABLE users (

	id SERIAL PRIMARY KEY,
	username TEXT UNIQUE NOT NULL,
	password_hash TEXT NOT NULL

);
*/
package main

import (
	"bufio"
	"log/slog"
	"login/cmd/server"
	"login/internal/config"
	"os"
	"strings"
)

func main() {
	// SECURITY: Load .env file ONLY if explicitly enabled for development
	// This prevents accidental .env usage in production environments
	// Production should NEVER set DEVELOPMENT=true
	if os.Getenv("DEVELOPMENT") == "true" {
		if _, err := os.Stat(".env"); err == nil {
			loadEnvFile(".env")
			slog.Info("Loaded .env file for development")
		}
	}

	// Initialize structured logging
	logger := slog.New(slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}))
	slog.SetDefault(logger)

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		slog.Error("Failed to load configuration", "error", err)
		os.Exit(1)
	}

	slog.Info("Starting STEMBlock login server", "port", cfg.Server.Port)

	// Start server with configuration
	server.StartServer(cfg)
}

// loadEnvFile loads environment variables from a .env file
// Only used when DEVELOPMENT=true is set
func loadEnvFile(filename string) {
	file, err := os.Open(filename)
	if err != nil {
		return // Silently fail if .env doesn't exist
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		// Skip empty lines and comments
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// Split on first = sign
		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])

			// Remove quotes if present
			if len(value) >= 2 && ((value[0] == '"' && value[len(value)-1] == '"') ||
				(value[0] == '\'' && value[len(value)-1] == '\'')) {
				value = value[1 : len(value)-1]
			}

			// Only set if not already set (system env vars take precedence)
			if os.Getenv(key) == "" {
				os.Setenv(key, value)
			}
		}
	}
}
