# Bazel configuration for STEMBlock login application

# Force WORKSPACE mode (disable <PERSON><PERSON><PERSON><PERSON>)
common --noenable_bzlmod

# Build settings
build --incompatible_enable_cc_toolchain_resolution
build --cxxopt=-std=c++17
build --host_cxxopt=-std=c++17

# Test settings
test --test_output=errors
test --test_summary=detailed

# Performance settings
build --jobs=auto
build --local_resources=memory=HOST_RAM*0.75
build --local_resources=cpu=HOST_CPUS*0.75

# Remote cache (optional - uncomment if you have remote cache)
# build --remote_cache=grpc://your-cache-server:port

# Development settings
build:dev --compilation_mode=dbg
build:dev --strip=never

# Production settings
build:prod --compilation_mode=opt
build:prod --strip=always
build:prod --define=GOOS=linux
build:prod --define=GOARCH=amd64

# Try to import user-specific .bazelrc
try-import %workspace%/.bazelrc.user
