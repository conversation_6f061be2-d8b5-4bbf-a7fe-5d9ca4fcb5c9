load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "config",
    srcs = glob(["*.go"], exclude = ["*_test.go"]),
    importpath = "login/internal/config",
    visibility = ["//visibility:public"],
)

go_test(
    name = "config_test",
    srcs = glob(["*_test.go"]),
    embed = [":config"],
    deps = [
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)
