package config

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestLoad(t *testing.T) {
	t.Run("load fails without required env vars", func(t *testing.T) {
		// Save and clear environment
		originalUser := os.Getenv("DB_USER")
		originalPass := os.Getenv("DB_PASSWORD")
		originalEmail := os.Getenv("SUPPORT_EMAIL_PASSWORD")

		os.Unsetenv("DB_USER")
		os.Unsetenv("DB_PASSWORD")
		os.Unsetenv("SUPPORT_EMAIL_PASSWORD")

		defer func() {
			if originalUser != "" {
				os.Setenv("DB_USER", originalUser)
			}
			if originalPass != "" {
				os.Setenv("DB_PASSWORD", originalPass)
			}
			if originalEmail != "" {
				os.Setenv("SUPPORT_EMAIL_PASSWORD", originalEmail)
			}
		}()

		cfg, err := Load()
		assert.Error(t, err)
		assert.Nil(t, cfg)
	})
}

func TestGetEnvOrDefault(t *testing.T) {
	result := getEnvOrDefault("NON_EXISTENT_VAR", "default")
	assert.Equal(t, "default", result)

	os.Setenv("TEST_VAR", "test_value")
	defer os.Unsetenv("TEST_VAR")

	result = getEnvOrDefault("TEST_VAR", "default")
	assert.Equal(t, "test_value", result)
}

func TestDatabaseConfigDSN(t *testing.T) {
	config := DatabaseConfig{
		Host:     "localhost",
		User:     "postgres",
		Password: "password",
		Name:     "testdb",
		Port:     "5432",
		SSLMode:  "disable",
	}

	expected := "host=localhost user=postgres password=password dbname=testdb port=5432 sslmode=disable search_path=login"
	result := config.DSN()
	assert.Equal(t, expected, result)
}
