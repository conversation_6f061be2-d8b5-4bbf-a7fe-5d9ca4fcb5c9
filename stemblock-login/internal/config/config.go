package config

import (
	"fmt"
	"os"
)

// Config holds all configuration for the application
type Config struct {
	Database DatabaseConfig
	Server   ServerConfig
	Email    EmailConfig
	JWT      JWTConfig
}

// DatabaseConfig holds database configuration
type DatabaseConfig struct {
	Host     string
	User     string
	Password string
	Name     string
	Port     string
	SSLMode  string
}

// ServerConfig holds server configuration
type ServerConfig struct {
	Port        string
	BaseURL     string
	FrontendURL string
}

// EmailConfig holds email configuration
type EmailConfig struct {
	SMTPHost  string
	SMTPPort  int
	FromEmail string
	FromName  string
	Password  string
}

// JWTConfig holds JWT configuration
type JWTConfig struct {
	Secret string
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	config := &Config{
		Database: DatabaseConfig{
			Host:     getEnvOrDefault("DB_HOST", "localhost"),
			User:     os.Getenv("DB_USER"),
			Password: os.Getenv("DB_PASSWORD"),
			Name:     getEnvOrDefault("DB_NAME", "stemblock"),
			Port:     getEnvOrDefault("DB_PORT", "5432"),
			SSLMode:  getEnvOrDefault("DB_SSLMODE", "disable"),
		},
		Server: ServerConfig{
			Port:        getEnvOrDefault("PORT", "8080"),
			BaseURL:     getEnvOrDefault("BASE_URL", "http://localhost:8080"),
			FrontendURL: getEnvOrDefault("FRONTEND_URL", ""),
		},
		Email: EmailConfig{
			SMTPHost:  getEnvOrDefault("SMTP_HOST", "sh-cp7.yyz2.servername.online"),
			SMTPPort:  465,
			FromEmail: getEnvOrDefault("FROM_EMAIL", "<EMAIL>"),
			FromName:  getEnvOrDefault("FROM_NAME", "STEMBlock Support"),
			Password:  os.Getenv("SUPPORT_EMAIL_PASSWORD"),
		},
		JWT: JWTConfig{
			Secret: os.Getenv("JWT_SECRET"),
		},
	}

	// Validate required fields
	if config.Database.User == "" {
		return nil, fmt.Errorf("DB_USER environment variable is required")
	}
	if config.Database.Password == "" {
		return nil, fmt.Errorf("DB_PASSWORD environment variable is required")
	}
	if config.Email.Password == "" {
		return nil, fmt.Errorf("SUPPORT_EMAIL_PASSWORD environment variable is required")
	}

	return config, nil
}

// DSN returns the database connection string
func (d DatabaseConfig) DSN() string {
	return fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=%s search_path=login",
		d.Host, d.User, d.Password, d.Name, d.Port, d.SSLMode)
}

// getEnvOrDefault returns the environment variable value or a default value if not set
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
