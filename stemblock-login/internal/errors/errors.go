package errors

import (
	"fmt"
	"log/slog"
	"net/http"

	"github.com/gin-gonic/gin"
)

// AppError represents an application error
type AppError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
	Err     error  `json:"-"`
}

func (e *AppError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("%s: %v", e.Message, e.Err)
	}
	return e.Message
}

// Common error constructors
func NewBadRequestError(message string, err error) *AppError {
	return &AppError{
		Code:    http.StatusBadRequest,
		Message: message,
		Err:     err,
	}
}

func NewUnauthorizedError(message string) *AppError {
	return &AppError{
		Code:    http.StatusUnauthorized,
		Message: message,
	}
}

func NewForbiddenError(message string) *AppError {
	return &AppError{
		Code:    http.StatusForbidden,
		Message: message,
	}
}

func NewNotFoundError(message string) *AppError {
	return &AppError{
		Code:    http.StatusNotFound,
		Message: message,
	}
}

func NewConflictError(message string, err error) *AppError {
	return &AppError{
		Code:    http.StatusConflict,
		Message: message,
		Err:     err,
	}
}

func NewInternalServerError(message string, err error) *AppError {
	return &AppError{
		Code:    http.StatusInternalServerError,
		Message: message,
		Err:     err,
	}
}

func NewTooManyRequestsError(message string) *AppError {
	return &AppError{
		Code:    http.StatusTooManyRequests,
		Message: message,
	}
}

// HandleError handles application errors consistently
func HandleError(c *gin.Context, err *AppError) {
	// Log the error with context
	slog.Error("Request error",
		"error", err.Error(),
		"code", err.Code,
		"path", c.Request.URL.Path,
		"method", c.Request.Method,
		"ip", c.ClientIP(),
		"user_agent", c.Request.UserAgent())

	// Return appropriate response
	response := gin.H{"error": err.Message}
	if err.Details != "" {
		response["details"] = err.Details
	}

	c.JSON(err.Code, response)
}

// ErrorMiddleware handles panics and converts them to proper error responses
func ErrorMiddleware() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		var err *AppError

		switch v := recovered.(type) {
		case *AppError:
			err = v
		case error:
			err = NewInternalServerError("Internal server error", v)
		case string:
			err = NewInternalServerError("Internal server error", fmt.Errorf("%s", v))
		default:
			err = NewInternalServerError("Internal server error", fmt.Errorf("unknown error: %v", v))
		}

		HandleError(c, err)
	})
}

// WrapDatabaseError converts database errors to appropriate app errors
func WrapDatabaseError(err error, operation string) *AppError {
	if err == nil {
		return nil
	}

	errStr := err.Error()

	// Check for common database errors
	switch {
	case contains(errStr, "duplicate key") || contains(errStr, "unique constraint"):
		return NewConflictError("Resource already exists", err)
	case contains(errStr, "record not found"):
		return NewNotFoundError("Resource not found")
	case contains(errStr, "connection refused") || contains(errStr, "connection failed"):
		return NewInternalServerError("Database connection error", err)
	default:
		return NewInternalServerError("Database error during "+operation, err)
	}
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || (len(s) > len(substr) &&
		(s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
			containsSubstring(s, substr))))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
