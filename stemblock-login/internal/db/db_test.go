package db

import (
	"fmt"
	"login/internal/user"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// Helper function to get environment variable or default value
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func TestInitDB(t *testing.T) {
	t.Run("function exists and can be called", func(t *testing.T) {
		// Since InitDB panics on invalid connections, we just test that the function exists
		// and has the correct signature. We don't actually call it with invalid data
		// because that would cause the test to fail.

		// This is a basic smoke test to ensure the function is properly defined
		assert.NotNil(t, InitDB, "InitDB function should exist")
	})
}

func TestMigrateModels(t *testing.T) {
	// This test requires a real database connection
	// Skip if no test database environment variables are set
	testDBHost := os.Getenv("TEST_DB_HOST")
	testDBUser := os.Getenv("TEST_DB_USER")
	testDBPassword := os.Getenv("TEST_DB_PASSWORD")
	testDBName := os.Getenv("TEST_DB_NAME")

	if testDBHost == "" || testDBUser == "" || testDBPassword == "" || testDBName == "" {
		t.Skip("Skipping database migration test - set TEST_DB_HOST, TEST_DB_USER, TEST_DB_PASSWORD, TEST_DB_NAME environment variables to run")
	}

	// Use environment variables for test database connection
	testDBPort := getEnvOrDefault("TEST_DB_PORT", "5432")
	testDBSSLMode := getEnvOrDefault("TEST_DB_SSLMODE", "disable")

	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=%s",
		testDBHost, testDBUser, testDBPassword, testDBName, testDBPort, testDBSSLMode)

	db := InitDB(dsn)
	defer func() {
		if sqlDB, err := db.DB(); err == nil {
			sqlDB.Close()
		}
	}()

	// Verify tables exist (migration happens in InitDB)
	assert.True(t, db.Migrator().HasTable(&user.User{}))
	assert.True(t, db.Migrator().HasTable(&user.PendingRegistration{}))
	assert.True(t, db.Migrator().HasTable(&user.PasswordResetToken{}))
	assert.True(t, db.Migrator().HasTable(&user.EmailVerificationToken{}))
	assert.True(t, db.Migrator().HasTable(&user.ResendLimit{}))
	assert.True(t, db.Migrator().HasTable(&user.RateLimitEntry{}))
}

func TestDatabaseConnection(t *testing.T) {
	t.Run("connection string format", func(t *testing.T) {
		// Test that we can create a valid connection string format
		// This doesn't actually connect, just validates the format
		testCases := []struct {
			name  string
			dsn   string
			valid bool
		}{
			{
				name:  "valid postgres DSN",
				dsn:   "host=localhost user=postgres password=pass dbname=test port=5432 sslmode=disable",
				valid: true,
			},
			{
				name:  "minimal DSN",
				dsn:   "host=localhost user=postgres dbname=test",
				valid: true,
			},
			{
				name:  "empty DSN",
				dsn:   "",
				valid: false,
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				if tc.valid {
					// For valid DSNs, we expect the postgres driver to accept the format
					// even if the connection fails
					_, err := gorm.Open(postgres.Open(tc.dsn), &gorm.Config{})
					// We expect an error because we're not actually connecting to a real DB
					// but the error should be about connection, not DSN format
					if err != nil {
						// The error should be about connection, not invalid DSN format
						assert.NotContains(t, err.Error(), "invalid DSN")
					}
				} else {
					// For invalid DSNs, we expect immediate failure
					_, err := gorm.Open(postgres.Open(tc.dsn), &gorm.Config{})
					assert.Error(t, err)
				}
			})
		}
	})
}

func TestModelStructures(t *testing.T) {
	t.Run("user model has required fields", func(t *testing.T) {
		user := &user.User{}

		// Use reflection to check that required fields exist
		// This is a basic structure test
		assert.NotNil(t, user)

		// Test that we can set basic fields
		user.Username = "testuser"
		user.Email = "<EMAIL>"
		user.Role = "student"

		assert.Equal(t, "testuser", user.Username)
		assert.Equal(t, "<EMAIL>", user.Email)
		assert.Equal(t, "student", user.Role)
	})

	t.Run("pending registration model", func(t *testing.T) {
		pending := &user.PendingRegistration{}
		assert.NotNil(t, pending)

		pending.Username = "pendinguser"
		pending.Email = "<EMAIL>"
		pending.Token = "verification-token"

		assert.Equal(t, "pendinguser", pending.Username)
		assert.Equal(t, "<EMAIL>", pending.Email)
		assert.Equal(t, "verification-token", pending.Token)
	})

	t.Run("password reset token model", func(t *testing.T) {
		token := &user.PasswordResetToken{}
		assert.NotNil(t, token)

		token.UserID = "user-id"
		token.Token = "reset-token"

		assert.Equal(t, "user-id", token.UserID)
		assert.Equal(t, "reset-token", token.Token)
	})

	t.Run("email verification token model", func(t *testing.T) {
		token := &user.EmailVerificationToken{}
		assert.NotNil(t, token)

		token.UserID = "user-id"
		token.Token = "verify-token"

		assert.Equal(t, "user-id", token.UserID)
		assert.Equal(t, "verify-token", token.Token)
	})

	t.Run("resend limit model", func(t *testing.T) {
		limit := &user.ResendLimit{}
		assert.NotNil(t, limit)

		limit.Email = "<EMAIL>"
		limit.Count = 3

		assert.Equal(t, "<EMAIL>", limit.Email)
		assert.Equal(t, 3, limit.Count)
	})

	t.Run("rate limit entry model", func(t *testing.T) {
		entry := &user.RateLimitEntry{}
		assert.NotNil(t, entry)

		entry.Key = "rate-limit-key"
		entry.Endpoint = "login"

		assert.Equal(t, "rate-limit-key", entry.Key)
		assert.Equal(t, "login", entry.Endpoint)
	})
}

func TestDatabaseErrorHandling(t *testing.T) {
	t.Run("database connection string format", func(t *testing.T) {
		// Test that we can validate connection string formats without actually connecting
		validDSN := "host=localhost user=postgres password=pass dbname=test port=5432 sslmode=disable"
		assert.NotEmpty(t, validDSN)
		assert.Contains(t, validDSN, "host=")
		assert.Contains(t, validDSN, "user=")
		assert.Contains(t, validDSN, "dbname=")
		assert.Contains(t, validDSN, "port=")
	})
}

// Integration test helper (would be used with actual test database)
func setupTestDatabase(t *testing.T) *gorm.DB {
	t.Helper()

	// Check for test database environment variables
	testDBHost := os.Getenv("TEST_DB_HOST")
	testDBUser := os.Getenv("TEST_DB_USER")
	testDBPassword := os.Getenv("TEST_DB_PASSWORD")
	testDBName := os.Getenv("TEST_DB_NAME")

	if testDBHost == "" || testDBUser == "" || testDBPassword == "" || testDBName == "" {
		t.Skip("Integration test requires test database configuration - set TEST_DB_HOST, TEST_DB_USER, TEST_DB_PASSWORD, TEST_DB_NAME environment variables")
	}

	// Use environment variables for test database connection
	testDBPort := getEnvOrDefault("TEST_DB_PORT", "5432")
	testDBSSLMode := getEnvOrDefault("TEST_DB_SSLMODE", "disable")

	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=%s",
		testDBHost, testDBUser, testDBPassword, testDBName, testDBPort, testDBSSLMode)

	return InitDB(dsn)
}

func TestIntegrationWithRealDB(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	db := setupTestDatabase(t)
	if db == nil {
		return
	}

	defer func() {
		if sqlDB, err := db.DB(); err == nil {
			sqlDB.Close()
		}
	}()

	// Example integration test
	// Migration happens automatically in InitDB, so just test that tables exist
	assert.True(t, db.Migrator().HasTable(&user.User{}))
	assert.True(t, db.Migrator().HasTable(&user.PendingRegistration{}))
	assert.True(t, db.Migrator().HasTable(&user.PasswordResetToken{}))
	assert.True(t, db.Migrator().HasTable(&user.EmailVerificationToken{}))
	assert.True(t, db.Migrator().HasTable(&user.ResendLimit{}))
	assert.True(t, db.Migrator().HasTable(&user.RateLimitEntry{}))

	// Test basic database operations
	t.Run("can create and query user", func(t *testing.T) {
		// This is just a basic test to ensure the database is working
		// In a real integration test, you might test actual user operations

		// Test that we can execute a simple query
		var count int64
		err := db.Model(&user.User{}).Count(&count).Error
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, count, int64(0))
	})
}
