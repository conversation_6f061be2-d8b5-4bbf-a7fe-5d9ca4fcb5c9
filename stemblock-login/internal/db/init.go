package db

import (
	"log"
	"login/internal/user"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// InitDB initializes and returns a GORM DB connection using the provided DSN.
func InitDB(dsn string) *gorm.DB {
	db, err := gorm.Open(postgres.New(postgres.Config{
		DSN:                  dsn,
		PreferSimpleProtocol: true,
	}), &gorm.Config{})
	if err != nil {
		log.Fatalf("failed to connect to database: %v", err)
	}

	db.Exec("CREATE SCHEMA IF NOT EXISTS login;")

	// Auto-migrate user-related tables
	err = db.AutoMigrate(
		&user.User{},
		&user.PendingRegistration{},
		&user.PasswordResetToken{},
		&user.EmailVerificationToken{},
		&user.ResendLimit{},
		&user.RateLimitEntry{},
	)
	if err != nil {
		log.Fatalf("failed to migrate database: %v", err)
	}

	return db
}
