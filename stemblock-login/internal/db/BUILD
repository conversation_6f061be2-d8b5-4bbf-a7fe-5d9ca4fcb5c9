load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "db",
    srcs = glob(["*.go"], exclude = ["*_test.go"]),
    importpath = "login/internal/db",
    visibility = ["//visibility:public"],
    deps = [
        "//internal/user",
        "@io_gorm_driver_postgres//:postgres",
        "@io_gorm_gorm//:gorm",
    ],
)

go_test(
    name = "db_test",
    srcs = glob(["*_test.go"]),
    embed = [":db"],
    deps = [
        "//internal/user",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@io_gorm_driver_postgres//:postgres",
        "@io_gorm_gorm//:gorm",
    ],
)
