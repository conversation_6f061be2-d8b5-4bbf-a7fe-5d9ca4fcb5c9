load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "middleware",
    srcs = glob(["*.go"], exclude = ["*_test.go"]),
    importpath = "login/internal/middleware",
    visibility = ["//visibility:public"],
    deps = [
        "//internal/config",
        "//internal/errors",
        "//internal/user",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_google_uuid//:uuid",
        "@io_gorm_gorm//:gorm",
    ],
)

go_test(
    name = "middleware_test",
    srcs = glob(["*_test.go"]),
    embed = [":middleware"],
    deps = [
        "//internal/config",
        "//internal/user",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@io_gorm_gorm//:gorm",
    ],
)
