package middleware

import (
	"bytes"
	"encoding/json"
	"io"
	"log/slog"
	"login/internal/user"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// RateLimiter represents a rate limiter for different operations
type RateLimiter struct {
	requests map[string][]time.Time
	mutex    sync.RWMutex
	limit    int
	window   time.Duration
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter(limit int, window time.Duration) *RateLimiter {
	return &RateLimiter{
		requests: make(map[string][]time.Time),
		limit:    limit,
		window:   window,
	}
}

// Allow checks if a request from the given key is allowed
func (rl *RateLimiter) Allow(key string) bool {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	now := time.Now()

	// Clean up old requests
	if requests, exists := rl.requests[key]; exists {
		var validRequests []time.Time
		for _, reqTime := range requests {
			if now.Sub(reqTime) < rl.window {
				validRequests = append(validRequests, reqTime)
			}
		}
		rl.requests[key] = validRequests
	}

	// Check if limit is exceeded
	if len(rl.requests[key]) >= rl.limit {
		return false
	}

	// Add current request
	rl.requests[key] = append(rl.requests[key], now)
	return true
}

// RateLimitMiddleware creates a rate limiting middleware
func RateLimitMiddleware(limiter *RateLimiter, keyFunc func(*gin.Context) string) gin.HandlerFunc {
	return func(c *gin.Context) {
		key := keyFunc(c)

		if !limiter.Allow(key) {
			slog.Warn("Rate limit exceeded",
				"key", key,
				"ip", c.ClientIP(),
				"path", c.Request.URL.Path)

			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "Too many requests. Please try again later.",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// GetClientKey returns a key based on client IP
func GetClientKey(c *gin.Context) string {
	return c.ClientIP()
}

// GetEmailKey returns a key based on email from JSON body
func GetEmailKey(c *gin.Context) string {
	// Read the body once and store it for reuse
	body, err := c.GetRawData()
	if err != nil {
		return c.ClientIP() // fallback to IP
	}

	// Parse JSON to extract email
	var req struct {
		Email string `json:"email"`
	}
	if err := json.Unmarshal(body, &req); err != nil {
		// Restore body for the handler
		c.Request.Body = io.NopCloser(bytes.NewBuffer(body))
		return c.ClientIP() // fallback to IP
	}

	// Restore body for the handler
	c.Request.Body = io.NopCloser(bytes.NewBuffer(body))
	return req.Email
}

// GetUsernameKey returns a key based on username from JSON body
func GetUsernameKey(c *gin.Context) string {
	// Read the body once and store it for reuse
	body, err := c.GetRawData()
	if err != nil {
		return c.ClientIP() // fallback to IP
	}

	// Parse JSON to extract username
	var req struct {
		Username string `json:"username"`
	}
	if err := json.Unmarshal(body, &req); err != nil {
		// Restore body for the handler
		c.Request.Body = io.NopCloser(bytes.NewBuffer(body))
		return c.ClientIP() // fallback to IP
	}

	// Restore body for the handler
	c.Request.Body = io.NopCloser(bytes.NewBuffer(body))
	return req.Username
}

// ============================================================================
// DATABASE-BACKED RATE LIMITING (Traditional - counts all attempts)
// ============================================================================

// DBRateLimiter implements rate limiting using database storage
type DBRateLimiter struct {
	db       *gorm.DB
	limit    int
	window   time.Duration
	endpoint string // Identifies which endpoint this limiter is for
}

// NewDBRateLimiter creates a new database-backed rate limiter
func NewDBRateLimiter(db *gorm.DB, limit int, window time.Duration, endpoint string) *DBRateLimiter {
	return &DBRateLimiter{
		db:       db,
		limit:    limit,
		window:   window,
		endpoint: endpoint,
	}
}

// Allow checks if a request should be allowed based on database records
func (rl *DBRateLimiter) Allow(key string) bool {
	now := time.Now()
	windowStart := now.Add(-rl.window) // Calculate window start time

	// Step 1: Clean up expired entries (older than window)
	err := rl.db.Where("endpoint = ? AND timestamp < ?", rl.endpoint, windowStart).
		Delete(&user.RateLimitEntry{}).Error
	if err != nil {
		slog.Error("Failed to cleanup rate limit entries", "error", err, "endpoint", rl.endpoint)
	}

	// Step 2: Count current requests within the window
	var count int64
	err = rl.db.Model(&user.RateLimitEntry{}).
		Where("key = ? AND endpoint = ? AND timestamp >= ?", key, rl.endpoint, windowStart).
		Count(&count).Error
	if err != nil {
		slog.Error("Failed to count rate limit entries", "error", err, "key", key, "endpoint", rl.endpoint)
		return true // Fail open on database error
	}

	// Step 3: Check if limit is exceeded
	if count >= int64(rl.limit) {
		slog.Warn("Rate limit exceeded",
			"key", key,
			"endpoint", rl.endpoint,
			"count", count,
			"limit", rl.limit)
		return false
	}

	// Step 4: Record this request
	entry := user.RateLimitEntry{
		ID:        uuid.NewString(),
		Key:       key,
		Endpoint:  rl.endpoint,
		Timestamp: now,
		CreatedAt: now,
	}

	err = rl.db.Create(&entry).Error
	if err != nil {
		slog.Error("Failed to record rate limit entry", "error", err, "key", key, "endpoint", rl.endpoint)
		return true // Fail open on database error
	}

	slog.Debug("Rate limit check passed",
		"key", key,
		"endpoint", rl.endpoint,
		"count", count+1,
		"limit", rl.limit)

	return true
}

// DBRateLimitMiddleware creates a rate limiting middleware using database storage
func DBRateLimitMiddleware(limiter *DBRateLimiter, keyFunc func(*gin.Context) string) gin.HandlerFunc {
	return func(c *gin.Context) {
		key := keyFunc(c)

		if !limiter.Allow(key) {
			slog.Warn("Rate limit exceeded",
				"key", key,
				"ip", c.ClientIP(),
				"path", c.Request.URL.Path,
				"endpoint", limiter.endpoint)

			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "Too many requests. Please try again later.",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// ============================================================================
// SMART RATE LIMITING (Only counts meaningful attempts)
// ============================================================================

// SmartDBRateLimiter implements intelligent rate limiting that only counts meaningful attempts
type SmartDBRateLimiter struct {
	db       *gorm.DB
	limit    int
	window   time.Duration
	endpoint string
}

// NewSmartDBRateLimiter creates a new smart database-backed rate limiter
func NewSmartDBRateLimiter(db *gorm.DB, limit int, window time.Duration, endpoint string) *SmartDBRateLimiter {
	return &SmartDBRateLimiter{
		db:       db,
		limit:    limit,
		window:   window,
		endpoint: endpoint,
	}
}

// Allow checks if a request should be allowed (pre-check only)
func (rl *SmartDBRateLimiter) Allow(key string) bool {
	now := time.Now()
	windowStart := now.Add(-rl.window)

	// Clean up expired entries
	err := rl.db.Where("endpoint = ? AND timestamp < ?", rl.endpoint, windowStart).
		Delete(&user.RateLimitEntry{}).Error
	if err != nil {
		slog.Error("Failed to cleanup rate limit entries", "error", err, "endpoint", rl.endpoint)
	}

	// Count current requests within the window
	var count int64
	err = rl.db.Model(&user.RateLimitEntry{}).
		Where("key = ? AND endpoint = ? AND timestamp >= ?", key, rl.endpoint, windowStart).
		Count(&count).Error
	if err != nil {
		slog.Error("Failed to count rate limit entries", "error", err, "key", key, "endpoint", rl.endpoint)
		return true // Fail open on database error
	}

	// Check if limit is exceeded
	if count >= int64(rl.limit) {
		slog.Warn("Rate limit exceeded",
			"key", key,
			"endpoint", rl.endpoint,
			"count", count,
			"limit", rl.limit)
		return false
	}

	return true
}

// RecordAttempt records a meaningful attempt (only called after validation passes)
func (rl *SmartDBRateLimiter) RecordAttempt(key string) error {
	now := time.Now()
	entry := user.RateLimitEntry{
		ID:        uuid.NewString(),
		Key:       key,
		Endpoint:  rl.endpoint,
		Timestamp: now,
		CreatedAt: now,
	}

	err := rl.db.Create(&entry).Error
	if err != nil {
		slog.Error("Failed to record rate limit entry", "error", err, "key", key, "endpoint", rl.endpoint)
		return err
	}

	slog.Debug("Rate limit attempt recorded",
		"key", key,
		"endpoint", rl.endpoint)

	return nil
}

// ConditionalRateLimitMiddleware creates middleware that only records attempts based on response
func ConditionalRateLimitMiddleware(limiter *SmartDBRateLimiter, keyFunc func(*gin.Context) string, shouldCount func(*gin.Context) bool) gin.HandlerFunc {
	return func(c *gin.Context) {
		key := keyFunc(c)

		// Pre-check: only verify if limit is exceeded
		if !limiter.Allow(key) {
			slog.Warn("Rate limit exceeded",
				"key", key,
				"ip", c.ClientIP(),
				"path", c.Request.URL.Path,
				"endpoint", limiter.endpoint)

			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "Too many requests. Please try again later.",
			})
			c.Abort()
			return
		}

		// Process the request
		c.Next()

		// After request is processed, check if we should count it
		if shouldCount(c) {
			err := limiter.RecordAttempt(key)
			if err != nil {
				slog.Error("Failed to record rate limit attempt", "error", err, "key", key)
			}
		}
	}
}

// ShouldCountRegistrationAttempt determines if a registration attempt should be counted
func ShouldCountRegistrationAttempt(c *gin.Context) bool {
	status := c.Writer.Status()

	// Don't count validation errors (400 Bad Request)
	if status == http.StatusBadRequest {
		return false
	}

	// Don't count duplicate username/email errors (409 Conflict)
	if status == http.StatusConflict {
		return false
	}

	// Don't count rate limit errors (429 Too Many Requests)
	if status == http.StatusTooManyRequests {
		return false
	}

	// Count everything else:
	// - 200 OK (successful registration)
	// - 500 Internal Server Error (actual backend failures)
	// - Other server errors that indicate real processing attempts
	return true
}

// ShouldCountLoginAttempt determines if a login attempt should be counted
func ShouldCountLoginAttempt(c *gin.Context) bool {
	status := c.Writer.Status()

	// Don't count validation errors (400 Bad Request)
	if status == http.StatusBadRequest {
		return false
	}

	// Don't count rate limit errors (429 Too Many Requests)
	if status == http.StatusTooManyRequests {
		return false
	}

	// Count everything else:
	// - 200 OK (successful login)
	// - 401 Unauthorized (wrong credentials - this should be counted)
	// - 500 Internal Server Error (actual backend failures)
	return true
}

// ShouldCountPasswordResetAttempt determines if a password reset attempt should be counted
func ShouldCountPasswordResetAttempt(c *gin.Context) bool {
	status := c.Writer.Status()

	// Don't count validation errors (400 Bad Request)
	if status == http.StatusBadRequest {
		return false
	}

	// Don't count rate limit errors (429 Too Many Requests)
	if status == http.StatusTooManyRequests {
		return false
	}

	// Count everything else:
	// - 200 OK (successful password reset request)
	// - 404 Not Found (email not found - should be counted to prevent enumeration)
	// - 500 Internal Server Error (actual backend failures)
	return true
}

// ============================================================================
// RATE LIMIT CLEANUP SERVICE
// ============================================================================

// StartRateLimitCleanup starts a background goroutine to clean up expired rate limit entries
func StartRateLimitCleanup(db *gorm.DB) {
	go func() {
		ticker := time.NewTicker(5 * time.Minute) // Clean up every 5 minutes
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				cleanupExpiredRateLimitEntries(db)
			}
		}
	}()

	slog.Info("Started rate limit cleanup service")
}

// cleanupExpiredRateLimitEntries removes old rate limit entries from the database
func cleanupExpiredRateLimitEntries(db *gorm.DB) {
	// Clean up entries older than 1 hour (should cover all rate limit windows)
	cutoff := time.Now().Add(-1 * time.Hour)

	result := db.Where("timestamp < ?", cutoff).Delete(&user.RateLimitEntry{})
	if result.Error != nil {
		slog.Error("Failed to cleanup expired rate limit entries", "error", result.Error)
		return
	}

	if result.RowsAffected > 0 {
		slog.Debug("Cleaned up expired rate limit entries", "count", result.RowsAffected)
	}
}
