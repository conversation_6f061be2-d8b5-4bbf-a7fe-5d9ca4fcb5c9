package middleware

import (
	"bytes"
	"encoding/json"
	"login/internal/config"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestGetClientKey(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name         string
		clientIP     string
		forwardedFor string
		realIP       string
		expectedKey  string
	}{
		{
			name:        "direct client IP",
			clientIP:    "*************",
			expectedKey: "*************",
		},
		{
			name:         "with X-Forwarded-For header",
			clientIP:     "127.0.0.1",
			forwardedFor: "***********",
			expectedKey:  "***********", // ClientIP() should use the forwarded header
		},
		{
			name:        "localhost",
			clientIP:    "127.0.0.1",
			expectedKey: "127.0.0.1",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			req := httptest.NewRequest("GET", "/test", nil)
			if tt.forwardedFor != "" {
				req.Header.Set("X-Forwarded-For", tt.forwardedFor)
			}
			if tt.realIP != "" {
				req.Header.Set("X-Real-IP", tt.realIP)
			}
			req.RemoteAddr = tt.clientIP + ":12345"
			c.Request = req

			key := GetClientKey(c)
			assert.Equal(t, tt.expectedKey, key)
		})
	}
}

func TestGetEmailKey(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name        string
		requestBody map[string]interface{}
		expectedKey string
	}{
		{
			name: "valid email in body",
			requestBody: map[string]interface{}{
				"email": "<EMAIL>",
			},
			expectedKey: "<EMAIL>",
		},
		{
			name: "email with other fields",
			requestBody: map[string]interface{}{
				"email":    "<EMAIL>",
				"username": "testuser",
				"password": "password123",
			},
			expectedKey: "<EMAIL>",
		},
		{
			name: "missing email field",
			requestBody: map[string]interface{}{
				"username": "testuser",
				"password": "password123",
			},
			expectedKey: "", // Should return empty string when field is missing
		},
		{
			name: "empty email",
			requestBody: map[string]interface{}{
				"email": "",
			},
			expectedKey: "", // Empty email should be returned as-is
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			jsonBody, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest("POST", "/test", bytes.NewBuffer(jsonBody))
			req.Header.Set("Content-Type", "application/json")
			req.RemoteAddr = "127.0.0.1:12345"
			c.Request = req

			key := GetEmailKey(c)
			assert.Equal(t, tt.expectedKey, key)

			// Verify that the request body can still be read after GetEmailKey
			var body map[string]interface{}
			err := c.BindJSON(&body)
			assert.NoError(t, err)

			// Check that the body was properly restored
			if tt.requestBody["email"] != nil {
				assert.Equal(t, tt.requestBody["email"], body["email"])
			}
		})
	}
}

func TestGetUsernameKey(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name        string
		requestBody map[string]interface{}
		expectedKey string
	}{
		{
			name: "valid username in body",
			requestBody: map[string]interface{}{
				"username": "testuser",
			},
			expectedKey: "testuser",
		},
		{
			name: "username with other fields",
			requestBody: map[string]interface{}{
				"username": "myuser",
				"email":    "<EMAIL>",
				"password": "password123",
			},
			expectedKey: "myuser",
		},
		{
			name: "missing username field",
			requestBody: map[string]interface{}{
				"email":    "<EMAIL>",
				"password": "password123",
			},
			expectedKey: "", // Should return empty string when field is missing
		},
		{
			name: "empty username",
			requestBody: map[string]interface{}{
				"username": "",
			},
			expectedKey: "", // Empty username should be returned as-is
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			jsonBody, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest("POST", "/test", bytes.NewBuffer(jsonBody))
			req.Header.Set("Content-Type", "application/json")
			req.RemoteAddr = "127.0.0.1:12345"
			c.Request = req

			key := GetUsernameKey(c)
			assert.Equal(t, tt.expectedKey, key)

			// Verify that the request body can still be read after GetUsernameKey
			var body map[string]interface{}
			err := c.BindJSON(&body)
			assert.NoError(t, err)

			// Check that the body was properly restored
			if tt.requestBody["username"] != nil {
				assert.Equal(t, tt.requestBody["username"], body["username"])
			}
		})
	}
}

func TestSecurityHeaders(t *testing.T) {
	gin.SetMode(gin.TestMode)

	r := gin.New()
	r.Use(SecurityHeadersMiddleware())
	r.GET("/test", func(c *gin.Context) {
		c.JSON(200, gin.H{"message": "ok"})
	})

	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/test", nil)
	r.ServeHTTP(w, req)

	assert.Equal(t, 200, w.Code)

	// Check security headers
	headers := w.Header()
	assert.Equal(t, "nosniff", headers.Get("X-Content-Type-Options"))
	assert.Equal(t, "DENY", headers.Get("X-Frame-Options"))
	assert.Contains(t, headers.Get("Content-Security-Policy"), "default-src 'self'")
}

func TestCORSHeaders(t *testing.T) {
	gin.SetMode(gin.TestMode)

	cfg := &config.Config{
		Server: config.ServerConfig{
			BaseURL: "https://example.com",
		},
	}

	r := gin.New()
	r.Use(CORSMiddleware(cfg))
	r.GET("/test", func(c *gin.Context) {
		c.JSON(200, gin.H{"message": "ok"})
	})

	t.Run("CORS middleware works", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Origin", "https://example.com")

		r.ServeHTTP(w, req)

		assert.Equal(t, 200, w.Code)

		// Check that CORS headers are set
		headers := w.Header()
		assert.NotEmpty(t, headers.Get("Access-Control-Allow-Origin"))
	})
}

func TestInvalidJSONHandling(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name        string
		requestBody string
		keyFunc     func(*gin.Context) string
	}{
		{
			name:        "invalid JSON for email key",
			requestBody: `{"email": "<EMAIL>", "invalid": }`,
			keyFunc:     GetEmailKey,
		},
		{
			name:        "invalid JSON for username key",
			requestBody: `{"username": "testuser", "invalid": }`,
			keyFunc:     GetUsernameKey,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			req := httptest.NewRequest("POST", "/test", bytes.NewBufferString(tt.requestBody))
			req.Header.Set("Content-Type", "application/json")
			req.RemoteAddr = "127.0.0.1:12345"
			c.Request = req

			key := tt.keyFunc(c)
			// Should fallback to IP when JSON is invalid
			assert.Equal(t, "127.0.0.1", key)
		})
	}
}
