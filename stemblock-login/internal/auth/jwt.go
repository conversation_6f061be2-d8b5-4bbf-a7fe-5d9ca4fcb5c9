package auth

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"login/internal/config"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

var jwtSecret []byte

// InitJWT initializes the JWT secret from configuration
func InitJWT(cfg *config.Config) {
	secret := cfg.JWT.Secret
	if secret == "" {
		// Generate a random secret if not provided (for development)
		// In production, this should always be set via environment variable
		fmt.Println("WARNING: JWT_SECRET not set, generating random secret (not suitable for production)")
		randomBytes := make([]byte, 32)
		if _, err := rand.Read(randomBytes); err != nil {
			panic("Failed to generate random JWT secret: " + err.Error())
		}
		secret = hex.EncodeToString(randomBytes)
	}
	jwtSecret = []byte(secret)
}

func GenerateToken(username string) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"username": username,
		"exp":      time.Now().Add(time.Hour * 1).Unix(), // expires in 1 hour
	})
	return token.SignedString(jwtSecret)

}

func ValidateToken(tokenStr string) (*jwt.Token, error) {
	return jwt.Parse(tokenStr, func(token *jwt.Token) (any, error) {
		return jwtSecret, nil
	})
}
