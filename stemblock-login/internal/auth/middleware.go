package auth

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

// @Summary		Access user dashboard
// @Description	Returns personalized dashboard message. Requires valid JWT cookie.
// @Tags			dashboard
// @Produce		json
// @Success		200	{object}	map[string]string	"Welcome message"
// @Failure		401	{object}	map[string]string	"Missing or invalid token"
// @Router			/dashboard [get]
// @Security		ApiKeyAuth
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		tokenStr, err := c.<PERSON>ie("token")
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Missing auth token"})
			return
		}

		token, err := ValidateToken(tokenStr)
		if err != nil || !token.Valid {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid or expired token"})
			return
		}

		claims, ok := token.Claims.(jwt.MapClaims)
		if !ok {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid token claims"})
			return
		}

		usernameVal, usernameOk := claims["username"]
		if !usernameOk {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Token missing username"})
			return
		}
		username, ok := usernameVal.(string)
		if !ok {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Token username has invalid type"})
			return
		}
		c.Set("user", username)

		c.Next()
	}
}
