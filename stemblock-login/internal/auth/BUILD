load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "auth",
    srcs = glob(["*.go"], exclude = ["*_test.go"]),
    importpath = "login/internal/auth",
    visibility = ["//visibility:public"],
    deps = [
        "//internal/config",
        "@com_github_golang_jwt_jwt_v5//:jwt",
        "@org_golang_x_crypto//bcrypt",
        "@com_github_gin_gonic_gin//:gin",
    ],
)

go_test(
    name = "auth_test",
    srcs = glob(["*_test.go"]),
    embed = [":auth"],
    deps = [
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)
