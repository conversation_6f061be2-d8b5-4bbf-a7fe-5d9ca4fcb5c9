package auth

import (
	"crypto/rand"
	"encoding/hex"
	"errors"

	"golang.org/x/crypto/bcrypt"
)

// HashPassword hashes pw with bcrypt and returns a base-64 string.
func HashPassword(pw string) (string, error) {
	if pw == "" {
		return "", bcrypt.ErrPasswordTooLong // or custom error
	}
	hash, err := bcrypt.GenerateFromPassword([]byte(pw), bcrypt.DefaultCost)
	return string(hash), err
}

// CheckPassword returns nil if pw matches hash, or an error otherwise.
func CheckPassword(pw, hash string) error {
	if pw == "" || hash == "" {
		return bcrypt.ErrMismatchedHashAndPassword
	}
	return bcrypt.CompareHashAndPassword([]byte(hash), []byte(pw))
}

// GenerateRandomToken generates a secure random string for email verification tokens.
func GenerateRandomToken() (string, error) {
	b := make([]byte, 32)
	_, err := rand.Read(b)
	if err != nil {
		return "", errors.New("failed to generate random token")
	}
	return hex.EncodeToString(b), nil
}
