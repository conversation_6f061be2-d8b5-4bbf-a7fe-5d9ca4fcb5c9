package auth

import (
	"login/internal/config"
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestHashPassword(t *testing.T) {
	tests := []struct {
		name     string
		password string
		wantErr  bool
	}{
		{
			name:     "valid password",
			password: "testpassword123",
			wantErr:  false,
		},
		{
			name:     "empty password",
			password: "",
			wantErr:  true,
		},
		{
			name:     "long password",
			password: "verylongpasswordthatshouldalsowork123!@#",
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			hash, err := HashPassword(tt.password)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, hash)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, hash)
				assert.NotEqual(t, tt.password, hash) // Hash should be different from password
			}
		})
	}
}

func TestCheckPassword(t *testing.T) {
	// First, create a valid hash
	password := "testpassword123"
	hash, err := HashPassword(password)
	require.NoError(t, err)
	require.NotEmpty(t, hash)

	tests := []struct {
		name     string
		password string
		hash     string
		wantErr  bool
	}{
		{
			name:     "correct password",
			password: password,
			hash:     hash,
			wantErr:  false,
		},
		{
			name:     "incorrect password",
			password: "wrongpassword",
			hash:     hash,
			wantErr:  true,
		},
		{
			name:     "empty password",
			password: "",
			hash:     hash,
			wantErr:  true,
		},
		{
			name:     "empty hash",
			password: password,
			hash:     "",
			wantErr:  true,
		},
		{
			name:     "both empty",
			password: "",
			hash:     "",
			wantErr:  true,
		},
		{
			name:     "invalid hash format",
			password: password,
			hash:     "invalid-hash",
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := CheckPassword(tt.password, tt.hash)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestGenerateRandomToken(t *testing.T) {
	// Test multiple generations to ensure randomness
	tokens := make(map[string]bool)

	for i := 0; i < 10; i++ {
		token, err := GenerateRandomToken()
		assert.NoError(t, err)
		assert.NotEmpty(t, token)
		assert.Len(t, token, 64) // 32 bytes * 2 (hex encoding) = 64 characters

		// Ensure token is unique
		assert.False(t, tokens[token], "Generated duplicate token: %s", token)
		tokens[token] = true
	}
}

func TestJWTFunctions(t *testing.T) {
	// Initialize JWT with test config
	cfg := &config.Config{
		JWT: config.JWTConfig{
			Secret: "test-secret-key-for-testing",
		},
	}
	InitJWT(cfg)

	username := "testuser"

	t.Run("generate and validate token", func(t *testing.T) {
		token, err := GenerateToken(username)
		assert.NoError(t, err)
		assert.NotEmpty(t, token)

		// Validate the token
		parsedToken, err := ValidateToken(token)
		assert.NoError(t, err)
		assert.True(t, parsedToken.Valid)

		// Check claims
		if claims, ok := parsedToken.Claims.(jwt.MapClaims); ok {
			assert.Equal(t, username, claims["username"])
			assert.NotNil(t, claims["exp"])
		}
	})

	t.Run("validate invalid token", func(t *testing.T) {
		invalidToken := "invalid.jwt.token"
		_, err := ValidateToken(invalidToken)
		assert.Error(t, err)
	})

	t.Run("validate empty token", func(t *testing.T) {
		_, err := ValidateToken("")
		assert.Error(t, err)
	})

	t.Run("validate token with wrong secret", func(t *testing.T) {
		// Generate token with current secret
		token, err := GenerateToken(username)
		assert.NoError(t, err)

		// Change the secret
		cfg.JWT.Secret = "different-secret"
		InitJWT(cfg)

		// Try to validate with different secret
		_, err = ValidateToken(token)
		assert.Error(t, err)

		// Restore original secret for other tests
		cfg.JWT.Secret = "test-secret-key-for-testing"
		InitJWT(cfg)
	})
}

func TestInitJWT(t *testing.T) {
	t.Run("with provided secret", func(t *testing.T) {
		cfg := &config.Config{
			JWT: config.JWTConfig{
				Secret: "my-test-secret",
			},
		}
		InitJWT(cfg)

		// Test that JWT functions work with the secret
		token, err := GenerateToken("test")
		assert.NoError(t, err)
		assert.NotEmpty(t, token)
	})

	t.Run("with empty secret generates random", func(t *testing.T) {
		cfg := &config.Config{
			JWT: config.JWTConfig{
				Secret: "",
			},
		}

		// This should not panic and should generate a random secret
		assert.NotPanics(t, func() {
			InitJWT(cfg)
		})

		// Test that JWT functions work with the generated secret
		token, err := GenerateToken("test")
		assert.NoError(t, err)
		assert.NotEmpty(t, token)
	})
}

func TestPasswordHashConsistency(t *testing.T) {
	password := "consistencytest123"

	// Generate multiple hashes of the same password
	hash1, err1 := HashPassword(password)
	hash2, err2 := HashPassword(password)

	assert.NoError(t, err1)
	assert.NoError(t, err2)
	assert.NotEmpty(t, hash1)
	assert.NotEmpty(t, hash2)

	// Hashes should be different (due to salt)
	assert.NotEqual(t, hash1, hash2)

	// But both should validate against the original password
	assert.NoError(t, CheckPassword(password, hash1))
	assert.NoError(t, CheckPassword(password, hash2))
}

func TestJWTTokenExpiration(t *testing.T) {
	cfg := &config.Config{
		JWT: config.JWTConfig{
			Secret: "test-secret-for-expiration",
		},
	}
	InitJWT(cfg)

	username := "testuser"

	token, err := GenerateToken(username)
	assert.NoError(t, err)

	parsedToken, err := ValidateToken(token)
	assert.NoError(t, err)
	assert.True(t, parsedToken.Valid)

	// Check that expiration is set to future (1 hour from now)
	if claims, ok := parsedToken.Claims.(jwt.MapClaims); ok {
		if exp, ok := claims["exp"].(float64); ok {
			expTime := time.Unix(int64(exp), 0)
			assert.True(t, expTime.After(time.Now()))
			assert.True(t, expTime.Before(time.Now().Add(2*time.Hour))) // Should be within 2 hours
		}
	}
}
