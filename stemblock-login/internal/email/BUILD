load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "email",
    srcs = glob(["*.go"], exclude = ["*_test.go"]),
    importpath = "login/internal/email",
    visibility = ["//visibility:public"],
    deps = [
        "//internal/config",
        "@in_gopkg_mail_v2//:mail_v2",
    ],
)

go_test(
    name = "email_test",
    srcs = glob(["*_test.go"]),
    embed = [":email"],
    deps = [
        "//internal/config",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)
