package email

import (
	"login/internal/config"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestEmailConfiguration(t *testing.T) {
	t.Run("valid SMTP configuration", func(t *testing.T) {
		cfg := &config.Config{
			Email: config.EmailConfig{
				SMTPHost:  "smtp.gmail.com",
				SMTPPort:  587,
				FromEmail: "<EMAIL>",
				FromName:  "Test App",
				Password:  "apppassword",
			},
		}

		// Validate configuration
		assert.NotEmpty(t, cfg.Email.SMTPHost)
		assert.NotZero(t, cfg.Email.SMTPPort)
		assert.NotEmpty(t, cfg.Email.FromEmail)
		assert.NotEmpty(t, cfg.Email.FromName)
		assert.NotEmpty(t, cfg.Email.Password)

		// Validate email formats
		assert.Contains(t, cfg.Email.FromEmail, "@")

		// Validate port is positive
		assert.Greater(t, cfg.Email.SMTPPort, 0)
	})
}

func TestEmailValidation(t *testing.T) {
	tests := []struct {
		name    string
		email   string
		isValid bool
	}{
		{"valid email", "<EMAIL>", true},
		{"invalid - no @", "invalid-email", false},
		{"empty email", "", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.isValid {
				assert.Contains(t, tt.email, "@")
				assert.NotEmpty(t, tt.email)
			} else {
				if tt.email == "" {
					assert.Empty(t, tt.email)
				} else {
					assert.NotContains(t, tt.email, "@")
				}
			}
		})
	}
}
