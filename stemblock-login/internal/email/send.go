package email

import (
	"fmt"
	"login/internal/config"

	"gopkg.in/mail.v2"
)

// SendSupportEmail sends an email using the configured SMTP server with SSL/TLS.
func SendSupportEmail(cfg *config.Config, to, subject, body string) error {
	if cfg.Email.Password == "" {
		return fmt.Errorf("email password not configured")
	}

	d := mail.NewDialer(cfg.Email.SMTPHost, cfg.Email.SMTPPort, cfg.Email.FromEmail, cfg.Email.Password)
	d.SSL = true

	m := mail.NewMessage()
	m.SetHeader("From", m.<PERSON>at<PERSON>(cfg.Email.FromEmail, cfg.Email.FromName))
	m.SetHeader("To", to)
	m.SetHeader("Subject", subject)
	m.Set<PERSON>ody("text/plain", body)

	if err := d.DialAndSend(m); err != nil {
		return fmt.Errorf("failed to send email: %w", err)
	}
	return nil
}
