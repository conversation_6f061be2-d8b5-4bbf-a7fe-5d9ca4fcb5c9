package validation

import (
	"fmt"
	"regexp"
	"strings"
	"unicode"
)

// ValidationError represents a validation error
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

// ValidationErrors represents multiple validation errors
type ValidationErrors []ValidationError

func (ve ValidationErrors) Error() string {
	var messages []string
	for _, err := range ve {
		messages = append(messages, fmt.Sprintf("%s: %s", err.Field, err.Message))
	}
	return strings.Join(messages, "; ")
}

// Email validation
var emailRegex = regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)

// Username validation (alphanumeric, underscore, hyphen, 3-30 chars)
var usernameRegex = regexp.MustCompile(`^[a-zA-Z0-9_-]{3,30}$`)

// ValidateEmail validates email format
func ValidateEmail(email string) error {
	if email == "" {
		return fmt.Errorf("email is required")
	}
	if len(email) > 254 {
		return fmt.Errorf("email is too long (max 254 characters)")
	}
	if !emailRegex.MatchString(email) {
		return fmt.Errorf("invalid email format")
	}
	return nil
}

// ValidateUsername validates username format
func ValidateUsername(username string) error {
	if username == "" {
		return fmt.Errorf("username is required")
	}
	if len(username) < 3 {
		return fmt.Errorf("username must be at least 3 characters long")
	}
	if len(username) > 30 {
		return fmt.Errorf("username must be no more than 30 characters long")
	}
	if !usernameRegex.MatchString(username) {
		return fmt.Errorf("username can only contain letters, numbers, underscores, and hyphens")
	}
	return nil
}

// ValidatePassword validates password strength
func ValidatePassword(password string) error {
	if password == "" {
		return fmt.Errorf("password is required")
	}
	if len(password) < 8 {
		return fmt.Errorf("password must be at least 8 characters long")
	}
	if len(password) > 128 {
		return fmt.Errorf("password is too long (max 128 characters)")
	}

	var (
		hasUpper   = false
		hasLower   = false
		hasNumber  = false
		hasSpecial = false
	)

	for _, char := range password {
		switch {
		case unicode.IsUpper(char):
			hasUpper = true
		case unicode.IsLower(char):
			hasLower = true
		case unicode.IsNumber(char):
			hasNumber = true
		case unicode.IsPunct(char) || unicode.IsSymbol(char):
			hasSpecial = true
		}
	}

	var missing []string
	if !hasUpper {
		missing = append(missing, "uppercase letter")
	}
	if !hasLower {
		missing = append(missing, "lowercase letter")
	}
	if !hasNumber {
		missing = append(missing, "number")
	}
	if !hasSpecial {
		missing = append(missing, "special character")
	}

	if len(missing) > 0 {
		return fmt.Errorf("password must contain at least one %s", strings.Join(missing, ", "))
	}

	return nil
}

// ValidateUserRegistration validates all user registration fields
func ValidateUserRegistration(username, email, password string) ValidationErrors {
	var errors ValidationErrors

	if err := ValidateUsername(username); err != nil {
		errors = append(errors, ValidationError{Field: "username", Message: err.Error()})
	}

	if err := ValidateEmail(email); err != nil {
		errors = append(errors, ValidationError{Field: "email", Message: err.Error()})
	}

	if err := ValidatePassword(password); err != nil {
		errors = append(errors, ValidationError{Field: "password", Message: err.Error()})
	}

	return errors
}

// ValidateUserLogin validates login fields
func ValidateUserLogin(username, password string) ValidationErrors {
	var errors ValidationErrors

	if username == "" {
		errors = append(errors, ValidationError{Field: "username", Message: "username is required"})
	}

	if password == "" {
		errors = append(errors, ValidationError{Field: "password", Message: "password is required"})
	}

	return errors
}

// ValidateEmailOnly validates just an email field
func ValidateEmailOnly(email string) ValidationErrors {
	var errors ValidationErrors

	if err := ValidateEmail(email); err != nil {
		errors = append(errors, ValidationError{Field: "email", Message: err.Error()})
	}

	return errors
}
