package validation

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestValidateEmail(t *testing.T) {
	tests := []struct {
		name    string
		email   string
		wantErr bool
		errMsg  string
	}{
		{
			name:    "valid email",
			email:   "<EMAIL>",
			wantErr: false,
		},
		{
			name:    "valid email with subdomain",
			email:   "<EMAIL>",
			wantErr: false,
		},
		{
			name:    "valid email with plus",
			email:   "<EMAIL>",
			wantErr: false,
		},
		{
			name:    "empty email",
			email:   "",
			wantErr: true,
			errMsg:  "email is required",
		},
		{
			name:    "invalid email format - no @",
			email:   "invalid-email",
			wantErr: true,
			errMsg:  "invalid email format",
		},
		{
			name:    "invalid email format - no domain",
			email:   "user@",
			wantErr: true,
			errMsg:  "invalid email format",
		},
		{
			name:    "invalid email format - no user",
			email:   "@example.com",
			wantErr: true,
			errMsg:  "invalid email format",
		},
		{
			name:    "invalid email format - multiple @",
			email:   "user@@example.com",
			wantErr: true,
			errMsg:  "invalid email format",
		},
		{
			name:    "email too long",
			email:   "<EMAIL>",
			wantErr: true,
			errMsg:  "email is too long",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateEmail(tt.email)
			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateUsername(t *testing.T) {
	tests := []struct {
		name     string
		username string
		wantErr  bool
		errMsg   string
	}{
		{
			name:     "valid username",
			username: "testuser",
			wantErr:  false,
		},
		{
			name:     "valid username with numbers",
			username: "user123",
			wantErr:  false,
		},
		{
			name:     "valid username with underscore",
			username: "test_user",
			wantErr:  false,
		},
		{
			name:     "valid username with hyphen",
			username: "test-user",
			wantErr:  false,
		},
		{
			name:     "empty username",
			username: "",
			wantErr:  true,
			errMsg:   "username is required",
		},
		{
			name:     "username too short",
			username: "ab",
			wantErr:  true,
			errMsg:   "username must be at least 3 characters long",
		},
		{
			name:     "username too long",
			username: "verylongusernamethatexceedsthelimit",
			wantErr:  true,
			errMsg:   "username must be no more than 30 characters long",
		},
		{
			name:     "username with invalid characters",
			username: "user@name",
			wantErr:  true,
			errMsg:   "username can only contain letters, numbers, underscores, and hyphens",
		},
		{
			name:     "username with spaces",
			username: "user name",
			wantErr:  true,
			errMsg:   "username can only contain letters, numbers, underscores, and hyphens",
		},
		{
			name:     "username starting with number",
			username: "123user",
			wantErr:  false, // This should be allowed
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateUsername(tt.username)
			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidatePassword(t *testing.T) {
	tests := []struct {
		name     string
		password string
		wantErr  bool
		errMsg   string
	}{
		{
			name:     "valid strong password",
			password: "StrongPass123!",
			wantErr:  false,
		},
		{
			name:     "valid password with special chars",
			password: "MyP@ssw0rd!",
			wantErr:  false,
		},
		{
			name:     "empty password",
			password: "",
			wantErr:  true,
			errMsg:   "password is required",
		},
		{
			name:     "password too short",
			password: "Short1!",
			wantErr:  true,
			errMsg:   "password must be at least 8 characters long",
		},
		{
			name:     "password without uppercase",
			password: "lowercase123!",
			wantErr:  true,
			errMsg:   "password must contain at least one uppercase letter",
		},
		{
			name:     "password without lowercase",
			password: "UPPERCASE123!",
			wantErr:  true,
			errMsg:   "password must contain at least one lowercase letter",
		},
		{
			name:     "password without numbers",
			password: "NoNumbers!",
			wantErr:  true,
			errMsg:   "password must contain at least one number",
		},
		{
			name:     "password without special characters",
			password: "NoSpecialChars123",
			wantErr:  true,
			errMsg:   "password must contain at least one special character",
		},
		{
			name:     "password too long",
			password: "VeryLongPasswordThatExceedsTheMaximumLengthLimitVeryLongPasswordThatExceedsTheMaximumLengthLimitVeryLongPasswordThatExceedsTheMaximumLengthLimit123!",
			wantErr:  true,
			errMsg:   "password is too long",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidatePassword(tt.password)
			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateUserRegistration(t *testing.T) {
	tests := []struct {
		name     string
		username string
		email    string
		password string
		wantErr  bool
	}{
		{
			name:     "valid user data",
			username: "testuser",
			email:    "<EMAIL>",
			password: "StrongPass123!",
			wantErr:  false,
		},
		{
			name:     "invalid username",
			username: "ab",
			email:    "<EMAIL>",
			password: "StrongPass123!",
			wantErr:  true,
		},
		{
			name:     "invalid email",
			username: "testuser",
			email:    "invalid-email",
			password: "StrongPass123!",
			wantErr:  true,
		},
		{
			name:     "invalid password",
			username: "testuser",
			email:    "<EMAIL>",
			password: "weak",
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			errors := ValidateUserRegistration(tt.username, tt.email, tt.password)
			if tt.wantErr {
				assert.NotEmpty(t, errors)
			} else {
				assert.Empty(t, errors)
			}
		})
	}
}

func TestValidateUserLogin(t *testing.T) {
	tests := []struct {
		name     string
		username string
		password string
		wantErr  bool
	}{
		{
			name:     "valid login data",
			username: "testuser",
			password: "password123",
			wantErr:  false,
		},
		{
			name:     "empty username",
			username: "",
			password: "password123",
			wantErr:  true,
		},
		{
			name:     "empty password",
			username: "testuser",
			password: "",
			wantErr:  true,
		},
		{
			name:     "both empty",
			username: "",
			password: "",
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			errors := ValidateUserLogin(tt.username, tt.password)
			if tt.wantErr {
				assert.NotEmpty(t, errors)
			} else {
				assert.Empty(t, errors)
			}
		})
	}
}

func TestValidateEmailOnly(t *testing.T) {
	tests := []struct {
		name    string
		email   string
		wantErr bool
	}{
		{
			name:    "valid email",
			email:   "<EMAIL>",
			wantErr: false,
		},
		{
			name:    "invalid email",
			email:   "invalid-email",
			wantErr: true,
		},
		{
			name:    "empty email",
			email:   "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			errors := ValidateEmailOnly(tt.email)
			if tt.wantErr {
				assert.NotEmpty(t, errors)
			} else {
				assert.Empty(t, errors)
			}
		})
	}
}
