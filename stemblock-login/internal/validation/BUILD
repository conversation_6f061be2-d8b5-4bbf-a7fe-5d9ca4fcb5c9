load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "validation",
    srcs = glob(["*.go"], exclude = ["*_test.go"]),
    importpath = "login/internal/validation",
    visibility = ["//visibility:public"],
)

go_test(
    name = "validation_test",
    srcs = glob(["*_test.go"]),
    embed = [":validation"],
    deps = [
        "@com_github_stretchr_testify//assert",
    ],
)
