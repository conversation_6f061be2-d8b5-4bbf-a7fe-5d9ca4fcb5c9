package user_test

import (
	"fmt"
	"login/internal/user"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func setupTestDB(t *testing.T) *gorm.DB {
	// Use environment variables for test database connection
	host := getEnvOrDefault("TEST_DB_HOST", "localhost")
	dbUser := getEnvOrDefault("TEST_DB_USER", "postgres")
	password := os.Getenv("TEST_DB_PASSWORD")
	dbname := getEnvOrDefault("TEST_DB_NAME", "User_test")
	port := getEnvOrDefault("TEST_DB_PORT", "5432")

	if password == "" {
		t.Skip("TEST_DB_PASSWORD not set, skipping database tests")
	}

	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable",
		host, dbUser, password, dbname, port)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		t.Skipf("Failed to connect to test database: %v", err)
	}

	// Migrate all user models
	err = db.AutoMigrate(&user.User{}, &user.PendingRegistration{}, &user.PasswordResetToken{}, &user.EmailVerificationToken{}, &user.ResendLimit{}, &user.RateLimitEntry{})
	assert.NoError(t, err)

	return db
}

func cleanupTestDB(_ *testing.T, db *gorm.DB) {
	// Clean up test data
	db.Exec("DELETE FROM users WHERE username LIKE 'test%'")
	db.Exec("DELETE FROM pending_registrations WHERE username LIKE 'test%'")
	db.Exec("DELETE FROM password_reset_tokens WHERE token LIKE 'test%'")
	db.Exec("DELETE FROM email_verification_tokens WHERE token LIKE 'test%'")
	db.Exec("DELETE FROM resend_limits WHERE email LIKE 'test%'")
	db.Exec("DELETE FROM rate_limit_entries WHERE key LIKE 'test%'")
}

func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func TestUserCRUD(t *testing.T) {
	db := setupTestDB(t)
	defer cleanupTestDB(t, db)

	// Use unique test data to avoid conflicts
	timestamp := time.Now().UnixNano()
	username := fmt.Sprintf("testuser_%d", timestamp)
	email := fmt.Sprintf("<EMAIL>", timestamp)

	// Create
	hash := "hashedpass"
	uc := user.UserCreate{Username: username, Email: email, Password: hash, Role: "student"}
	err := user.StoreUser(db, uc, hash)
	assert.NoError(t, err)

	// Read
	u, err := user.GetUserByUsername(db, username)
	assert.NoError(t, err)
	assert.Equal(t, username, u.Username)

	// By email
	u2, err := user.GetUserByEmail(db, email)
	assert.NoError(t, err)
	assert.Equal(t, u.ID, u2.ID)

	// By id
	u3, err := user.GetUserById(db, u.ID)
	assert.NoError(t, err)
	assert.Equal(t, u.ID, u3.ID)
}

func TestPendingRegistrationCRUD(t *testing.T) {
	db := setupTestDB(t)
	pending := user.PendingRegistration{
		ID:        "pending-uuid",
		Username:  "pendinguser",
		Email:     "<EMAIL>",
		Password:  "hash",
		Role:      "student",
		Token:     "token123",
		ExpiresAt: time.Now().Add(1 * time.Hour),
		CreatedAt: time.Now(),
	}
	err := user.StorePendingRegistration(db, pending)
	assert.NoError(t, err)
	reg, err := user.GetPendingRegistrationByToken(db, "token123")
	assert.NoError(t, err)
	assert.Equal(t, "pendinguser", reg.Username)
	err = user.DeletePendingRegistrationByToken(db, "token123")
	assert.NoError(t, err)
	_, err = user.GetPendingRegistrationByToken(db, "token123")
	assert.Error(t, err)
}

func TestPasswordResetTokenCRUD(t *testing.T) {
	db := setupTestDB(t)
	userID := "user-uuid"
	token := "reset-token"
	expires := time.Now().Add(1 * time.Hour)
	err := user.StorePasswordResetToken(db, userID, token, expires)
	assert.NoError(t, err)
	tok, err := user.GetPasswordResetToken(db, token)
	assert.NoError(t, err)
	assert.Equal(t, userID, tok.UserID)
	err = user.DeletePasswordResetToken(db, token)
	assert.NoError(t, err)
	_, err = user.GetPasswordResetToken(db, token)
	assert.Error(t, err)
}

func TestDeleteExpiredPendingRegistrations(t *testing.T) {
	db := setupTestDB(t)
	pending := user.PendingRegistration{
		ID:        "expired-uuid",
		Username:  "expireduser",
		Email:     "<EMAIL>",
		Password:  "hash",
		Role:      "student",
		Token:     "expiredtoken",
		ExpiresAt: time.Now().Add(-1 * time.Hour),
		CreatedAt: time.Now().Add(-2 * time.Hour),
	}
	err := user.StorePendingRegistration(db, pending)
	assert.NoError(t, err)
	err = user.DeleteExpiredPendingRegistrations(db)
	assert.NoError(t, err)
	_, err = user.GetPendingRegistrationByToken(db, "expiredtoken")
	assert.Error(t, err)
}

func TestDeleteExpiredPasswordResetTokens(t *testing.T) {
	db := setupTestDB(t)
	token := user.PasswordResetToken{
		ID:        "expired-reset-uuid",
		UserID:    "user-uuid",
		Token:     "expired-reset-token",
		ExpiresAt: time.Now().Add(-1 * time.Hour),
		CreatedAt: time.Now().Add(-2 * time.Hour),
	}
	db.Create(&token)
	err := user.DeleteExpiredPasswordResetTokens(db)
	assert.NoError(t, err)
	_, err = user.GetPasswordResetToken(db, "expired-reset-token")
	assert.Error(t, err)
}

func TestGetEmailVerificationTokenByUserID(t *testing.T) {
	db := setupTestDB(t)
	token := user.EmailVerificationToken{
		ID:        "evtid-1",
		UserID:    "user-evtid-1",
		Token:     "evtoken123",
		ExpiresAt: time.Now().Add(1 * time.Hour),
		CreatedAt: time.Now(),
	}
	db.Create(&token)
	found, err := user.GetEmailVerificationTokenByUserID(db, "user-evtid-1")
	assert.NoError(t, err)
	assert.Equal(t, token.Token, found.Token)
	_, err = user.GetEmailVerificationTokenByUserID(db, "nonexistent")
	assert.Error(t, err)
}

func TestGetResendLimitByEmail(t *testing.T) {
	db := setupTestDB(t)
	rl := user.ResendLimit{
		Email:     "<EMAIL>",
		Count:     2,
		LastReset: time.Now(),
	}
	db.Create(&rl)
	found, err := user.GetResendLimitByEmail(db, "<EMAIL>")
	assert.NoError(t, err)
	assert.Equal(t, rl.Count, found.Count)
	_, err = user.GetResendLimitByEmail(db, "<EMAIL>")
	assert.Error(t, err)
}
