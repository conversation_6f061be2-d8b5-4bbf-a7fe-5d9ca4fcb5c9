package user

import (
	"log/slog"
	"login/internal/auth"
	"login/internal/config"
	"login/internal/email"
	"login/internal/validation"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// handleDatabaseError provides consistent error handling for database operations
func handleDatabaseError(c *gin.Context, err error, operation string, username, email string) {
	slog.Error("Database operation failed", "operation", operation, "error", err, "username", username, "email", email)

	errStr := err.Error()
	if strings.Contains(errStr, "duplicate key") || strings.Contains(errStr, "UNIQUE constraint") {
		if strings.Contains(errStr, "username") {
			c.JSON(http.StatusConflict, gin.H{"error": "Username is already taken"})
		} else if strings.Contains(errStr, "email") {
			c.<PERSON>(http.StatusConflict, gin.H{"error": "Email is already registered"})
		} else {
			c.<PERSON>(http.StatusConflict, gin.H{"error": "Username or email is already taken"})
		}
	} else {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process request. Please try again."})
	}
}

// Register, ForgotPassword, ResetPassword, VerifyEmail handlers

// CheckAndUpdateResendLimit checks and updates the resend limit for a given email. Returns (allowed, errorMsg).
func CheckAndUpdateResendLimit(dbConn *gorm.DB, email string) (bool, string) {
	rl, err := GetResendLimitByEmail(dbConn, email)
	now := time.Now()
	if err != nil {
		rl = &ResendLimit{Email: email, Count: 0, LastReset: now}
	} else {
		elapsed := now.Sub(rl.LastReset)
		if rl.Count >= 3 {
			if elapsed >= 5*time.Minute {
				rl.Count = 2 // regain 1 chance
				rl.LastReset = now
			} else {
				return false, "Too many verification attempts. Please wait a few minutes and try again."
			}
		} else if elapsed >= 5*time.Minute {
			regained := int(elapsed.Minutes()) / 5
			rl.Count -= regained
			if rl.Count < 0 {
				rl.Count = 0
			}
			rl.LastReset = now
		}
	}
	rl.Count++
	if rl.Count > 3 {
		rl.Count = 3
	}
	StoreOrUpdateResendLimit(dbConn, rl)
	return true, ""
}

func RegisterHandler(dbConn *gorm.DB, cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req UserCreate
		if err := c.BindJSON(&req); err != nil {
			slog.Warn("Registration JSON binding failed", "error", err)
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Invalid JSON format",
				"details": "Please check that your request contains valid JSON with username, email, and password fields",
			})
			return
		}

		// Validate input
		if validationErrors := validation.ValidateUserRegistration(req.Username, req.Email, req.Password); len(validationErrors) > 0 {
			slog.Warn("Registration validation failed",
				"username", req.Username,
				"email", req.Email,
				"errors", validationErrors)

			// Return validation errors directly to client
			c.JSON(http.StatusBadRequest, gin.H{
				"error":             "Validation failed",
				"validation_errors": validationErrors,
			})
			return
		}

		// Check if username or email is already registered (in users)
		if user, _ := GetUserByUsername(dbConn, req.Username); user != nil {
			c.JSON(http.StatusConflict, gin.H{"error": "Username already exists"})
			return
		}
		if user, _ := GetUserByEmail(dbConn, req.Email); user != nil {
			c.JSON(http.StatusConflict, gin.H{"error": "Email already used"})
			return
		}

		// Check for pending registration by email or username and apply resend limit logic
		var pending PendingRegistration
		pendingExists := false
		if err := dbConn.Where("email = ?", req.Email).First(&pending).Error; err == nil {
			pendingExists = true
		}
		if err := dbConn.Where("username = ?", req.Username).First(&pending).Error; err == nil {
			pendingExists = true
		}
		if pendingExists {
			allowed, msg := CheckAndUpdateResendLimit(dbConn, req.Email)
			if !allowed {
				c.JSON(http.StatusTooManyRequests, gin.H{"error": msg})
				return
			}
			// Delete old pending registration(s)
			dbConn.Where("email = ? OR username = ?", req.Email, req.Username).Delete(&PendingRegistration{})
			// Continue to create new pending registration and send email
		}

		hash, err := auth.HashPassword(req.Password)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to hash password"})
			return
		}
		token, err := auth.GenerateRandomToken()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate verification token"})
			return
		}
		expiresAt := time.Now().Add(24 * time.Hour)
		pending = PendingRegistration{
			ID:        uuid.NewString(),
			Username:  req.Username,
			Email:     req.Email,
			Password:  hash,
			Role:      "student",
			Token:     token,
			ExpiresAt: expiresAt,
			CreatedAt: time.Now(),
		}
		err = StorePendingRegistration(dbConn, pending)
		if err != nil {
			handleDatabaseError(c, err, "store pending registration", req.Username, req.Email)
			return
		}
		link := cfg.Server.BaseURL + "/verify-email?token=" + token
		emailErr := email.SendSupportEmail(
			cfg,
			pending.Email,
			"Verify your email",
			"Welcome! Please verify your email by clicking this link: "+link,
		)
		if emailErr != nil {
			slog.Error("Failed to send verification email",
				"error", emailErr,
				"email", pending.Email,
				"username", pending.Username)
			_ = DeletePendingRegistrationByToken(dbConn, token)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send verification email"})
			return
		}
		c.JSON(http.StatusOK, gin.H{"message": "User registered. Please check your email to verify your account. (It may be in your spam or junk folder.)"})
	}
}

func ForgotPasswordHandler(dbConn *gorm.DB, cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req struct {
			Email string `json:"email" binding:"required"`
		}
		if err := c.BindJSON(&req); err != nil {
			slog.Warn("Forgot password JSON binding failed", "error", err)
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Invalid JSON format",
				"details": "Please provide a valid email address in JSON format",
			})
			return
		}

		// Validate input
		if validationErrors := validation.ValidateEmailOnly(req.Email); len(validationErrors) > 0 {
			slog.Warn("Forgot password validation failed",
				"email", req.Email,
				"errors", validationErrors)

			c.JSON(http.StatusBadRequest, gin.H{
				"error":             "Validation failed",
				"validation_errors": validationErrors,
			})
			return
		}
		user, err := GetUserByEmail(dbConn, req.Email)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
			return
		}
		token, err := auth.GenerateRandomToken()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
			return
		}
		expiresAt := time.Now().Add(1 * time.Hour)
		err = StorePasswordResetToken(dbConn, user.ID, token, expiresAt)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to store reset token"})
			return
		}
		link := cfg.Server.BaseURL + "/reset-password?token=" + token
		emailErr := email.SendSupportEmail(
			cfg,
			user.Email,
			"Password Reset",
			"Click this link to reset your password: "+link,
		)
		if emailErr != nil {
			_ = DeletePasswordResetToken(dbConn, token)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send reset email"})
			return
		}
		c.JSON(http.StatusOK, gin.H{"message": "Password reset email sent. Please check your inbox. (It may be in your spam or junk folder.)"})
	}
}

func ResetPasswordHandler(dbConn *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.Query("token")
		if token == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Token required"})
			return
		}
		var req struct {
			Password string `json:"password" binding:"required"`
		}
		if err := c.BindJSON(&req); err != nil {
			slog.Warn("Reset password JSON binding failed", "error", err)
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Invalid JSON format",
				"details": "Please provide a valid password in JSON format",
			})
			return
		}
		// Validate password
		if validationErrors := validation.ValidatePassword(req.Password); validationErrors != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Password validation failed",
				"details": validationErrors.Error(),
			})
			return
		}

		resetToken, err := GetPasswordResetToken(dbConn, token)
		if err != nil || resetToken.ExpiresAt.Before(time.Now()) {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid or expired token"})
			return
		}
		user, err := GetUserById(dbConn, resetToken.UserID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
			return
		}
		hash, err := auth.HashPassword(req.Password)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to hash password"})
			return
		}
		err = dbConn.Model(user).Update("password_hash", hash).Error
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update password"})
			return
		}
		_ = DeletePasswordResetToken(dbConn, token)
		c.JSON(http.StatusOK, gin.H{"message": "Password reset successful. You may now log in."})
	}
}

func VerifyEmailHandler(dbConn *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.Query("token")
		if token == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Token required"})
			return
		}
		pending, err := GetPendingRegistrationByToken(dbConn, token)
		if err != nil || pending.ExpiresAt.Before(time.Now()) {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid or expired token"})
			return
		}
		if user, _ := GetUserByUsername(dbConn, pending.Username); user != nil {
			_ = DeletePendingRegistrationByToken(dbConn, token)
			c.JSON(http.StatusConflict, gin.H{"error": "Username already exists"})
			return
		}
		if user, _ := GetUserByEmail(dbConn, pending.Email); user != nil {
			_ = DeletePendingRegistrationByToken(dbConn, token)
			c.JSON(http.StatusConflict, gin.H{"error": "Email already exists"})
			return
		}
		userCreate := UserCreate{
			Username: pending.Username,
			Email:    pending.Email,
			Password: pending.Password, // already hashed
			Role:     pending.Role,
		}
		err = StoreUser(dbConn, userCreate, pending.Password)
		if err != nil {
			_ = DeletePendingRegistrationByToken(dbConn, token)
			c.JSON(http.StatusConflict, gin.H{"error": "User already exists"})
			return
		}
		_ = DeletePendingRegistrationByToken(dbConn, token)
		c.JSON(http.StatusOK, gin.H{"message": "Email verified. You may now log in."})
	}
}

func LoginHandler(dbConn *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req struct {
			Id       string `json:"id"`
			Username string `json:"username"`
			Password string `json:"password"`
		}

		if err := c.BindJSON(&req); err != nil {
			slog.Warn("Login JSON binding failed", "error", err)
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Invalid JSON format",
				"details": "Please provide username and password in JSON format",
			})
			return
		}

		// Validate input
		if validationErrors := validation.ValidateUserLogin(req.Username, req.Password); len(validationErrors) > 0 {
			slog.Warn("Login validation failed",
				"username", req.Username,
				"errors", validationErrors)

			c.JSON(http.StatusBadRequest, gin.H{
				"error":             "Validation failed",
				"validation_errors": validationErrors,
			})
			return
		}

		userObj, err := GetUserByUsername(dbConn, req.Username)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid username or password"})
			return
		}
		// Check if user has a pending email verification token
		if token, err := GetEmailVerificationTokenByUserID(dbConn, userObj.ID); err == nil && token.ExpiresAt.After(time.Now()) {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Please verify your email before logging in."})
			return
		}
		// Compare hash with provided password
		if err := auth.CheckPassword(req.Password, userObj.PasswordHash); err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid username or password"})
			return
		}

		// Generate JWT token
		tokenString, err := auth.GenerateToken(userObj.Username)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
			return
		}
		c.SetCookie("token", "", -1, "/", "", false, true)
		c.SetCookie("token", tokenString, 3600, "/", "", false, true) // 3600s = 1hr
		c.JSON(http.StatusOK, gin.H{
			"message": "Login successful",
			"token":   tokenString,
			"user": gin.H{
				"id":       userObj.ID,
				"username": userObj.Username,
				"email":    userObj.Email,
				"role":     userObj.Role,
			},
		})
	}
}

func ResendVerificationHandler(dbConn *gorm.DB, cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req struct {
			Email    string `json:"email" binding:"required"`
			Username string `json:"username" binding:"required"`
			Password string `json:"password" binding:"required"`
		}
		if err := c.BindJSON(&req); err != nil {
			slog.Warn("Resend verification JSON binding failed", "error", err)
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Invalid JSON format",
				"details": "Please provide email, username, and password in JSON format",
			})
			return
		}
		// Rate limit logic
		allowed, msg := CheckAndUpdateResendLimit(dbConn, req.Email)
		if !allowed {
			c.JSON(http.StatusTooManyRequests, gin.H{"error": msg})
			return
		}
		// Delete old pending registration
		dbConn.Where("email = ?", req.Email).Delete(&PendingRegistration{})
		// Create new pending registration
		hash, err := auth.HashPassword(req.Password)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to hash password"})
			return
		}
		token, err := auth.GenerateRandomToken()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate verification token"})
			return
		}
		expiresAt := time.Now().Add(24 * time.Hour)
		pending := PendingRegistration{
			ID:        uuid.NewString(),
			Username:  req.Username,
			Email:     req.Email,
			Password:  hash,
			Role:      "student",
			Token:     token,
			ExpiresAt: expiresAt,
			CreatedAt: time.Now(),
		}
		err = StorePendingRegistration(dbConn, pending)
		if err != nil {
			handleDatabaseError(c, err, "store pending registration for resend", req.Username, req.Email)
			return
		}
		link := cfg.Server.BaseURL + "/verify-email?token=" + token
		emailErr := email.SendSupportEmail(
			cfg,
			pending.Email,
			"Verify your email",
			"Welcome! Please verify your email by clicking this link: "+link,
		)
		if emailErr != nil {
			slog.Error("Failed to resend verification email",
				"error", emailErr,
				"email", pending.Email,
				"username", pending.Username)
			_ = DeletePendingRegistrationByToken(dbConn, token)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send verification email"})
			return
		}
		c.JSON(http.StatusOK, gin.H{"message": "Verification email resent. Please check your inbox. (It may be in your spam or junk folder.)"})
	}
}

// Periodically delete expired pending registrations (cleanup goroutine)
func StartPendingRegistrationCleanup(dbConn *gorm.DB) {
	go func() {
		ticker := time.NewTicker(10 * time.Minute)
		defer ticker.Stop()
		for {
			<-ticker.C
			_ = DeleteExpiredPendingRegistrations(dbConn)
		}
	}()
}

// Periodically delete expired password reset tokens (cleanup goroutine)
func StartPasswordResetTokenCleanup(dbConn *gorm.DB) {
	go func() {
		ticker := time.NewTicker(10 * time.Minute)
		defer ticker.Stop()
		for {
			<-ticker.C
			_ = DeleteExpiredPasswordResetTokens(dbConn)
		}
	}()
}
