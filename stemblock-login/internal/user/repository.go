package user

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// User DB logic
func StoreUser(db *gorm.DB, userCreate UserCreate, hash string) error {
	role := userCreate.Role
	if role == "" {
		role = "student"
	}
	user := User{
		ID:           uuid.NewString(),
		Username:     userCreate.Username,
		Email:        userCreate.Email,
		PasswordHash: hash,
		Role:         role,
	}
	return db.Create(&user).Error
}

func GetUserByUsername(db *gorm.DB, username string) (*User, error) {
	var user = &User{}
	err := db.Where("username = ?", username).First(&user).Error
	if err != nil {
		return nil, err
	}
	return user, nil
}

func GetUserById(db *gorm.DB, id string) (*User, error) {
	var user = &User{}
	err := db.Where("id = ?", id).First(&user).Error
	if err != nil {
		return nil, err
	}
	return user, nil
}

func GetUserByEmail(db *gorm.DB, email string) (*User, error) {
	var user = &User{}
	err := db.Where("LOWER(email) = LOWER(?)", email).First(&user).Error
	if err != nil {
		return nil, err
	}
	return user, nil
}

// PendingRegistration DB logic
func StorePendingRegistration(db *gorm.DB, reg PendingRegistration) error {
	return db.Create(&reg).Error
}

func GetPendingRegistrationByToken(db *gorm.DB, token string) (*PendingRegistration, error) {
	var reg PendingRegistration
	err := db.Where("token = ?", token).First(&reg).Error
	if err != nil {
		return nil, err
	}
	return &reg, nil
}

func DeletePendingRegistrationByToken(db *gorm.DB, token string) error {
	return db.Where("token = ?", token).Delete(&PendingRegistration{}).Error
}

func DeleteExpiredPendingRegistrations(db *gorm.DB) error {
	return db.Where("expires_at < ?", time.Now()).Delete(&PendingRegistration{}).Error
}

// PasswordResetToken DB logic
func StorePasswordResetToken(db *gorm.DB, userID, token string, expiresAt time.Time) error {
	resetToken := PasswordResetToken{
		ID:        uuid.NewString(),
		UserID:    userID,
		Token:     token,
		ExpiresAt: expiresAt,
		CreatedAt: time.Now(),
	}
	return db.Create(&resetToken).Error
}

func GetPasswordResetToken(db *gorm.DB, token string) (*PasswordResetToken, error) {
	var t PasswordResetToken
	err := db.Where("token = ?", token).First(&t).Error
	if err != nil {
		return nil, err
	}
	return &t, nil
}

func DeletePasswordResetToken(db *gorm.DB, token string) error {
	return db.Where("token = ?", token).Delete(&PasswordResetToken{}).Error
}

func DeleteExpiredPasswordResetTokens(db *gorm.DB) error {
	return db.Where("expires_at < ?", time.Now()).Delete(&PasswordResetToken{}).Error
}

func GetEmailVerificationTokenByUserID(db *gorm.DB, userID string) (*EmailVerificationToken, error) {
	t := &EmailVerificationToken{}
	err := db.Where("user_id = ?", userID).First(t).Error
	if err != nil {
		return nil, err
	}
	return t, nil
}

// ResendLimit DB logic
func GetResendLimitByEmail(db *gorm.DB, email string) (*ResendLimit, error) {
	var rl ResendLimit
	err := db.Where("email = ?", email).First(&rl).Error
	if err != nil {
		return nil, err
	}
	return &rl, nil
}

func StoreOrUpdateResendLimit(db *gorm.DB, rl *ResendLimit) error {
	return db.Save(rl).Error
}

func ResetResendLimitCount(db *gorm.DB, email string) error {
	return db.Model(&ResendLimit{}).Where("email = ?", email).Updates(map[string]interface{}{"count": 0, "last_reset": time.Now()}).Error
}
