load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "user",
    srcs = glob(["*.go"], exclude = ["*_test.go"]),
    importpath = "login/internal/user",
    visibility = ["//visibility:public"],
    deps = [
        "//internal/auth",
        "//internal/config",
        "//internal/email",
        "//internal/errors",
        "//internal/validation",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_google_uuid//:uuid",
        "@io_gorm_gorm//:gorm",
    ],
)

go_test(
    name = "user_test",
    srcs = glob(["*_test.go"]),
    embed = [":user"],
    deps = [
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@io_gorm_driver_postgres//:postgres",
        "@io_gorm_gorm//:gorm",
    ],
)
