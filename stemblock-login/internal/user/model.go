package user

import (
	"time"
)

// User, PendingRegistration, PasswordResetToken structs

type User struct {
	ID           string `gorm:"primaryKey"`
	Username     string `gorm:"unique;not null"`
	Email        string `gorm:"unique;not null"`
	PasswordHash string `gorm:"not null"`
	Role         string `gorm:"not null"`
	FirstName    string `gorm:"default:''"`
	LastName     string `gorm:"default:''"`
}

type UserCreate struct {
	Username string `json:"username" binding:"required"`
	Email    string `json:"email" binding:"required"`
	Password string `json:"password" binding:"required"`
	Role     string `json:"role"`
}

type PendingRegistration struct {
	ID        string    `gorm:"primaryKey;type:varchar(36)" json:"id"`
	Username  string    `gorm:"unique;not null" json:"username"`
	Email     string    `gorm:"unique;not null" json:"email"`
	Password  string    `gorm:"not null" json:"password"`
	Role      string    `gorm:"not null;default:student" json:"role"`
	Token     string    `gorm:"unique;not null" json:"token"`
	ExpiresAt time.Time `gorm:"not null" json:"expires_at"`
	CreatedAt time.Time
}

type PasswordResetToken struct {
	ID        string    `gorm:"primaryKey;type:varchar(36)"`
	UserID    string    `gorm:"not null"`
	Token     string    `gorm:"unique;not null"`
	ExpiresAt time.Time `gorm:"not null"`
	CreatedAt time.Time
}

type EmailVerificationToken struct {
	ID        string    `gorm:"primaryKey"`
	UserID    string    `gorm:"not null;index"`
	Token     string    `gorm:"unique;not null"`
	ExpiresAt time.Time `gorm:"not null"`
	CreatedAt time.Time
}

type ResendLimit struct {
	Email     string `gorm:"primaryKey"`
	Count     int
	LastReset time.Time
}

// RateLimitEntry represents a single rate limit request record
type RateLimitEntry struct {
	ID        string    `gorm:"primaryKey;type:varchar(36)"`
	Key       string    `gorm:"not null;index"` // IP address or email
	Endpoint  string    `gorm:"not null;index"` // login, register, password-reset
	Timestamp time.Time `gorm:"not null;index"` // When request was made
	CreatedAt time.Time
}

// Add TableName methods for schema support
func (User) TableName() string                   { return "users" }
func (PendingRegistration) TableName() string    { return "pending_registrations" }
func (PasswordResetToken) TableName() string     { return "password_reset_tokens" }
func (EmailVerificationToken) TableName() string { return "email_verification_tokens" }
func (ResendLimit) TableName() string            { return "resend_limits" }
func (RateLimitEntry) TableName() string         { return "rate_limit_entries" }
