<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>STEMBlock Login</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 400px; margin: 50px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { width: 100%; padding: 10px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .error { color: red; margin: 10px 0; padding: 10px; background: #ffe6e6; border-radius: 4px; }
        .success { color: green; margin: 10px 0; padding: 10px; background: #e6ffe6; border-radius: 4px; }
        .tabs { display: flex; margin-bottom: 20px; }
        .tab { flex: 1; padding: 10px; text-align: center; cursor: pointer; border: 1px solid #ddd; }
        .tab.active { background: #007bff; color: white; }
        .form-container { display: none; }
        .form-container.active { display: block; }
    </style>
</head>
<body>
    <h1>STEMBlock Authentication</h1>
    
    <!-- Tabs -->
    <div class="tabs">
        <div class="tab active" onclick="showForm('login')">Login</div>
        <div class="tab" onclick="showForm('register')">Register</div>
    </div>

    <!-- Login Form -->
    <div id="login-form" class="form-container active">
        <h2>Login</h2>
        <form id="loginForm">
            <div class="form-group">
                <label for="login-username">Username:</label>
                <input type="text" id="login-username" name="username" required>
            </div>
            <div class="form-group">
                <label for="login-password">Password:</label>
                <input type="password" id="login-password" name="password" required>
            </div>
            <button type="submit">Login</button>
        </form>
        <div id="login-errors"></div>
    </div>

    <!-- Register Form -->
    <div id="register-form" class="form-container">
        <h2>Register</h2>
        <form id="registerForm">
            <div class="form-group">
                <label for="register-username">Username:</label>
                <input type="text" id="register-username" name="username" required>
            </div>
            <div class="form-group">
                <label for="register-email">Email:</label>
                <input type="email" id="register-email" name="email" required>
            </div>
            <div class="form-group">
                <label for="register-password">Password:</label>
                <input type="password" id="register-password" name="password" required>
            </div>
            <button type="submit">Register</button>
        </form>
        <div id="register-errors"></div>
    </div>

    <script>
        // Configuration
        const API_BASE_URL = 'http://localhost:8080';

        // Utility function to make API calls
        async function apiCall(endpoint, method = 'GET', data = null) {
            const config = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include', // Important for cookies
            };

            if (data) {
                config.body = JSON.stringify(data);
            }

            try {
                const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
                const result = await response.json();

                if (!response.ok) {
                    throw { response: { data: result, status: response.status } };
                }

                return result;
            } catch (error) {
                throw error;
            }
        }

        // Show/hide forms
        function showForm(formType) {
            // Update tabs
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');

            // Update forms
            document.querySelectorAll('.form-container').forEach(form => form.classList.remove('active'));
            document.getElementById(`${formType}-form`).classList.add('active');

            // Clear errors
            clearErrors();
        }

        // Clear error messages
        function clearErrors() {
            document.getElementById('login-errors').innerHTML = '';
            document.getElementById('register-errors').innerHTML = '';
        }

        // Display errors
        function displayErrors(containerId, errors) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';

            if (Array.isArray(errors)) {
                errors.forEach(error => {
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'error';
                    errorDiv.textContent = error.field !== 'general' 
                        ? `${error.field}: ${error.message}` 
                        : error.message;
                    container.appendChild(errorDiv);
                });
            } else {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error';
                errorDiv.textContent = errors;
                container.appendChild(errorDiv);
            }
        }

        // Display success message
        function displaySuccess(containerId, message) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';
            const successDiv = document.createElement('div');
            successDiv.className = 'success';
            successDiv.textContent = message;
            container.appendChild(successDiv);
        }

        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            clearErrors();

            const formData = new FormData(e.target);
            const data = {
                username: formData.get('username'),
                password: formData.get('password')
            };

            const submitButton = e.target.querySelector('button');
            submitButton.disabled = true;
            submitButton.textContent = 'Logging in...';

            try {
                const result = await apiCall('/login', 'POST', data);
                console.log('Login successful:', result);
                
                // Redirect to dashboard or show success
                displaySuccess('login-errors', 'Login successful! Redirecting...');
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 1000);

            } catch (error) {
                console.error('Login error:', error);
                
                if (error.response?.data?.validation_errors) {
                    displayErrors('login-errors', error.response.data.validation_errors);
                } else if (error.response?.data?.error) {
                    displayErrors('login-errors', [{ field: 'general', message: error.response.data.error }]);
                } else {
                    displayErrors('login-errors', [{ field: 'general', message: 'Network error. Please try again.' }]);
                }
            } finally {
                submitButton.disabled = false;
                submitButton.textContent = 'Login';
            }
        });

        // Register form handler
        document.getElementById('registerForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            clearErrors();

            const formData = new FormData(e.target);
            const data = {
                username: formData.get('username'),
                email: formData.get('email'),
                password: formData.get('password')
            };

            const submitButton = e.target.querySelector('button');
            submitButton.disabled = true;
            submitButton.textContent = 'Registering...';

            try {
                const result = await apiCall('/register', 'POST', data);
                console.log('Registration successful:', result);
                
                displaySuccess('register-errors', 'Registration successful! Please check your email to verify your account.');
                e.target.reset(); // Clear form

            } catch (error) {
                console.error('Registration error:', error);
                
                if (error.response?.data?.validation_errors) {
                    displayErrors('register-errors', error.response.data.validation_errors);
                } else if (error.response?.data?.error) {
                    displayErrors('register-errors', [{ field: 'general', message: error.response.data.error }]);
                } else {
                    displayErrors('register-errors', [{ field: 'general', message: 'Network error. Please try again.' }]);
                }
            } finally {
                submitButton.disabled = false;
                submitButton.textContent = 'Register';
            }
        });
    </script>
</body>
</html>
