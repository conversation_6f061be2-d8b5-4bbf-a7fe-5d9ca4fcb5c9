# Ignore files and directories that <PERSON><PERSON> shouldn't process

# Environment and config files
.env
.env.*

# Documentation
*.md
LICENSE

# Scripts
scripts/

# Examples (frontend integration examples)
examples/

# Generated API client (can be regenerated)
api/go-client/

# Build artifacts
bazel-*
.bazel/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Go module files (Bazel manages dependencies)
vendor/
