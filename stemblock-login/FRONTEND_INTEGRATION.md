# Frontend Integration Guide

## Cloud-to-Cloud Integration

Your Go backend is configured for cloud hosting and will communicate with your cloud-hosted frontend.

### 1. Configure Environment Variables

Add these to your `.env` file:

```bash
# Cloud hosting configuration
BASE_URL=https://your-backend-domain.com
FRONTEND_URL=https://your-frontend-domain.com
FORCE_HTTPS=true
ENVIRONMENT=production

# For development/testing only (NOT for production)
# ALLOW_ALL_ORIGINS=true
```

### 2. Share with Frontend Team

Send your frontend team:

1. **Backend URL**: `https://your-backend-domain.com` (or `http://localhost:8080` for local testing)
2. **API Documentation**: `API_DOCUMENTATION.md`
3. **Required fetch configuration**:

```javascript
// Frontend team needs to use this configuration
const API_BASE_URL = 'https://your-backend-domain.com';

fetch(`${API_BASE_URL}/login`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  credentials: 'include', // CRITICAL: This enables cookies/JWT
  body: JSON.stringify({
    username: 'testuser',
    password: 'password123'
  })
});
```

### 3. Test the Integration

#### Option A: Test with Frontend Team's Domain
1. Add their domain to `FRONTEND_URL` in your `.env`
2. Deploy your backend
3. Have them test API calls from their frontend

#### Option B: Test with Staging Environment
1. Deploy backend to staging environment
2. Set `ALLOW_ALL_ORIGINS=true` temporarily for testing
3. Have frontend team test against staging backend

### 4. Common Issues & Solutions

#### CORS Errors
```
Access to fetch at 'https://backend.com/login' from origin 'https://frontend.com' has been blocked by CORS policy
```

**Solution**: Make sure `FRONTEND_URL` is set correctly in your backend's environment variables.

#### Authentication Not Working
```
User appears logged in on frontend but backend returns 401
```

**Solution**: Frontend must use `credentials: 'include'` in all API calls.

#### Rate Limiting During Testing
```
{"error": "Too many requests. Please try again later."}
```

**Solution**: Use admin tool to clear rate limits:
```bash
./admin.sh clear-rate-limits
```

### 5. Production Deployment Checklist

- [ ] Set `FRONTEND_URL` to production frontend domain
- [ ] Remove or set `ALLOW_ALL_ORIGINS=false`
- [ ] Configure proper JWT secret
- [ ] Set up HTTPS for backend
- [ ] Test all API endpoints from production frontend
- [ ] Verify authentication flow works end-to-end

### 6. API Endpoints Summary

| Endpoint | Method | Purpose |
|----------|--------|---------|
| `/register` | POST | User registration |
| `/login` | POST | User login |
| `/verify-email` | GET | Email verification |
| `/forgot-password` | POST | Request password reset |
| `/reset-password` | POST | Reset password with token |
| `/dashboard-auth` | GET | Check authentication status |
| `/resend-verification` | POST | Resend verification email |

### 7. Frontend Team Requirements

The frontend team needs to:

1. **Use proper fetch configuration** with `credentials: 'include'`
2. **Handle validation errors** from the backend (structured error format)
3. **Implement proper error handling** for network issues and API errors
4. **Handle authentication state** using the `/dashboard-auth` endpoint
5. **Respect rate limiting** and show appropriate user feedback

### 8. Testing Commands

```bash
# Start backend for testing
go run main.go

# Clear rate limits if needed
./admin.sh clear-rate-limits

# Check database stats
./admin.sh db-stats

# Test API endpoint
curl -X POST http://localhost:8080/register \
  -H "Content-Type: application/json" \
  -d '{"username":"test","email":"<EMAIL>","password":"Test123!"}'
```

Your backend is now ready to work with any frontend hosted anywhere! 🚀
