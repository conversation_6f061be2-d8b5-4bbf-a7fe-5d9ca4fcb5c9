#!/bin/bash

echo "🧪 Testing Validation Error Messages"
echo "====================================="

# Start the server in the background
echo "Starting server..."
go run main.go &
SERVER_PID=$!

# Wait for server to start
sleep 3

echo ""
echo "1. Testing Invalid JSON Format:"
curl -s -X POST http://localhost:8080/register \
  -H "Content-Type: application/json" \
  -d '{"username": "test", "email": "<EMAIL>", "password"' | jq .

echo ""
echo "2. Testing Missing Required Fields:"
curl -s -X POST http://localhost:8080/register \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser", "email": "<EMAIL>"}' | jq .

echo ""
echo "3. Testing Invalid Email Format:"
curl -s -X POST http://localhost:8080/register \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser", "email": "invalid-email", "password": "ValidPassword123!"}' | jq .

echo ""
echo "4. Testing Weak Password:"
curl -s -X POST http://localhost:8080/register \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser", "email": "<EMAIL>", "password": "weak"}' | jq .

echo ""
echo "5. Testing Invalid Username:"
curl -s -X POST http://localhost:8080/register \
  -H "Content-Type: application/json" \
  -d '{"username": "ab", "email": "<EMAIL>", "password": "ValidPassword123!"}' | jq .

echo ""
echo "✅ All validation error tests completed!"

# Kill the server
kill $SERVER_PID
