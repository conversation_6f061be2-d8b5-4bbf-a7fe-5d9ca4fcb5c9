# Development Configuration
# IMPORTANT: Only set DEVELOPMENT=true for local development
# NEVER set this in production - it enables .env file loading
DEVELOPMENT=true

# Database Configuration
DB_HOST=localhost
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=stemblock
DB_PORT=5432
DB_SSLMODE=disable

# Server Configuration
PORT=8080
BASE_URL=https://your-backend-domain.com
FRONTEND_URL=https://your-frontend-domain.com

# CORS Configuration
# For development only - allows all origins (NOT for production)
# ALLOW_ALL_ORIGINS=true

# Cloud Hosting Configuration
# Set to true if using cloud hosting with HTTPS
FORCE_HTTPS=true
ENVIRONMENT=production

# Email Configuration
SMTP_HOST=sh-cp7.yyz2.servername.online
FROM_EMAIL=<EMAIL>
FROM_NAME=STEMBlock Support
SUPPORT_EMAIL_PASSWORD=your_email_password

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here

# Test Database Configuration (for running tests)
TEST_DB_HOST=localhost
TEST_DB_USER=your_test_db_user
TEST_DB_PASSWORD=your_test_db_password
TEST_DB_NAME=stemblock_test
TEST_DB_PORT=5432
