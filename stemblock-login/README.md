# STEMBlock Login System

A secure authentication system built with Go, Gin, and PostgreSQL.

## Features

- User registration with email verification
- Secure login with JWT tokens
- Password reset functionality
- Rate limiting for email operations
- Configurable via environment variables
- Comprehensive test suite

## Quick Start

1. **Clone and setup**:
   ```bash
   git clone <repository-url>
   cd stemblock-login
   ```

2. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your actual configuration values
   ```

   **Important**: The .env file is only loaded when `DEVELOPMENT=true` is set. This prevents accidental use in production.

3. **Set up database**:
   - Create a PostgreSQL database
   - Update DB_* variables in .env

4. **Run the application**:
   ```bash
   go run main.go
   ```

5. **Access the application**:
   - Web interface: http://localhost:8080
   - API documentation: http://localhost:8080/swagger/index.html

## Digital Ocean Deployment

Digital Ocean App Platform automatically detects Go applications and builds them using `go build`.

### **Quick Deployment Steps**

1. **Connect Repository**: Connect your GitHub repository to Digital Ocean App Platform

2. **Set Environment Variables** in the App Platform dashboard:
   ```bash
   # Required
   DB_USER=your_production_db_user
   DB_PASSWORD=your_production_db_password
   SUPPORT_EMAIL_PASSWORD=your_production_email_password

   # Recommended
   JWT_SECRET=your_production_jwt_secret
   BASE_URL=https://your-app-name.ondigitalocean.app
   FRONTEND_URL=https://your-frontend-domain.com
   ```

3. **Deploy**: App Platform automatically builds and deploys on every push

### **Admin Tools in Production**
Great news! Digital Ocean App Platform automatically builds ALL your admin tools:

**Development (with Go):**
```bash
./admin.sh list-users
./admin.sh clear-rate-limits
./admin.sh db-stats
```

**Production (Digital Ocean):**
```bash
# Admin tools are available as compiled binaries!
./admin.sh list-users        # Uses ./bin/admin automatically
./admin.sh clear-rate-limits # Full admin functionality available
./admin.sh db-stats          # Works in production
```

The `admin.sh` script automatically detects and uses the compiled `./bin/admin` binary in production.

### **Security Requirements**
- ❌ **DO NOT** set `DEVELOPMENT=true` in production
- ❌ **DO NOT** upload .env files to production servers
- ✅ **DO** set environment variables in the App Platform dashboard

## Configuration

All configuration is done via environment variables. See `.env.example` for all available options.

### Required Environment Variables

- `DB_USER`: Database username
- `DB_PASSWORD`: Database password
- `SUPPORT_EMAIL_PASSWORD`: Email server password
- `JWT_SECRET`: Secret key for JWT tokens (generate a secure random string)

## Development

### Running Tests

```bash
# Run all tests
go test ./...

# Run tests with database (requires TEST_DB_* environment variables)
export TEST_DB_PASSWORD=your_test_password
go test ./internal/user -v
```

### Building

```bash
# Build for local testing
go build .

# Run development server
go run main.go

# Run tests
go test ./...
```

### Generate Token (for testing)

```bash
go run cmd/generate-token/main.go [username]
```

### Admin CLI Tool (for testing & management)

The admin tool provides convenient commands for testing and database management:

```bash
# Development (using Go)
go run cmd/admin/main.go [command]

# Or use the wrapper script (works in both development and production)
./admin.sh [command]

# Show all available commands
./admin.sh

# Clear rate limits (useful when testing login)
./admin.sh clear-rate-limits
./admin.sh clear-rate-limit *************

# User management
./admin.sh list-users
./admin.sh create-<NAME_EMAIL> password123 admin
./admin.sh delete-user testuser
./admin.sh reset-password username newpassword

# Database statistics and cleanup
./admin.sh db-stats
./admin.sh cleanup 24

# Run example script
./scripts/admin-example.sh
```

**Note**: Admin tool requires the same environment variables as the main application. The `./admin.sh` wrapper automatically uses compiled binaries when available (production) or `go run` for development.

## Troubleshooting

### "Too Many Requests" Error During Testing

If you're getting rate limited while testing:

```bash
# Clear all rate limits
go run cmd/admin/main.go clear-rate-limits

# Or clear rate limits for your specific IP
go run cmd/admin/main.go clear-rate-limit YOUR_IP_ADDRESS

# Check current rate limit records
go run cmd/admin/main.go list-rate-limits
```

### Database Connection Issues

Make sure your `.env` file is properly configured:

```bash
cp .env.example .env
# Edit .env with your actual database credentials
```

## Project Structure

```
├── cmd/
│   ├── generate-token/    # Token generation utility
│   └── server/           # HTTP server setup
├── internal/
│   ├── auth/            # Authentication & JWT
│   ├── config/          # Configuration management
│   ├── db/              # Database initialization
│   ├── email/           # Email sending
│   ├── errors/          # Error handling & responses
│   ├── middleware/      # HTTP middleware (rate limiting, security)
│   ├── user/            # User management
│   └── validation/      # Input validation
└── api/                 # API documentation & client
```

## Security Features

- JWT tokens with configurable secrets
- Password hashing with bcrypt
- Email verification for new accounts
- Comprehensive rate limiting (login, registration, password reset)
- Input validation with detailed error messages
- CORS configuration for cross-origin requests
- Security headers (XSS protection, clickjacking prevention, etc.)
- SQL injection protection via GORM
- Environment-based configuration
- Structured logging for security monitoring

## Recent Improvements

### ✅ **Implemented Features**
- **🔧 Structured Logging**: JSON-formatted logs with context
- **🚦 Rate Limiting**: Protects against brute force attacks
- **✅ Input Validation**: Email, username, and password validation
- **🌐 CORS & Security Headers**: Production-ready security
- **🔄 Graceful Shutdown**: Proper server lifecycle management
- **🚨 Error Handling**: Consistent error responses and logging
- **📁 Better Architecture**: Organized middleware and error packages