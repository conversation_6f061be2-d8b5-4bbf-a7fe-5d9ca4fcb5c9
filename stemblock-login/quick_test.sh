#!/bin/bash

# Quick endpoint testing script
BASE_URL="http://localhost:8080"

echo "🧪 Quick API Endpoint Testing"
echo "=============================="

# Test function
test_api() {
    local name="$1"
    local method="$2"
    local endpoint="$3"
    local data="$4"
    
    echo -n "Testing $name... "
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "%{http_code}" -X "$method" -H "Content-Type: application/json" -d "$data" "$BASE_URL$endpoint")
    else
        response=$(curl -s -w "%{http_code}" -X "$method" "$BASE_URL$endpoint")
    fi
    
    # Extract status code (last 3 characters)
    status_code="${response: -3}"
    body="${response%???}"
    
    echo "HTTP $status_code"
    if [ -n "$body" ]; then
        echo "   Response: $body"
    fi
    echo ""
}

# Test all endpoints that frontend uses
echo "📋 Testing Frontend-Used Endpoints:"

test_api "HOME_PAGE" "GET" "/"

test_api "REGISTER" "POST" "/register" \
    '{"username":"testuser123","email":"<EMAIL>","password":"SecurePass123!"}'

test_api "LOGIN" "POST" "/login" \
    '{"username":"testuser123","password":"SecurePass123!"}'

test_api "FORGOT_PASSWORD" "POST" "/forgot-password" \
    '{"email":"<EMAIL>"}'

test_api "RESET_PASSWORD" "POST" "/reset-password" \
    '{"token":"invalid","password":"NewPass123!"}'

test_api "VERIFY_EMAIL" "GET" "/verify-email?token=invalid"

test_api "RESEND_VERIFICATION" "POST" "/resend-verification" \
    '{"email":"<EMAIL>","username":"testuser123","password":"SecurePass123!"}'

test_api "DASHBOARD_AUTH" "GET" "/dashboard-auth"

test_api "CONTACT" "POST" "/contact" \
    '{"name":"Test","email":"<EMAIL>","subject":"Test","message":"Test message"}'

test_api "LOGOUT" "POST" "/logout"

test_api "UPDATE_PROFILE" "POST" "/update-profile" \
    '{"firstName":"Test","lastName":"User"}'

test_api "UPDATE_SETTINGS" "POST" "/update-settings" \
    '{"setting":"value"}'

test_api "VALIDATE_RESET_TOKEN" "GET" "/validate-reset-token?token=invalid"

echo "📋 Testing Error Cases:"

test_api "REGISTER_WEAK_PASSWORD" "POST" "/register" \
    '{"username":"weak","email":"<EMAIL>","password":"123"}'

test_api "REGISTER_INVALID_EMAIL" "POST" "/register" \
    '{"username":"invalid","email":"invalid-email","password":"SecurePass123!"}'

test_api "REGISTER_MISSING_FIELDS" "POST" "/register" \
    '{"username":"missing"}'

test_api "LOGIN_WRONG_PASSWORD" "POST" "/login" \
    '{"username":"testuser123","password":"wrongpassword"}'

test_api "MALFORMED_JSON" "POST" "/register" \
    '{"username":"test","email":"<EMAIL>","password":'

echo "📋 Testing Rate Limiting:"
echo "Sending 6 rapid registration requests..."

for i in {1..6}; do
    echo -n "Request $i... "
    response=$(curl -s -w "%{http_code}" -X POST -H "Content-Type: application/json" \
        -d "{\"username\":\"rate$i\",\"email\":\"rate$<EMAIL>\",\"password\":\"SecurePass123!\"}" \
        "$BASE_URL/register")
    
    status_code="${response: -3}"
    body="${response%???}"
    
    echo "HTTP $status_code"
    if [[ "$body" == *"Too many requests"* ]]; then
        echo "   ✅ Rate limiting working!"
        break
    elif [ "$status_code" = "200" ]; then
        echo "   ✅ Registration successful"
    else
        echo "   Response: $body"
    fi
done

echo ""
echo "🏁 Testing completed!"
echo ""
echo "🔍 Issues Found:"
echo "1. /contact endpoint missing (404)"
echo "2. /logout endpoint missing (404)"  
echo "3. /update-profile endpoint missing (404)"
echo "4. /update-settings endpoint missing (404)"
echo "5. /validate-reset-token endpoint missing (404)"
echo ""
echo "✅ Working endpoints:"
echo "- / (home page)"
echo "- /register"
echo "- /login" 
echo "- /forgot-password"
echo "- /reset-password"
echo "- /verify-email"
echo "- /resend-verification"
echo "- /dashboard-auth"
