#!/bin/bash

# Quick script to clear rate limits for your current IP
# Useful when testing login and getting "too many requests"

# Load environment variables if .env exists
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

# Get your current IP address
MY_IP=$(curl -s ifconfig.me 2>/dev/null || curl -s ipinfo.io/ip 2>/dev/null || echo "127.0.0.1")

echo "🚦 Clearing rate limits for testing..."
echo "Your IP: $MY_IP"
echo

# Clear all rate limits (safest option)
echo "Clearing all rate limits..."
./admin.sh clear-rate-limits

echo
echo "✅ Rate limits cleared! You can now test login without rate limit issues."
echo
echo "💡 Other useful commands:"
echo "   ./admin.sh list-rate-limits     # Check current rate limits"
echo "   ./admin.sh db-stats             # See database statistics"
echo "   ./admin.sh list-users           # See all users"
