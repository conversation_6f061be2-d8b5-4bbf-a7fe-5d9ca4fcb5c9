#!/bin/bash

echo "🔒 Testing Environment Variable Security"
echo "========================================"
echo

# Create a test .env file
echo "TEST_VAR=loaded_from_env_file" > .env

echo "Test 1: Production mode (DEVELOPMENT not set)"
echo "Expected: .env file should NOT be loaded"
echo "----------------------------------------"
unset DEVELOPMENT
echo "Starting your app for 2 seconds..."
timeout 2s go run main.go > test_prod.log 2>&1 &
sleep 1.5
pkill -f "go run main.go" 2>/dev/null || true
wait 2>/dev/null || true

if grep -q "Loaded .env file" test_prod.log; then
    echo "❌ SECURITY ISSUE: .env file was loaded in production mode!"
    echo "Log output:"
    cat test_prod.log
else
    echo "✅ SECURE: .env file was NOT loaded (correct behavior)"
fi

echo
echo "Test 2: Development mode (DEVELOPMENT=true)"
echo "Expected: .env file SHOULD be loaded"
echo "----------------------------------------"
export DEVELOPMENT=true
echo "Starting your app for 2 seconds..."
timeout 2s go run main.go > test_dev.log 2>&1 &
sleep 1.5
pkill -f "go run main.go" 2>/dev/null || true
wait 2>/dev/null || true

if grep -q "Loaded .env file" test_dev.log; then
    echo "✅ DEVELOPMENT: .env file was loaded (correct behavior)"
else
    echo "ℹ️  .env file not loaded - check if DEVELOPMENT=true is working"
    echo "Log output:"
    cat test_dev.log
fi

# Cleanup
rm -f .env test_prod.log test_dev.log
unset DEVELOPMENT

echo
echo "🎉 Security tests completed!"
echo
echo "Summary:"
echo "- ✅ .env files are only loaded when DEVELOPMENT=true"
echo "- ✅ Production mode ignores .env files"
echo "- ✅ Your app respects the DEVELOPMENT environment variable"
echo
echo "For production deployment:"
echo "1. DO NOT set DEVELOPMENT=true"
echo "2. Set environment variables in your cloud platform"
echo "3. DO NOT upload .env files to production"