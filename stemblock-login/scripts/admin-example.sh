#!/bin/bash

# Example script showing how to use the admin CLI tool
# Make sure your .env file is configured with database credentials

echo "🛠️  STEMBlock Admin CLI Examples"
echo "================================"
echo

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please create it from .env.example first:"
    echo "   cp .env.example .env"
    echo "   # Edit .env with your database credentials"
    exit 1
fi

# Load environment variables
export $(cat .env | grep -v '^#' | xargs)

echo "📊 Database Statistics:"
./admin.sh db-stats
echo

echo "🚦 Rate Limit Records:"
./admin.sh list-rate-limits
echo

echo "👥 Recent Users:"
./admin.sh list-users 5
echo

echo "⏳ Pending Registrations:"
./admin.sh list-pending
echo

echo "💡 To clear rate limits for testing:"
echo "   ./admin.sh clear-rate-limits"
echo "   ./admin.sh clear-rate-limit YOUR_IP_ADDRESS"
echo
echo "💡 To manage users:"
echo "   ./admin.sh create-<NAME_EMAIL> password123"
echo "   ./admin.sh delete-user testuser"
echo "   ./admin.sh reset-password testuser newpassword"
