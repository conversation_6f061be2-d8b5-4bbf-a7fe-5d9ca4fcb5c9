openapi: 3.0.1
info:
  contact: {}
  description: API for login and registration system
  title: Login API
  version: 1.0.0
servers:
- url: /
paths:
  /dashboard:
    get:
      description: Returns personalized dashboard message. Requires valid JWT cookie.
      responses:
        "200":
          content:
            application/json:
              schema:
                additionalProperties:
                  type: string
                type: object
          description: Welcome message
        "401":
          content:
            application/json:
              schema:
                additionalProperties:
                  type: string
                type: object
          description: Missing or invalid token
      security:
      - ApiKeyAuth: []
      summary: Access user dashboard
      tags:
      - dashboard
  /login:
    post:
      description: Validates user credentials and returns a JWT token as a cookie.
      requestBody:
        content:
          application/json:
            schema:
              additionalProperties:
                type: string
              type: object
        description: Username and Password
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                additionalProperties:
                  type: string
                type: object
          description: Login successful
        "400":
          content:
            application/json:
              schema:
                additionalProperties:
                  type: string
                type: object
          description: Invalid input
        "401":
          content:
            application/json:
              schema:
                additionalProperties:
                  type: string
                type: object
          description: Invalid username or password
        "500":
          content:
            application/json:
              schema:
                additionalProperties:
                  type: string
                type: object
          description: Failed to generate token
      summary: Log in a user
      tags:
      - auth
      x-codegen-request-body-name: credentials
  /register:
    post:
      description: Creates a new user account.
      requestBody:
        content:
          application/json:
            schema:
              additionalProperties:
                type: string
              type: object
        description: Username and Password
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                additionalProperties:
                  type: string
                type: object
          description: User registered
        "400":
          content:
            application/json:
              schema:
                additionalProperties:
                  type: string
                type: object
          description: Invalid input
        "500":
          content:
            application/json:
              schema:
                additionalProperties:
                  type: string
                type: object
          description: Failed to hash password
      summary: Register a new user
      tags:
      - auth
      x-codegen-request-body-name: user
components:
  schemas: {}
x-original-swagger-version: "2.0"
