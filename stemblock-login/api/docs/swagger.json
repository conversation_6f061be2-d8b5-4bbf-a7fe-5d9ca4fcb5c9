{"swagger": "2.0", "info": {"contact": {}}, "paths": {"/dashboard": {"get": {"security": [{"ApiKeyAuth": []}], "description": "Returns personalized dashboard message. Requires valid JWT cookie.", "produces": ["application/json"], "tags": ["dashboard"], "summary": "Access user dashboard", "responses": {"200": {"description": "Welcome message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Missing or invalid token", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/login": {"post": {"description": "Validates user credentials and returns a JWT token as a cookie.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "Log in a user", "parameters": [{"description": "Username and Password", "name": "credentials", "in": "body", "required": true, "schema": {"type": "object", "additionalProperties": {"type": "string"}}}], "responses": {"200": {"description": "Login successful", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "Invalid input", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Invalid username or password", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "Failed to generate token", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/register": {"post": {"description": "Creates a new user account.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "Register a new user", "parameters": [{"description": "Username and Password", "name": "user", "in": "body", "required": true, "schema": {"type": "object", "additionalProperties": {"type": "string"}}}], "responses": {"200": {"description": "User registered", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "Invalid input", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "Failed to hash password", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}}}