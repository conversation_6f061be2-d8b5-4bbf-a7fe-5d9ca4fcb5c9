#!/bin/bash

# Production admin script for Digital Ocean deployment
# This script provides admin functionality when Go is not available on the server

echo "🔧 STEMBlock Admin Tool (Production Mode)"
echo "========================================="
echo

# Check if we have any arguments
if [ $# -eq 0 ]; then
    echo "Available admin commands:"
    echo ""
    echo "Database Management:"
    echo "  db-stats              - Show database statistics"
    echo "  list-users [limit]    - List users (default: 10)"
    echo "  list-pending          - List pending registrations"
    echo ""
    echo "User Management:"
    echo "  create-user <username> <email> <password> [role]"
    echo "  delete-user <username>"
    echo "  reset-password <username> <new_password>"
    echo ""
    echo "Rate Limiting:"
    echo "  list-rate-limits      - Show current rate limits"
    echo "  clear-rate-limits     - Clear all rate limits"
    echo "  clear-rate-limit <ip> - Clear rate limit for specific IP"
    echo ""
    echo "Maintenance:"
    echo "  cleanup <hours>       - Clean up old records (older than X hours)"
    echo ""
    echo "Usage: $0 <command> [arguments]"
    echo ""
    echo "⚠️  Note: This is production mode. Admin tools are not available"
    echo "   without Go runtime. Use the development environment for admin tasks."
    exit 0
fi

# Get the command
COMMAND=$1
shift

echo "⚠️  Production Admin Tool Limitation"
echo "===================================="
echo
echo "Command requested: $COMMAND"
echo "Arguments: $@"
echo
echo "❌ Admin tools are not available in production without Go runtime."
echo
echo "🔧 To run admin commands:"
echo "1. Use your development environment with Go installed"
echo "2. Connect to the same production database"
echo "3. Run: ./admin.sh $COMMAND $@"
echo
echo "💡 Alternative: Use database tools directly"
echo "   - Connect to your production database with psql or pgAdmin"
echo "   - Run SQL queries manually for database operations"
echo
echo "🔗 Database connection info:"
echo "   Host: \$DB_HOST"
echo "   Database: \$DB_NAME"
echo "   User: \$DB_USER"
echo
echo "📚 For common operations, see the documentation:"
echo "   - User management: Use the web interface"
echo "   - Rate limit clearing: Usually resolves automatically"
echo "   - Database stats: Use your database monitoring tools"
